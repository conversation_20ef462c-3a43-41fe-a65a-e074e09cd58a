# MCP Integration Guide

This guide provides instructions for integrating the Model Context Protocol (MCP) with the Hauling QR Trip Management System.

## Overview

The Model Context Protocol (MCP) allows for enhanced AI capabilities within the Hauling QR Trip Management System. By integrating MCP, the system can leverage advanced AI models for tasks such as:

- Anomaly detection in trip patterns
- Predictive maintenance scheduling
- Route optimization
- Driver behavior analysis
- Exception handling automation

## Kiro Agent Model Selection

The Kiro agent uses Claude models for AI assistance. The model can be configured in the Kiro user settings:

```json
"kiroAgent.agentModelSelection": "CLAUDE_SONNET_4_20250514_V1_0"
```

Available models include:
- `CLAUDE_SONNET_4_20250514_V1_0` (Latest Sonnet model, updated May 2025)
- `CLAUDE_3_7_SONNET_20250219_V1_0` (Previous Sonnet model, February 2025)

The model selection affects the AI capabilities, response quality, and performance of the Kiro agent when working with the Hauling QR Trip Management System.

## Configuration

### Workspace Level Configuration

To configure MCP at the workspace level, create or modify the `.kiro/settings/mcp.json` file:

```json
{
  "mcpServers": {
    "aws-docs": {
      "command": "uvx",
      "args": ["awslabs.aws-documentation-mcp-server@latest"],
      "env": {
        "FASTMCP_LOG_LEVEL": "ERROR"
      },
      "disabled": false,
      "autoApprove": []
    },
    "hauling-analytics": {
      "command": "uvx",
      "args": ["hauling-analytics-mcp-server@latest"],
      "env": {
        "FASTMCP_LOG_LEVEL": "INFO"
      },
      "disabled": false,
      "autoApprove": ["trip-pattern-analysis", "route-optimization"]
    }
  }
}
```

### User Level Configuration

For user-level configuration that applies across workspaces, modify the `~/.kiro/settings/mcp.json` file:

```json
{
  "mcpServers": {
    "personal-tools": {
      "command": "uvx",
      "args": ["personal-tools-mcp-server@latest"],
      "disabled": false,
      "autoApprove": ["code-formatter", "documentation-generator"]
    }
  }
}
```

## Installation

### Prerequisites

1. Install `uv` and `uvx` following the guide at https://docs.astral.sh/uv/getting-started/installation/

### Server Setup

MCP servers will be automatically downloaded and run by the `uvx` command when configured. No manual installation of individual servers is required.

## Usage

### In Development Environment

1. Open the command palette and search for "MCP"
2. Select "MCP: Connect to Server" to connect to a specific server
3. Use MCP tools through the Kiro interface

### In Production Environment

For production deployments, MCP integration is handled by the deployment script. The automated deployment script for Ubuntu 24.04 (`deploy-hauling-qr-ubuntu.sh`) includes options for configuring MCP servers.

To enable MCP in production:

1. Add MCP configuration to your deployment config file:
   ```bash
   # Add to deployment-config.conf
   MCP_ENABLED=true
   MCP_SERVERS=aws-docs,hauling-analytics
   ```

2. Run the deployment script with your configuration:
   ```bash
   ./deploy-hauling-qr-ubuntu.sh --config deployment-config.conf
   ```

## Troubleshooting

If you encounter issues with MCP integration:

1. Check the MCP server status in the Kiro feature panel
2. Verify that `uv` and `uvx` are installed correctly
3. Check the server logs for error messages
4. Try reconnecting to the server from the MCP Server view

### Kiro Agent Configuration Issues

If you experience issues with the Kiro agent's performance or capabilities:

1. Check your Kiro agent model selection in the settings:
   ```
   "kiroAgent.agentModelSelection": "CLAUDE_SONNET_4_20250514_V1_0"
   ```
2. Ensure you're using the latest available model for optimal performance
3. If experiencing memory issues, try restarting the Kiro agent
4. For complex MCP operations, the latest Claude model (Sonnet 4) provides improved performance

## Security Considerations

When using MCP in production:

1. Only enable necessary servers
2. Use the `autoApprove` list to limit which tools can run without approval
3. Regularly review the MCP server logs for suspicious activity
4. Keep `uvx` and MCP servers updated to the latest versions