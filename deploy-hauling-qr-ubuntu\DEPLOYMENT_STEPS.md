# Step-by-Step Deployment Guide

This guide provides a clear, sequential process for deploying the Hauling QR Trip Management System on a fresh Ubuntu VPS (18.04+, optimized for 24.04).

> **Note**: This is part of a comprehensive deployment system with advanced features including idempotency, rollback capabilities, and CI/CD integration. For complete deployment documentation, see the [Auto Deployment Ubuntu Guide](../docs/AUTO_DEPLOYMENT_UBUNTU_GUIDE.md).

## 🚀 Deployment Process

### Step 1: Initial Server Setup

#### Option A: Using Root (Simple, for Development)
```bash
# Login as root
ssh root@your-server-ip
```

#### Option B: Using Regular User (Recommended for Production)
```bash
# Login as root first
ssh root@your-server-ip

# Create a new user
adduser deployer

# Add user to sudo group
usermod -aG sudo deployer

# Switch to the new user
su - deployer
# OR log out and log back in as deployer
ssh deployer@your-server-ip
```

### Step 2: Install Essential Tools

```bash
# Update package lists
sudo apt update

# Install essential tools
sudo apt install -y curl nano
# Create directory for deployment
mkdir -p ~/hauling-deployment
cd ~/hauling-deployment
```

### Step 3: Download the Deployment Files

> **Important**: This repository is **private** and requires authentication with a GitHub token.

#### Option A: Download Script with Token (Recommended)

```bash
# Download the download script with authentication
curl -H "Authorization: token *********************************************************************************************" \
     -H "Accept: application/vnd.github.v3.raw" \
     -o download-deployment.sh \
     "https://api.github.com/repos/mightybadz18/hauling-qr-trip-management/contents/deploy-hauling-qr-ubuntu/download-deployment.sh?ref=main"

# Make it executable
chmod +x download-deployment.sh

# Run the download script (token is built-in)
./download-deployment.sh

# When prompted, choose option 1 to download deployment scripts only
# Files will be downloaded directly to your current directory
```

#### Option B: Manual File Downloads

```bash
# Set token variable for easier use
TOKEN="*********************************************************************************************"

# Download main deployment script
curl -H "Authorization: token $TOKEN" \
     -H "Accept: application/vnd.github.v3.raw" \
     -o deploy-hauling-qr-ubuntu-fixed.sh \
     "https://api.github.com/repos/mightybadz18/hauling-qr-trip-management/contents/deploy-hauling-qr-ubuntu/deploy-hauling-qr-ubuntu-fixed.sh?ref=main"

# Download configuration file
curl -H "Authorization: token $TOKEN" \
     -H "Accept: application/vnd.github.v3.raw" \
     -o deployment-config.conf \
     "https://api.github.com/repos/mightybadz18/hauling-qr-trip-management/contents/deploy-hauling-qr-ubuntu/deployment-config.conf?ref=main"

# Download database fix script
curl -H "Authorization: token $TOKEN" \
     -H "Accept: application/vnd.github.v3.raw" \
     -o fix-database-script.sh \
     "https://api.github.com/repos/mightybadz18/hauling-qr-trip-management/contents/fix-database-script.sh?ref=main"

# Make scripts executable
chmod +x deploy-hauling-qr-ubuntu-fixed.sh
chmod +x fix-database-script.sh
```

#### Option C: Clone Repository with Token

```bash
# Clone the entire repository with authentication
git clone https://<EMAIL>/mightybadz18/hauling-qr-trip-management.git

# Navigate to deployment files
cd hauling-qr-trip-management/deploy-hauling-qr-ubuntu/

# Copy files to your working directory
cp deploy-hauling-qr-ubuntu-fixed.sh ../
cp deployment-config.conf ../
cp ../fix-database-script.sh ../

# Make executable
chmod +x ../deploy-hauling-qr-ubuntu-fixed.sh
chmod +x ../fix-database-script.sh

# Go back to working directory
cd ..
```

#### What's New in the Updated Download

The download script now includes these **enhanced files**:

**🔧 Troubleshooting Guides**
- `POST_DEPLOYMENT_TROUBLESHOOTING.md` - Complete troubleshooting guide
- `POSTGRESQL_TABLE_INSPECTION_GUIDE.md` - Database inspection methods
- `QUICK_FIXES.md` - Emergency fix reference card

**🗄️ Database Fixes**
- `DATABASE_DEPLOYMENT_FIXES.md` - Database fix documentation
- `fix-database-script.sh` - Quick fix script for database issues

**📊 Updated Core Files**
- `deploy-hauling-qr-ubuntu-fixed.sh` - Enhanced with database fixes
- `DOWNLOAD_UPDATED_FILES.md` - Guide for getting updated files

### Step 4: Configure Deployment

```bash
# Review and edit the deployment configuration
nano deployment-config.conf

# Key settings to configure:
# Hauling QR Trip Management System Deployment Configuration
# This file contains your deployment settings

# Domain Configuration
DOMAIN_NAME="truckhaul.top"          # CHANGE THIS: Your domain name
ENV_MODE="production"                # production or development
SSL_MODE="cloudflare"                # cloudflare or letsencrypt

# Admin Configuration
ADMIN_USERNAME="admin"               # Default admin username
ADMIN_PASSWORD="admin12345"          # CHANGE THIS: Strong password required
ADMIN_EMAIL="<EMAIL>"    # CHANGE THIS: Your email address

# Database Configuration
DB_PASSWORD="PostgreSQLPassword123"  # CHANGE THIS: Strong database password

# GitHub Repository (with Personal Access Token)
REPO_URL="https://<EMAIL>/mightybadz18/hauling-qr-trip-management.git"
REPO_BRANCH="main"

# System Configuration
MONITORING_ENABLED=true              # Enable system monitoring
BACKUP_ENABLED=true                  # Enable automatic backups
BACKUP_RETENTION_DAYS=7              # Keep backups for 7 days

# Security Note: Keep this file secure and never commit it to version control
# Recommended: chmod 600 deployment-config.conf


**Important**: This deployment configuration uses a two-stage approach:
1. `deployment-config.conf` - Used during deployment setup
2. Application `.env` - Generated automatically from deployment config

See `CONFIGURATION_GUIDE.md` for detailed information about this configuration approach.

# Key settings to modify:
# - DOMAIN_NAME: Update to your domain name
# - ADMIN_EMAIL: Update to your email address
# - ADMIN_PASSWORD: Change from default (use strong password)
# - DB_PASSWORD: Change from default (use strong password)
# - SSL_MODE: "cloudflare" for Cloudflare SSL, "letsencrypt" for Let's Encrypt
# - ENV_MODE: "production" for live deployment, "development" for testing
```

### Step 5: Run Deployment

```bash
# Run the deployment with sudo
sudo ./run-deployment.sh

# Follow the interactive prompts
# Choose option 2 for full deployment when prompted
```

### Step 6: Post-Deployment

```bash
# Change default passwords
# Access the application at https://your-domain.com
# Configure Cloudflare SSL settings if using Cloudflare
```

## 🔧 Management Commands

After deployment, you can manage the application with:

```bash
# Server management
/var/www/hauling-qr-system/manage-server.sh {start|stop|restart|status|logs|health|config}

# Database management
/var/www/hauling-qr-system/manage-database.sh {connect|backup|test}
```

## 🔍 Troubleshooting

If you encounter issues:

```bash
# Check deployment logs
tail -f /var/log/hauling-deployment/deployment.log

# Check application logs
/var/www/hauling-qr-system/manage-server.sh logs

# Run deployment with debug logging
sudo ./deploy-hauling-qr-ubuntu-fixed.sh --config deployment-config.conf --log-level debug

# Check configuration sync
/var/www/hauling-qr-system/manage-server.sh config
```

**Configuration Issues**: If you encounter configuration-related problems, refer to `CONFIGURATION_GUIDE.md` for detailed information about the two-stage configuration approach.

## 📋 Security Checklist

After deployment:

1. Change default admin password
2. Change default database password
3. Configure firewall (ufw)
4. Set up fail2ban
5. Enable automatic security updates
6. Disable root SSH access
7. Use key-based authentication