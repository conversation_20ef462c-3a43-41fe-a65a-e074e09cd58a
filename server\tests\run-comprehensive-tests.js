#!/usr/bin/env node

/**
 * Comprehensive Test Runner for Shift Assignment Trip Status Fix
 * 
 * This script runs all tests related to the shift assignment and trip status fix,
 * including overnight scenarios and status protection tests.
 */

const { execSync } = require('child_process');
const path = require('path');

console.log('🚀 Starting Comprehensive Test Suite for Shift Assignment Trip Status Fix');
console.log('=' .repeat(80));

const testFiles = [
  'captureActiveDriverInfo.test.js',
  'trip-logs-field-population.test.js', 
  'end-to-end-shift-trip-flow.test.js'
];

const testResults = {
  passed: 0,
  failed: 0,
  total: testFiles.length
};

for (const testFile of testFiles) {
  console.log(`\n📋 Running: ${testFile}`);
  console.log('-'.repeat(50));
  
  try {
    const testPath = path.join(__dirname, testFile);
    const command = `npx jest "${testPath}" --verbose --detectOpenHandles --forceExit`;
    
    execSync(command, { 
      stdio: 'inherit',
      cwd: path.join(__dirname, '..')
    });
    
    console.log(`✅ ${testFile} - PASSED`);
    testResults.passed++;
    
  } catch (error) {
    console.log(`❌ ${testFile} - FAILED`);
    console.error(`Error: ${error.message}`);
    testResults.failed++;
  }
}

console.log('\n' + '='.repeat(80));
console.log('📊 TEST SUMMARY');
console.log('='.repeat(80));
console.log(`Total Tests: ${testResults.total}`);
console.log(`Passed: ${testResults.passed}`);
console.log(`Failed: ${testResults.failed}`);

if (testResults.failed === 0) {
  console.log('🎉 ALL TESTS PASSED! The shift assignment trip status fix is working correctly.');
  console.log('\n✅ Key Features Validated:');
  console.log('   • Overnight shift scenarios work correctly');
  console.log('   • QR-created shifts are protected from automated status changes');
  console.log('   • Trip logs are populated with complete driver information');
  console.log('   • Location sequence tracking is functional');
  console.log('   • End-to-end data flow is consistent');
  console.log('   • Performance impact is minimal');
  process.exit(0);
} else {
  console.log('⚠️  Some tests failed. Please review the output above and fix the issues.');
  process.exit(1);
}