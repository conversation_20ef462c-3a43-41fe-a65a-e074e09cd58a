// PWA Offline Functionality Test Script - Enhanced for Chunk Loading
// Run this in browser console to test offline mode

console.log('🧪 PWA Offline Functionality Test Starting...');
console.log('🔧 Enhanced test suite for JavaScript chunk loading and offline sync');

// Test for infinite refresh loop detection
let pageLoadCount = 0;
let lastLoadTime = Date.now();

window.addEventListener('beforeunload', () => {
  pageLoadCount++;
  const timeSinceLastLoad = Date.now() - lastLoadTime;
  if (timeSinceLastLoad < 1000 && pageLoadCount > 3) {
    console.error('🚨 INFINITE REFRESH LOOP DETECTED! Page reloading too frequently');
  }
  lastLoadTime = Date.now();
});

// Monitor React re-renders
let renderCount = 0;
const originalConsoleLog = console.log;
console.log = function(...args) {
  if (args[0] && typeof args[0] === 'string' && args[0].includes('render')) {
    renderCount++;
    if (renderCount > 10) {
      console.error('🚨 EXCESSIVE RE-RENDERS DETECTED!', renderCount);
    }
  }
  originalConsoleLog.apply(console, arguments);
};

// Monitor IndexedDB calls during online mode
let indexedDBCallCount = 0;
const originalIndexedDBOpen = indexedDB.open;
indexedDB.open = function(...args) {
  indexedDBCallCount++;
  if (navigator.onLine && indexedDBCallCount > 5) {
    console.warn('⚠️ EXCESSIVE INDEXEDDB CALLS WHILE ONLINE:', indexedDBCallCount);
    console.warn('   This may indicate offline functions running unnecessarily');
  }
  return originalIndexedDBOpen.apply(indexedDB, arguments);
};

// Test 1: Check if PWA services are available
async function testPWAServicesAvailable() {
  console.log('\n📋 Test 1: PWA Services Availability');
  
  try {
    // Check if services are imported and available
    const services = {
      tripScannerOffline: window.tripScannerOffline,
      driverConnectOffline: window.driverConnectOffline,
      offlineDB: window.offlineDB,
      backgroundSync: window.backgroundSync
    };
    
    console.log('Available services:', Object.keys(services).filter(key => services[key]));
    
    // If services aren't globally available, try importing them
    if (!services.tripScannerOffline) {
      console.log('⚠️ Services not globally available - this is normal in React apps');
      console.log('✅ Services are properly encapsulated in components');
      return true;
    }
    
    return true;
  } catch (error) {
    console.error('❌ PWA Services test failed:', error);
    return false;
  }
}

// Test 2: Check IndexedDB availability
async function testIndexedDBAvailability() {
  console.log('\n📋 Test 2: IndexedDB Availability');
  
  try {
    if (!window.indexedDB) {
      throw new Error('IndexedDB not supported');
    }
    
    console.log('✅ IndexedDB is available');
    
    // Test database creation
    const dbName = 'PWA_Test_' + Date.now();
    const request = indexedDB.open(dbName, 1);
    
    return new Promise((resolve, reject) => {
      request.onerror = () => {
        console.error('❌ IndexedDB test failed:', request.error);
        reject(false);
      };
      
      request.onsuccess = () => {
        console.log('✅ IndexedDB test database created successfully');
        request.result.close();
        indexedDB.deleteDatabase(dbName);
        resolve(true);
      };
      
      request.onupgradeneeded = (event) => {
        const db = event.target.result;
        db.createObjectStore('testStore', { keyPath: 'id', autoIncrement: true });
      };
    });
  } catch (error) {
    console.error('❌ IndexedDB availability test failed:', error);
    return false;
  }
}

// Test 3: Check Service Worker registration
async function testServiceWorkerRegistration() {
  console.log('\n📋 Test 3: Service Worker Registration');
  
  try {
    if (!('serviceWorker' in navigator)) {
      console.log('⚠️ Service Worker not supported in this browser');
      return false;
    }
    
    const registration = await navigator.serviceWorker.getRegistration();
    if (registration) {
      console.log('✅ Service Worker is registered:', registration.scope);
      console.log('   State:', registration.active?.state || 'inactive');
      return true;
    } else {
      console.log('⚠️ Service Worker not registered yet');
      return false;
    }
  } catch (error) {
    console.error('❌ Service Worker test failed:', error);
    return false;
  }
}

// Test 4: Check PWA installation capability
async function testPWAInstallability() {
  console.log('\n📋 Test 4: PWA Installation Capability');
  
  try {
    // Check if app is already installed
    if (window.matchMedia && window.matchMedia('(display-mode: standalone)').matches) {
      console.log('✅ PWA is already installed and running in standalone mode');
      return true;
    }
    
    // Check for beforeinstallprompt event (stored)
    if (window.deferredPrompt) {
      console.log('✅ PWA installation prompt is available');
      return true;
    }
    
    console.log('⚠️ PWA installation prompt not currently available');
    console.log('   This is normal - prompt appears based on engagement heuristics');
    return true;
  } catch (error) {
    console.error('❌ PWA installability test failed:', error);
    return false;
  }
}

// Test 5: Simulate offline mode and scan processing
async function testOfflineMode() {
  console.log('\n📋 Test 5: Offline Mode Simulation & Scan Processing');

  try {
    // Check current online status
    console.log('Current online status:', navigator.onLine);

    // Test offline detection
    const originalOnLine = navigator.onLine;

    // Simulate going offline
    console.log('🔌 Simulating offline mode...');

    // Create a mock fetch that fails (simulating offline)
    const originalFetch = window.fetch;
    window.fetch = () => Promise.reject(new Error('Network request failed (simulated offline)'));

    // Test if offline handling works
    try {
      await fetch('/api/test');
      console.log('⚠️ Fetch should have failed in offline simulation');
    } catch (error) {
      console.log('✅ Offline simulation working - fetch properly failed');
    }

    // Test offline scan storage if available
    if (window.tripScannerOffline) {
      console.log('🧪 Testing offline scan storage...');
      try {
        const testScanData = {
          location_qr_data: { type: 'location', id: 'test-location', name: 'Test Location' },
          truck_qr_data: { type: 'truck', id: 'test-truck', name: 'Test Truck' },
          timestamp: new Date().toISOString(),
          scanner_type: 'trip_scanner'
        };

        const result = await window.tripScannerOffline.storeScan(testScanData);
        if (result.success) {
          console.log('✅ Offline scan storage working:', result.message);
        } else {
          console.log('⚠️ Offline scan storage failed:', result.message);
        }
      } catch (scanError) {
        console.log('⚠️ Offline scan test error:', scanError.message);
      }
    } else {
      console.log('⚠️ tripScannerOffline service not globally available (normal in React)');
    }

    // Restore original fetch
    window.fetch = originalFetch;

    console.log('✅ Offline mode simulation completed');
    return true;
  } catch (error) {
    console.error('❌ Offline mode test failed:', error);
    return false;
  }
}

// Test 6: Check for infinite refresh loops and excessive re-renders
async function testPageStability() {
  console.log('\n📋 Test 6: Page Stability & Re-render Detection');

  try {
    console.log('Page load count:', pageLoadCount);
    console.log('Render count:', renderCount);
    console.log('IndexedDB call count:', indexedDBCallCount);
    console.log('Current online status:', navigator.onLine);

    // Check for excessive re-renders
    if (renderCount > 20) {
      console.error('❌ EXCESSIVE RE-RENDERS DETECTED:', renderCount);
      console.error('   This indicates a React infinite re-render loop');
      return false;
    } else if (renderCount > 10) {
      console.warn('⚠️ High render count detected:', renderCount);
      console.warn('   Monitor for potential performance issues');
    } else {
      console.log('✅ Render count is normal:', renderCount);
    }

    // Check for page refresh loops
    if (pageLoadCount > 3) {
      console.error('❌ EXCESSIVE PAGE REFRESHES DETECTED:', pageLoadCount);
      console.error('   This indicates an infinite refresh loop');
      return false;
    } else {
      console.log('✅ Page refresh count is normal:', pageLoadCount);
    }

    // Check for excessive IndexedDB calls while online
    if (navigator.onLine && indexedDBCallCount > 10) {
      console.error('❌ EXCESSIVE INDEXEDDB CALLS WHILE ONLINE:', indexedDBCallCount);
      console.error('   Offline functions should not run continuously when online');
      return false;
    } else if (navigator.onLine && indexedDBCallCount > 5) {
      console.warn('⚠️ High IndexedDB call count while online:', indexedDBCallCount);
      console.warn('   Monitor for unnecessary offline function calls');
    } else {
      console.log('✅ IndexedDB call count is appropriate for current mode');
    }

    return true;
  } catch (error) {
    console.error('❌ Page stability test failed:', error);
    return false;
  }
}

// Test 7: Check authentication bypass for trip-scanner
async function testTripScannerAuthBypass() {
  console.log('\n📋 Test 7: Trip Scanner Authentication Bypass');

  try {
    const currentPath = window.location.pathname;
    console.log('Current path:', currentPath);

    if (currentPath === '/trip-scanner') {
      console.log('✅ Successfully accessed /trip-scanner without authentication');
      console.log('✅ Authentication bypass is working correctly');
      return true;
    } else {
      console.log('⚠️ Not currently on trip-scanner page');
      console.log('   Navigate to /trip-scanner to test authentication bypass');
      return true;
    }
  } catch (error) {
    console.error('❌ Trip scanner auth bypass test failed:', error);
    return false;
  }
}

// Test 8: JavaScript chunk caching and loading
async function testJavaScriptChunkCaching() {
  console.log('\n📦 Test 8: JavaScript Chunk Caching');

  try {
    // Check if asset manifest is available
    const manifestResponse = await fetch('/asset-manifest.json');
    if (!manifestResponse.ok) {
      console.error('❌ Asset manifest not available');
      return false;
    }

    const manifest = await manifestResponse.json();
    console.log('📋 Asset manifest loaded:', Object.keys(manifest.files).length, 'files');

    // Test caching of JavaScript chunks
    const jsFiles = Object.values(manifest.files).filter(file => file.endsWith('.js'));
    console.log('🔍 Found', jsFiles.length, 'JavaScript files to test');

    let cachedCount = 0;
    let failedCount = 0;

    for (const jsFile of jsFiles.slice(0, 5)) { // Test first 5 chunks
      try {
        const response = await fetch(jsFile);
        if (response.ok) {
          cachedCount++;
          console.log('✅ Chunk accessible:', jsFile);
        } else {
          failedCount++;
          console.warn('⚠️ Chunk failed:', jsFile, response.status);
        }
      } catch (error) {
        failedCount++;
        console.error('❌ Chunk error:', jsFile, error.message);
      }
    }

    console.log(`📊 Chunk test results: ${cachedCount} accessible, ${failedCount} failed`);
    return failedCount === 0;

  } catch (error) {
    console.error('❌ JavaScript chunk caching test failed:', error);
    return false;
  }
}

// Test 9: Offline fallback page
async function testOfflineFallback() {
  console.log('\n🚫 Test 9: Offline Fallback Page');

  try {
    const response = await fetch('/offline-fallback.html');
    if (response.ok) {
      console.log('✅ Offline fallback page is available');
      const content = await response.text();
      if (content.includes('App Temporarily Unavailable')) {
        console.log('✅ Offline fallback content is correct');
        return true;
      } else {
        console.error('❌ Offline fallback content is incorrect');
        return false;
      }
    } else {
      console.error('❌ Offline fallback page not available:', response.status);
      return false;
    }
  } catch (error) {
    console.error('❌ Offline fallback test failed:', error);
    return false;
  }
}

// Test 10: Background sync functionality
async function testBackgroundSync() {
  console.log('\n🔄 Test 10: Background Sync Functionality');

  try {
    // Check if background sync is supported
    if (!('serviceWorker' in navigator) || !('sync' in window.ServiceWorkerRegistration.prototype)) {
      console.warn('⚠️ Background sync not supported in this browser');
      return false;
    }

    // Test service worker registration
    const registration = await navigator.serviceWorker.ready;
    console.log('✅ Service worker is ready');

    // Test sync registration
    await registration.sync.register('test-sync');
    console.log('✅ Background sync registration successful');

    return true;

  } catch (error) {
    console.error('❌ Background sync test failed:', error);
    return false;
  }
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Starting PWA Offline Functionality Tests...\n');
  
  const results = {
    pwaSevicesAvailable: await testPWAServicesAvailable(),
    indexedDBAvailability: await testIndexedDBAvailability(),
    serviceWorkerRegistration: await testServiceWorkerRegistration(),
    pwaInstallability: await testPWAInstallability(),
    offlineMode: await testOfflineMode(),
    pageStability: await testPageStability(),
    tripScannerAuthBypass: await testTripScannerAuthBypass(),
    javaScriptChunkCaching: await testJavaScriptChunkCaching(),
    offlineFallback: await testOfflineFallback(),
    backgroundSync: await testBackgroundSync()
  };
  
  console.log('\n📊 Test Results Summary:');
  console.log('========================');
  Object.entries(results).forEach(([test, passed]) => {
    console.log(`${passed ? '✅' : '❌'} ${test}: ${passed ? 'PASSED' : 'FAILED'}`);
  });
  
  const passedCount = Object.values(results).filter(Boolean).length;
  const totalCount = Object.keys(results).length;
  
  console.log(`\n🎯 Overall: ${passedCount}/${totalCount} tests passed`);
  
  if (passedCount === totalCount) {
    console.log('🎉 All PWA offline functionality tests PASSED!');
  } else {
    console.log('⚠️ Some tests failed - check individual results above');
  }
  
  return results;
}

// Auto-run tests when script is loaded
runAllTests().catch(error => {
  console.error('❌ Test suite failed to run:', error);
});

// Make functions available globally for manual testing
window.PWATests = {
  runAllTests,
  testPWAServicesAvailable,
  testIndexedDBAvailability,
  testServiceWorkerRegistration,
  testPWAInstallability,
  testOfflineMode,
  testPageStability,
  testTripScannerAuthBypass,
  testJavaScriptChunkCaching,
  testOfflineFallback,
  testBackgroundSync
};

console.log('\n💡 Manual testing available via window.PWATests object');
console.log('   Example: PWATests.testOfflineMode()');
