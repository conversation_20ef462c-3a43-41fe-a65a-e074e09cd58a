{"name": "Remove Debug and Console Logs", "description": "Automatically removes debug logs and console.log statements from JavaScript/TypeScript files to clean up code and reduce console output", "trigger": {"type": "manual", "label": "Clean Debug Logs"}, "filePatterns": ["**/*.js", "**/*.jsx", "**/*.ts", "**/*.tsx"], "excludePatterns": ["**/node_modules/**", "**/build/**", "**/dist/**", "**/*.min.js"], "prompt": "Remove all debug logs, console.log, console.debug, console.info, console.warn statements from the code files. Keep console.error statements for error handling. Also remove any debug-related comments or temporary logging code. Make sure to preserve the functionality of the code while cleaning up the logging statements. Focus on:\n\n1. Remove console.log() statements\n2. Remove console.debug() statements  \n3. Remove console.info() statements\n4. Remove console.warn() statements\n5. Remove debug-related comments like // DEBUG:, // TODO: remove this log, etc.\n6. Remove temporary logging variables that are only used for debugging\n7. Keep console.error() for proper error handling\n8. Preserve any logging that's part of the actual application functionality\n\nProcess all JavaScript and TypeScript files in the codebase systematically."}