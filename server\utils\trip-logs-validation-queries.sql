-- Trip Logs Field Population Validation Queries
-- These queries can be run directly against the database to validate trip_logs completeness

-- 1. Driver Information Completeness Check
-- Validates that all trip_logs entries have complete driver information (Requirement 5.1)
SELECT 
  'Driver Information Completeness' as validation_type,
  COUNT(*) as total_trips,
  COUNT(CASE WHEN performed_by_driver_id IS NOT NULL 
             AND performed_by_driver_name IS NOT NULL 
             AND performed_by_employee_id IS NOT NULL 
             AND performed_by_shift_id IS NOT NULL 
             AND performed_by_shift_type IS NOT NULL 
        THEN 1 END) as complete_driver_info,
  COUNT(CASE WHEN performed_by_driver_id IS NULL 
             OR performed_by_driver_name IS NULL 
             OR performed_by_employee_id IS NULL 
             OR performed_by_shift_id IS NULL 
             OR performed_by_shift_type IS NULL 
        THEN 1 END) as incomplete_driver_info,
  ROUND(
    COUNT(CASE WHEN performed_by_driver_id IS NOT NULL 
               AND performed_by_driver_name IS NOT NULL 
               AND performed_by_employee_id IS NOT NULL 
               AND performed_by_shift_id IS NOT NULL 
               AND performed_by_shift_type IS NOT NULL 
          THEN 1 END) * 100.0 / COUNT(*), 2
  ) as success_rate_percent
FROM trip_logs 
WHERE created_at >= NOW() - INTERVAL '24 hours';

-- 2. Notes Field Quality Check
-- Validates that notes field contains meaningful contextual information (Requirement 5.2)
SELECT 
  'Notes Field Quality' as validation_type,
  COUNT(*) as total_trips,
  COUNT(CASE WHEN notes IS NOT NULL 
             AND jsonb_typeof(notes) IN ('string', 'object')
             AND LENGTH(notes::text) >= 10
        THEN 1 END) as valid_notes,
  COUNT(CASE WHEN notes IS NULL 
             OR jsonb_typeof(notes) NOT IN ('string', 'object')
             OR LENGTH(notes::text) < 10
        THEN 1 END) as invalid_notes,
  ROUND(
    COUNT(CASE WHEN notes IS NOT NULL 
               AND jsonb_typeof(notes) IN ('string', 'object')
               AND LENGTH(notes::text) >= 10
          THEN 1 END) * 100.0 / COUNT(*), 2
  ) as success_rate_percent,
  ROUND(AVG(CASE WHEN notes IS NOT NULL THEN LENGTH(notes::text) END), 0) as avg_notes_length
FROM trip_logs 
WHERE created_at >= NOW() - INTERVAL '24 hours';

-- 3. Location Sequence Accuracy Check
-- Validates that location_sequence is correctly calculated for different workflow types (Requirement 5.3)
SELECT 
  'Location Sequence Accuracy' as validation_type,
  workflow_type,
  COUNT(*) as total_trips,
  COUNT(CASE WHEN location_sequence IS NOT NULL 
             AND (
               (workflow_type = 'standard' AND jsonb_typeof(location_sequence) = 'number' AND (location_sequence::text)::int BETWEEN 1 AND 2) OR
               (workflow_type = 'extended' AND jsonb_typeof(location_sequence) = 'number' AND (location_sequence::text)::int BETWEEN 1 AND 3) OR
               (workflow_type = 'cycle' AND jsonb_typeof(location_sequence) = 'number' AND (location_sequence::text)::int >= 1) OR
               (workflow_type = 'dynamic' AND location_sequence IS NOT NULL)
             )
        THEN 1 END) as valid_sequences,
  COUNT(CASE WHEN location_sequence IS NULL 
             OR NOT (
               (workflow_type = 'standard' AND jsonb_typeof(location_sequence) = 'number' AND (location_sequence::text)::int BETWEEN 1 AND 2) OR
               (workflow_type = 'extended' AND jsonb_typeof(location_sequence) = 'number' AND (location_sequence::text)::int BETWEEN 1 AND 3) OR
               (workflow_type = 'cycle' AND jsonb_typeof(location_sequence) = 'number' AND (location_sequence::text)::int >= 1) OR
               (workflow_type = 'dynamic' AND location_sequence IS NOT NULL)
             )
        THEN 1 END) as invalid_sequences,
  ROUND(
    COUNT(CASE WHEN location_sequence IS NOT NULL 
               AND (
                 (workflow_type = 'standard' AND jsonb_typeof(location_sequence) = 'number' AND (location_sequence::text)::int BETWEEN 1 AND 2) OR
                 (workflow_type = 'extended' AND jsonb_typeof(location_sequence) = 'number' AND (location_sequence::text)::int BETWEEN 1 AND 3) OR
                 (workflow_type = 'cycle' AND jsonb_typeof(location_sequence) = 'number' AND (location_sequence::text)::int >= 1) OR
                 (workflow_type = 'dynamic' AND location_sequence IS NOT NULL)
               )
          THEN 1 END) * 100.0 / COUNT(*), 2
  ) as success_rate_percent
FROM trip_logs 
WHERE created_at >= NOW() - INTERVAL '24 hours'
GROUP BY workflow_type
ORDER BY workflow_type;

-- 4. Missing Required Fields Detection
-- Automated checks to detect trip_logs entries with missing required fields (Requirement 5.4)
SELECT 
  'Missing Required Fields' as validation_type,
  'assignment_id' as field_name,
  COUNT(CASE WHEN assignment_id IS NULL THEN 1 END) as missing_count,
  COUNT(*) as total_trips,
  ROUND(COUNT(CASE WHEN assignment_id IS NULL THEN 1 END) * 100.0 / COUNT(*), 2) as missing_percentage
FROM trip_logs 
WHERE created_at >= NOW() - INTERVAL '24 hours'

UNION ALL

SELECT 
  'Missing Required Fields' as validation_type,
  'trip_number' as field_name,
  COUNT(CASE WHEN trip_number IS NULL THEN 1 END) as missing_count,
  COUNT(*) as total_trips,
  ROUND(COUNT(CASE WHEN trip_number IS NULL THEN 1 END) * 100.0 / COUNT(*), 2) as missing_percentage
FROM trip_logs 
WHERE created_at >= NOW() - INTERVAL '24 hours'

UNION ALL

SELECT 
  'Missing Required Fields' as validation_type,
  'performed_by_driver_id' as field_name,
  COUNT(CASE WHEN performed_by_driver_id IS NULL THEN 1 END) as missing_count,
  COUNT(*) as total_trips,
  ROUND(COUNT(CASE WHEN performed_by_driver_id IS NULL THEN 1 END) * 100.0 / COUNT(*), 2) as missing_percentage
FROM trip_logs 
WHERE created_at >= NOW() - INTERVAL '24 hours'

UNION ALL

SELECT 
  'Missing Required Fields' as validation_type,
  'performed_by_driver_name' as field_name,
  COUNT(CASE WHEN performed_by_driver_name IS NULL THEN 1 END) as missing_count,
  COUNT(*) as total_trips,
  ROUND(COUNT(CASE WHEN performed_by_driver_name IS NULL THEN 1 END) * 100.0 / COUNT(*), 2) as missing_percentage
FROM trip_logs 
WHERE created_at >= NOW() - INTERVAL '24 hours'

UNION ALL

SELECT 
  'Missing Required Fields' as validation_type,
  'performed_by_employee_id' as field_name,
  COUNT(CASE WHEN performed_by_employee_id IS NULL THEN 1 END) as missing_count,
  COUNT(*) as total_trips,
  ROUND(COUNT(CASE WHEN performed_by_employee_id IS NULL THEN 1 END) * 100.0 / COUNT(*), 2) as missing_percentage
FROM trip_logs 
WHERE created_at >= NOW() - INTERVAL '24 hours'

UNION ALL

SELECT 
  'Missing Required Fields' as validation_type,
  'performed_by_shift_id' as field_name,
  COUNT(CASE WHEN performed_by_shift_id IS NULL THEN 1 END) as missing_count,
  COUNT(*) as total_trips,
  ROUND(COUNT(CASE WHEN performed_by_shift_id IS NULL THEN 1 END) * 100.0 / COUNT(*), 2) as missing_percentage
FROM trip_logs 
WHERE created_at >= NOW() - INTERVAL '24 hours'

UNION ALL

SELECT 
  'Missing Required Fields' as validation_type,
  'performed_by_shift_type' as field_name,
  COUNT(CASE WHEN performed_by_shift_type IS NULL THEN 1 END) as missing_count,
  COUNT(*) as total_trips,
  ROUND(COUNT(CASE WHEN performed_by_shift_type IS NULL THEN 1 END) * 100.0 / COUNT(*), 2) as missing_percentage
FROM trip_logs 
WHERE created_at >= NOW() - INTERVAL '24 hours'

UNION ALL

SELECT 
  'Missing Required Fields' as validation_type,
  'notes' as field_name,
  COUNT(CASE WHEN notes IS NULL THEN 1 END) as missing_count,
  COUNT(*) as total_trips,
  ROUND(COUNT(CASE WHEN notes IS NULL THEN 1 END) * 100.0 / COUNT(*), 2) as missing_percentage
FROM trip_logs 
WHERE created_at >= NOW() - INTERVAL '24 hours'

UNION ALL

SELECT 
  'Missing Required Fields' as validation_type,
  'location_sequence' as field_name,
  COUNT(CASE WHEN location_sequence IS NULL THEN 1 END) as missing_count,
  COUNT(*) as total_trips,
  ROUND(COUNT(CASE WHEN location_sequence IS NULL THEN 1 END) * 100.0 / COUNT(*), 2) as missing_percentage
FROM trip_logs 
WHERE created_at >= NOW() - INTERVAL '24 hours'

ORDER BY field_name;

-- 5. Overall Data Quality Summary
-- Comprehensive overview of trip_logs data quality
SELECT 
  'Overall Data Quality Summary' as report_type,
  COUNT(*) as total_trips_last_24h,
  
  -- Driver information completeness
  COUNT(CASE WHEN performed_by_driver_id IS NOT NULL 
             AND performed_by_driver_name IS NOT NULL 
             AND performed_by_employee_id IS NOT NULL 
             AND performed_by_shift_id IS NOT NULL 
             AND performed_by_shift_type IS NOT NULL 
        THEN 1 END) as trips_with_complete_driver_info,
  
  -- Notes quality
  COUNT(CASE WHEN notes IS NOT NULL 
             AND jsonb_typeof(notes) IN ('string', 'object')
             AND LENGTH(notes::text) >= 10
        THEN 1 END) as trips_with_quality_notes,
  
  -- Location sequence accuracy
  COUNT(CASE WHEN location_sequence IS NOT NULL THEN 1 END) as trips_with_location_sequence,
  
  -- Overall completeness (all required fields present)
  COUNT(CASE WHEN assignment_id IS NOT NULL 
             AND trip_number IS NOT NULL 
             AND status IS NOT NULL
             AND performed_by_driver_id IS NOT NULL 
             AND performed_by_driver_name IS NOT NULL 
             AND performed_by_employee_id IS NOT NULL 
             AND performed_by_shift_id IS NOT NULL 
             AND performed_by_shift_type IS NOT NULL 
             AND notes IS NOT NULL
             AND location_sequence IS NOT NULL
        THEN 1 END) as trips_with_all_required_fields,
  
  -- Success rates
  ROUND(
    COUNT(CASE WHEN performed_by_driver_id IS NOT NULL 
               AND performed_by_driver_name IS NOT NULL 
               AND performed_by_employee_id IS NOT NULL 
               AND performed_by_shift_id IS NOT NULL 
               AND performed_by_shift_type IS NOT NULL 
          THEN 1 END) * 100.0 / COUNT(*), 2
  ) as driver_info_success_rate_percent,
  
  ROUND(
    COUNT(CASE WHEN notes IS NOT NULL 
               AND jsonb_typeof(notes) IN ('string', 'object')
               AND LENGTH(notes::text) >= 10
          THEN 1 END) * 100.0 / COUNT(*), 2
  ) as notes_quality_success_rate_percent,
  
  ROUND(
    COUNT(CASE WHEN location_sequence IS NOT NULL THEN 1 END) * 100.0 / COUNT(*), 2
  ) as location_sequence_success_rate_percent,
  
  ROUND(
    COUNT(CASE WHEN assignment_id IS NOT NULL 
               AND trip_number IS NOT NULL 
               AND status IS NOT NULL
               AND performed_by_driver_id IS NOT NULL 
               AND performed_by_driver_name IS NOT NULL 
               AND performed_by_employee_id IS NOT NULL 
               AND performed_by_shift_id IS NOT NULL 
               AND performed_by_shift_type IS NOT NULL 
               AND notes IS NOT NULL
               AND location_sequence IS NOT NULL
          THEN 1 END) * 100.0 / COUNT(*), 2
  ) as overall_completeness_rate_percent

FROM trip_logs 
WHERE created_at >= NOW() - INTERVAL '24 hours';

-- 6. Recent Problematic Trips
-- Identify specific trips with data quality issues for investigation
SELECT 
  'Recent Problematic Trips' as report_type,
  id,
  assignment_id,
  trip_number,
  status,
  created_at,
  
  -- Missing field indicators
  CASE WHEN performed_by_driver_id IS NULL THEN 'MISSING' ELSE 'OK' END as driver_id_status,
  CASE WHEN performed_by_driver_name IS NULL THEN 'MISSING' ELSE 'OK' END as driver_name_status,
  CASE WHEN performed_by_employee_id IS NULL THEN 'MISSING' ELSE 'OK' END as employee_id_status,
  CASE WHEN performed_by_shift_id IS NULL THEN 'MISSING' ELSE 'OK' END as shift_id_status,
  CASE WHEN performed_by_shift_type IS NULL THEN 'MISSING' ELSE 'OK' END as shift_type_status,
  CASE WHEN notes IS NULL THEN 'MISSING' 
       WHEN LENGTH(notes::text) < 10 THEN 'TOO_SHORT' 
       ELSE 'OK' END as notes_status,
  CASE WHEN location_sequence IS NULL THEN 'MISSING' ELSE 'OK' END as location_sequence_status,
  
  -- Count of missing fields
  (
    CASE WHEN performed_by_driver_id IS NULL THEN 1 ELSE 0 END +
    CASE WHEN performed_by_driver_name IS NULL THEN 1 ELSE 0 END +
    CASE WHEN performed_by_employee_id IS NULL THEN 1 ELSE 0 END +
    CASE WHEN performed_by_shift_id IS NULL THEN 1 ELSE 0 END +
    CASE WHEN performed_by_shift_type IS NULL THEN 1 ELSE 0 END +
    CASE WHEN notes IS NULL THEN 1 ELSE 0 END +
    CASE WHEN location_sequence IS NULL THEN 1 ELSE 0 END
  ) as total_missing_fields

FROM trip_logs 
WHERE created_at >= NOW() - INTERVAL '24 hours'
  AND (
    performed_by_driver_id IS NULL 
    OR performed_by_driver_name IS NULL 
    OR performed_by_employee_id IS NULL 
    OR performed_by_shift_id IS NULL 
    OR performed_by_shift_type IS NULL 
    OR notes IS NULL
    OR location_sequence IS NULL
  )
ORDER BY total_missing_fields DESC, created_at DESC
LIMIT 20;

-- 7. Data Quality Trends (Last 7 Days)
-- Track data quality improvements or degradation over time
SELECT 
  'Data Quality Trends' as report_type,
  DATE(created_at) as date,
  COUNT(*) as total_trips,
  
  COUNT(CASE WHEN performed_by_driver_id IS NOT NULL 
             AND performed_by_driver_name IS NOT NULL 
             AND performed_by_employee_id IS NOT NULL 
             AND performed_by_shift_id IS NOT NULL 
             AND performed_by_shift_type IS NOT NULL 
        THEN 1 END) as complete_driver_info_trips,
  
  ROUND(
    COUNT(CASE WHEN performed_by_driver_id IS NOT NULL 
               AND performed_by_driver_name IS NOT NULL 
               AND performed_by_employee_id IS NOT NULL 
               AND performed_by_shift_id IS NOT NULL 
               AND performed_by_shift_type IS NOT NULL 
          THEN 1 END) * 100.0 / COUNT(*), 2
  ) as driver_info_success_rate_percent,
  
  COUNT(CASE WHEN notes IS NOT NULL 
             AND jsonb_typeof(notes) IN ('string', 'object')
             AND LENGTH(notes::text) >= 10
        THEN 1 END) as quality_notes_trips,
  
  ROUND(
    COUNT(CASE WHEN notes IS NOT NULL 
               AND jsonb_typeof(notes) IN ('string', 'object')
               AND LENGTH(notes::text) >= 10
          THEN 1 END) * 100.0 / COUNT(*), 2
  ) as notes_quality_success_rate_percent,
  
  COUNT(CASE WHEN location_sequence IS NOT NULL THEN 1 END) as location_sequence_trips,
  
  ROUND(
    COUNT(CASE WHEN location_sequence IS NOT NULL THEN 1 END) * 100.0 / COUNT(*), 2
  ) as location_sequence_success_rate_percent

FROM trip_logs 
WHERE created_at >= NOW() - INTERVAL '7 days'
GROUP BY DATE(created_at)
ORDER BY date DESC;