#!/bin/bash
#
# Download Deployment Scripts from Private GitHub Repository
# This script downloads the deployment files needed for Ubuntu deployment
#

echo "Hauling QR Trip Management - Deployment Download Script"
echo "======================================================"
echo

# Check for required tools
if ! command -v curl &> /dev/null; then
    echo "❌ curl is not installed. Installing..."
    apt update
    apt install -y curl
    
    if ! command -v curl &> /dev/null; then
        echo "❌ Failed to install curl. Please install it manually:"
        echo "sudo apt update && sudo apt install -y curl"
        exit 1
    fi
    echo "✅ curl installed successfully"
fi

# Configuration
GITHUB_TOKEN="*********************************************************************************************"
REPO_OWNER="mightybadz18"
REPO_NAME="hauling-qr-trip-management"
BRANCH="main"

# Always download to current directory for simplicity
echo "📁 Using current directory for download: $(pwd)"
DOWNLOAD_DIR="."
# Ensure we're not creating any subdirectories
cd "$(pwd)"
echo

# Test GitHub token access
echo "🔑 Testing GitHub token access..."
if ! curl -s -o /dev/null -w "%{http_code}" -H "Authorization: token $GITHUB_TOKEN" \
     "https://api.github.com/repos/$REPO_OWNER/$REPO_NAME" | grep -q "200"; then
    echo "❌ GitHub token access failed. Please check your token."
    echo "If you're using a fresh VPS, make sure you have the correct token."
    exit 1
fi
echo "✅ GitHub token access confirmed"
echo

# Check for required tools and install if missing
check_requirements() {
    echo "🔍 Checking system requirements..."
    
    # Check for curl
    if ! command -v curl &> /dev/null; then
        echo "📦 Installing curl..."
        apt update && apt install -y curl
    fi
    
    # Check for git if needed for cloning
    if [[ "$1" == "clone" ]] && ! command -v git &> /dev/null; then
        echo "📦 Installing git..."
        apt update && apt install -y git
    fi
    
    echo "✅ System requirements satisfied"
}

# Function to download file from GitHub API
download_file() {
    local file_path="$1"
    local output_name="$2"
    
    echo "📥 Downloading: $file_path"
    
    # Try up to 3 times with increasing timeouts
    for attempt in 1 2 3; do
        if curl -s --connect-timeout $((5 * attempt)) -H "Authorization: token $GITHUB_TOKEN" \
                -H "Accept: application/vnd.github.v3.raw" \
                -o "$output_name" \
                "https://api.github.com/repos/$REPO_OWNER/$REPO_NAME/contents/$file_path?ref=$BRANCH"; then
            
            # Check if file was downloaded successfully (not empty and not an error message)
            if [[ -s "$output_name" ]] && ! grep -q "Not Found\|Bad credentials\|API rate limit" "$output_name" 2>/dev/null; then
                echo "✅ Downloaded: $output_name"
                return 0
            else
                echo "⚠️  Attempt $attempt failed, retrying..."
                sleep 2
            fi
        fi
    done
    
    echo "❌ Failed to download: $file_path after 3 attempts"
    rm -f "$output_name" 2>/dev/null
    return 1
}

# Function to clone entire repository
clone_repository() {
    echo "🔄 Attempting to clone entire repository..."
    
    # Try cloning with token
    if git clone "https://$<EMAIL>/$REPO_OWNER/$REPO_NAME.git" full-repository 2>/dev/null; then
        echo "✅ Repository cloned successfully to: full-repository/"
        echo "📂 Repository contents:"
        ls -la full-repository/ | head -10
        return 0
    else
        echo "❌ Repository clone failed"
        return 1
    fi
}

# Check requirements first
check_requirements

echo "Choose download method:"
echo "1. Download deployment scripts only (recommended)"
echo "2. Clone entire repository"
echo "3. Both"
echo "4. Quick download (essential files only)"
echo

# Check if running in non-interactive mode (for one-line bootstrap)
if [[ -t 0 ]]; then
    # Interactive mode
    read -p "Enter your choice (1-4): " choice
else
    # Non-interactive mode (piped from curl)
    echo "Running in non-interactive mode, using option 1 (download scripts only)"
    choice=1
fi

case $choice in
    1|3)
        echo
        echo "📥 Downloading deployment scripts..."
        echo "=================================="
        
        # Download deployment scripts
        download_file "deploy-hauling-qr-ubuntu/deploy-hauling-qr-ubuntu-fixed.sh" "deploy-hauling-qr-ubuntu-fixed.sh"
        download_file "deploy-hauling-qr-ubuntu/run-deployment.sh" "run-deployment.sh"
        download_file "deploy-hauling-qr-ubuntu/deployment-config.conf" "deployment-config.conf"
        download_file "deploy-hauling-qr-ubuntu/DEPLOYMENT_STEPS.md" "DEPLOYMENT_STEPS.md"
        download_file "deploy-hauling-qr-ubuntu/USER_PERMISSIONS.md" "USER_PERMISSIONS.md"
        download_file "deploy-hauling-qr-ubuntu/GITHUB_ACCESS_GUIDE.md" "GITHUB_ACCESS_GUIDE.md"
        download_file "deploy-hauling-qr-ubuntu/CONFIGURATION_GUIDE.md" "CONFIGURATION_GUIDE.md"
        download_file "deploy-hauling-qr-ubuntu/README.md" "README.md"
        download_file "deploy-hauling-qr-ubuntu/READY_TO_DEPLOY.md" "READY_TO_DEPLOY.md"
        
        # Download new troubleshooting and fix files
        download_file "deploy-hauling-qr-ubuntu/POST_DEPLOYMENT_TROUBLESHOOTING.md" "POST_DEPLOYMENT_TROUBLESHOOTING.md"
        download_file "deploy-hauling-qr-ubuntu/POSTGRESQL_TABLE_INSPECTION_GUIDE.md" "POSTGRESQL_TABLE_INSPECTION_GUIDE.md"
        download_file "deploy-hauling-qr-ubuntu/QUICK_FIXES.md" "QUICK_FIXES.md"
        download_file "deploy-hauling-qr-ubuntu/DATABASE_DEPLOYMENT_FIXES.md" "DATABASE_DEPLOYMENT_FIXES.md"
        download_file "deploy-hauling-qr-ubuntu/DEPLOYMENT_FIXES.md" "DEPLOYMENT_FIXES.md"
        download_file "fix-database-script.sh" "fix-database-script.sh"
        
        # Make scripts executable
        chmod +x deploy-hauling-qr-ubuntu-fixed.sh 2>/dev/null
        chmod +x run-deployment.sh 2>/dev/null
        chmod +x fix-database-script.sh 2>/dev/null
        
        echo
        echo "✅ Deployment scripts downloaded!"
        echo "📁 Files in current directory:"
        ls -la *.sh *.conf *.md 2>/dev/null
        ;;
    4)
        echo
        echo "📥 Quick download (essential files only)..."
        echo "======================================"
        
        # Download only the essential files
        download_file "deploy-hauling-qr-ubuntu/deploy-hauling-qr-ubuntu-fixed.sh" "deploy-hauling-qr-ubuntu-fixed.sh"
        download_file "deploy-hauling-qr-ubuntu/run-deployment.sh" "run-deployment.sh"
        download_file "deploy-hauling-qr-ubuntu/deployment-config.conf" "deployment-config.conf"
        download_file "deploy-hauling-qr-ubuntu/DEPLOYMENT_STEPS.md" "DEPLOYMENT_STEPS.md"
        download_file "fix-database-script.sh" "fix-database-script.sh"
        
        # Make scripts executable
        chmod +x deploy-hauling-qr-ubuntu-fixed.sh 2>/dev/null
        chmod +x run-deployment.sh 2>/dev/null
        
        echo
        echo "✅ Essential files downloaded!"
        echo "📁 Files in current directory:"
        ls -la *.sh *.conf *.md 2>/dev/null
        ;;
esac

case $choice in
    2|3)
        echo
        echo "🔄 Cloning repository..."
        echo "======================"
        clone_repository
        ;;
esac

echo
echo "🎉 Download completed!"
echo
echo "📁 Download location: $(pwd)"
echo

# Check if we're in non-interactive mode (for one-line bootstrap)
if [[ ! -t 0 ]]; then
    echo "Running in non-interactive mode, exiting successfully"
    exit 0
fi

echo "Next steps (from DEPLOYMENT_STEPS.md):"
echo
echo "Step 4: Configure Deployment"
echo "  nano deployment-config.conf"
echo "  - Update DOMAIN_NAME to your domain"
echo "  - Change ADMIN_PASSWORD from default"
echo "  - Set a strong DB_PASSWORD"
echo
echo "Step 5: Run Deployment"
echo "  sudo ./run-deployment.sh"
echo "  - Choose option 2 for full deployment when prompted"
echo
echo "For complete instructions:"
echo "  cat DEPLOYMENT_STEPS.md"
echo
echo "🔒 Security reminder: Change default passwords after deployment"
echo

# Show what was downloaded
echo "📋 Downloaded files:"
ls -la | grep -E '\.(sh|conf|md)'$' || echo "No files found"