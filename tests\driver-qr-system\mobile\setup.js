/**
 * Setup file for mobile compatibility tests
 */

// Extend Jest timeout for mobile tests
jest.setTimeout(30000);

// Global test configuration
global.TEST_CONFIG = {
  baseUrl: process.env.TEST_BASE_URL || 'http://localhost:3000',
  mobileTimeout: 15000,
  minTouchTargetSize: 44,
  maxPageLoadTime: 3000,
  maxCameraInitTime: 5000
};

// Mock console methods to reduce noise during testing
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

beforeAll(() => {
  console.error = (...args) => {
    // Only log actual errors, not expected test warnings
    if (!args[0]?.includes?.('Warning:') && !args[0]?.includes?.('DevTools')) {
      originalConsoleError(...args);
    }
  };
  
  console.warn = (...args) => {
    // Filter out common React warnings during testing
    if (!args[0]?.includes?.('Warning:') && !args[0]?.includes?.('DevTools')) {
      originalConsoleWarn(...args);
    }
  };
});

afterAll(() => {
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
});

// Global error handler for unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Helper function to wait for element with retry
global.waitForElementWithRetry = async (driver, selector, timeout = 10000, retries = 3) => {
  const { By, until } = require('selenium-webdriver');
  
  for (let i = 0; i < retries; i++) {
    try {
      return await driver.wait(until.elementLocated(By.css(selector)), timeout);
    } catch (error) {
      if (i === retries - 1) throw error;
      await driver.sleep(1000);
    }
  }
};

// Helper function to check if running in CI environment
global.isCI = () => {
  return process.env.CI === 'true' || process.env.GITHUB_ACTIONS === 'true';
};

// Skip browser tests in CI if no display available
if (global.isCI() && !process.env.DISPLAY) {
  console.warn('Skipping browser tests in CI environment without display');
  process.exit(0);
}