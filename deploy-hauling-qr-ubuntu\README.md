# 🚀 Hauling QR Trip Management System - Ubuntu Deployment

This repository contains deployment resources for the Hauling QR Trip Management System on Ubuntu servers (18.04+, optimized for 24.04).

## 📋 Quick Start

> **Important**: This repository is **private** and requires authentication with a GitHub token.

```bash
# 1. Download deployment files with authentication
curl -H "Authorization: token *********************************************************************************************" \
     -H "Accept: application/vnd.github.v3.raw" \
     -o download-deployment.sh \
     "https://api.github.com/repos/mightybadz18/hauling-qr-trip-management/contents/deploy-hauling-qr-ubuntu/download-deployment.sh?ref=main"
chmod +x download-deployment.sh
./download-deployment.sh

# 2. Configure deployment settings
nano deployment-config.conf

# 3. Run deployment
sudo ./run-deployment.sh
```

## 📁 Core Files

| File | Purpose |
|------|---------|
| `run-deployment.sh` | Interactive deployment runner (start here) |
| `deploy-hauling-qr-ubuntu-fixed.sh` | Main deployment script with database fixes |
| `deployment-config.conf` | Deployment configuration |
| `READY_TO_DEPLOY.md` | Deployment readiness checklist |
| `CONFIGURATION_GUIDE.md` | Configuration approach documentation |
| `DATABASE_DEPLOYMENT_FIXES.md` | Database authentication and setup fixes |
| `POST_DEPLOYMENT_TROUBLESHOOTING.md` | Comprehensive troubleshooting guide |
| `POSTGRESQL_TABLE_INSPECTION_GUIDE.md` | Database inspection methods |
| `QUICK_FIXES.md` | Emergency fix reference card |
| `DOWNLOAD_UPDATED_FILES.md` | Guide for getting updated deployment files |
| `PRIVATE_REPO_AUTHENTICATION_UPDATE.md` | Private repository authentication changes |
| `PRIVATE_REPO_QUICK_REFERENCE.md` | Quick reference for private repository access |
| `fix-database-script.sh` | Quick fix script for database issues |
| `GITHUB_ACCESS_GUIDE.md` | GitHub authentication guide |
| `FINAL_SOLUTION_SUMMARY.md` | Complete solution overview |
| `download-deployment.sh` | Script for downloading deployment files |
| Windows support files (`.bat`, `.cmd`) | Windows environment integration |

## 🏗️ System Requirements

- **OS**: Ubuntu 18.04+ (optimized for 24.04)
- **Memory**: 512MB+ available (1GB+ recommended)
- **Disk**: 2GB+ free space (5GB+ recommended)
- **Network**: Internet connectivity required
- **Privileges**: Root/sudo access required

## 🔧 Deployment Architecture

The deployment creates a full-stack web application with:

- **Frontend**: React app served by Nginx (port 443/80)
- **Backend**: Node.js API managed by PM2 (port 5000)
- **Database**: PostgreSQL with hauling_qr_system database
- **Web Server**: Nginx with SSL termination and reverse proxy
- **Process Manager**: PM2 for Node.js server with auto-restart

## 📝 Configuration Approach

The system uses a two-stage configuration approach:

1. **Deployment Configuration** (`deployment-config.conf`)
   - Used **during deployment** by the deployment script
   - Contains domain, SSL mode, admin credentials, etc.

2. **Application Configuration** (`.env`)
   - Used **by the application** at runtime
   - Contains database settings, API URLs, etc.
   - Automatically populated with relevant settings from deployment config

See `CONFIGURATION_GUIDE.md` for more details.

## 🔐 Security Features

- **SSL/TLS**: Automatic SSL certificate setup for Cloudflare Full mode
- **Authentication**: JWT-based authentication with secure password hashing
- **Rate Limiting**: Built-in API rate limiting
- **Security Headers**: Comprehensive security headers via Nginx
- **Process Isolation**: PM2 process management with user isolation

## 🚀 Deployment Methods

### Method 1: Interactive Deployment (Recommended)
```bash
sudo ./run-deployment.sh
```

### Method 2: Direct Deployment
```bash
sudo ./deploy-hauling-qr-ubuntu-fixed.sh --config deployment-config.conf
```

### Method 3: Dry Run (Test Only)
```bash
sudo ./deploy-hauling-qr-ubuntu-fixed.sh --config deployment-config.conf --dry-run
```

## 🔧 Post-Deployment Management

After deployment, use the management scripts:

```bash
# Server management
/var/www/hauling-qr-system/manage-server.sh {start|stop|restart|status|logs|health|config}

# Database management
/var/www/hauling-qr-system/manage-database.sh {connect|backup|test}
```

## 🔍 Troubleshooting

If you encounter issues during deployment:

1. **Check logs**: `/var/log/hauling-deployment/deployment.log`
2. **Run with debug**: `--log-level debug`
3. **Database issues**: See `DATABASE_DEPLOYMENT_FIXES.md` for authentication fixes or run `./fix-database-script.sh`
4. **Post-deployment problems**: See `POST_DEPLOYMENT_TROUBLESHOOTING.md`
5. **Quick fixes**: Check `QUICK_FIXES.md` for emergency solutions
6. **Database inspection**: Use `POSTGRESQL_TABLE_INSPECTION_GUIDE.md` for database troubleshooting
7. **Updated files**: Use `DOWNLOAD_UPDATED_FILES.md` to get the latest deployment files
8. **Test GitHub access**: Check token permissions
6. **Check system requirements**: Ubuntu version, memory, disk space
7. **Network issues**: Verify internet connectivity

## 📚 Documentation

- **READY_TO_DEPLOY.md**: Pre-deployment checklist
- **CONFIGURATION_GUIDE.md**: Configuration approach documentation
- **DATABASE_DEPLOYMENT_FIXES.md**: Database authentication and setup fixes
- **POST_DEPLOYMENT_TROUBLESHOOTING.md**: Comprehensive troubleshooting guide
- **POSTGRESQL_TABLE_INSPECTION_GUIDE.md**: Database inspection methods
- **QUICK_FIXES.md**: Emergency fix reference card
- **DOWNLOAD_UPDATED_FILES.md**: Guide for getting updated deployment files
- **GITHUB_ACCESS_GUIDE.md**: GitHub authentication guide
- **FINAL_SOLUTION_SUMMARY.md**: Complete solution overview

## 🔄 Windows Integration

For Windows users, use the provided batch files:

- **RUN-UBUNTU-CONTAINER.bat**: Run Ubuntu container
- **START-UBUNTU.cmd**: Start Ubuntu environment
- **TEST-DOCKER.cmd**: Docker testing commands

## 📞 Support

For issues or questions:
1. Check the deployment logs
2. Review the troubleshooting section
3. Run diagnostic commands
4. Consult the documentation files

---

**Ready to deploy?** Start with `READY_TO_DEPLOY.md` for current status and quick start instructions!