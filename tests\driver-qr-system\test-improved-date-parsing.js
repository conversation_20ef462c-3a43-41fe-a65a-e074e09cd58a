// Test the improved date parsing logic with graceful error handling

function testImprovedDateParsing() {
  console.log('Testing improved date parsing logic...\n');
  
  // Simulate the improved code from driver.js
  function parseShiftDateTime(currentShift) {
    let startDateTime;
    let durationHours = 0;
    let durationMinutes = 0;
    
    try {
      // Validate that we have valid date and time values
      if (!currentShift.start_date || !currentShift.start_time) {
        throw new Error('Missing start_date or start_time');
      }
      
      // Ensure start_date is in proper format (handle Date objects or strings)
      let formattedStartDate;
      if (currentShift.start_date instanceof Date) {
        formattedStartDate = currentShift.start_date.toISOString().split('T')[0];
      } else {
        formattedStartDate = currentShift.start_date;
      }
      
      startDateTime = new Date(`${formattedStartDate}T${currentShift.start_time}`);
      
      // Check if the created date is valid
      if (isNaN(startDateTime.getTime())) {
        throw new Error('Invalid date/time combination');
      }
      
      const endDateTime = new Date();
      const durationMs = endDateTime - startDateTime;
      durationHours = Math.floor(durationMs / (1000 * 60 * 60));
      durationMinutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));
      
      // Ensure non-negative duration
      if (durationMs < 0) {
        durationHours = 0;
        durationMinutes = 0;
      }
      
      return {
        success: true,
        startDateTime: startDateTime.toISOString(),
        duration: `${durationHours}h ${durationMinutes}m`
      };
      
    } catch (dateError) {
      // Log the date parsing error but don't fail the checkout
      console.log(`    📝 Logged error: ${dateError.message}`);
      
      // Use current timestamp as fallback for start time
      startDateTime = new Date();
      durationHours = 0;
      durationMinutes = 0;
      
      return {
        success: false,
        error: dateError.message,
        fallback: true,
        startDateTime: startDateTime.toISOString(),
        duration: `${durationHours}h ${durationMinutes}m`
      };
    }
  }
  
  // Test cases
  const testCases = [
    {
      name: 'Valid date string and time',
      shift: {
        id: 1,
        start_date: '2025-07-27',
        start_time: '10:45:19'
      }
    },
    {
      name: 'Date object and time (fixed format)',
      shift: {
        id: 2,
        start_date: new Date('2025-07-27'),
        start_time: '10:45:19'
      }
    },
    {
      name: 'NULL date (graceful fallback)',
      shift: {
        id: 3,
        start_date: null,
        start_time: '10:45:19'
      }
    },
    {
      name: 'NULL time (graceful fallback)',
      shift: {
        id: 4,
        start_date: '2025-07-27',
        start_time: null
      }
    },
    {
      name: 'Invalid date string (graceful fallback)',
      shift: {
        id: 5,
        start_date: 'invalid-date',
        start_time: '10:45:19'
      }
    },
    {
      name: 'Malformed date with timezone (graceful fallback)',
      shift: {
        id: 6,
        start_date: 'Sun Jul 27 2025 00:00:00 GMT+0800 (Singapore Standard Time)',
        start_time: '10:45:19'
      }
    }
  ];
  
  testCases.forEach((testCase, index) => {
    console.log(`Test ${index + 1}: ${testCase.name}`);
    console.log(`  Input: start_date=${testCase.shift.start_date}, start_time=${testCase.shift.start_time}`);
    
    const result = parseShiftDateTime(testCase.shift);
    
    if (result.success) {
      console.log(`  ✅ SUCCESS: ${result.startDateTime}, duration=${result.duration}`);
    } else {
      console.log(`  ⚠️  HANDLED GRACEFULLY: ${result.error}`);
      console.log(`     Fallback used: ${result.startDateTime}, duration=${result.duration}`);
    }
    
    console.log('');
  });
  
  console.log('🎉 All test cases handled properly - no crashes!');
}

// Run the test
testImprovedDateParsing();