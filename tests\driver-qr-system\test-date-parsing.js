// Test the date parsing logic that was causing the error

function testDateParsing() {
  console.log('Testing date parsing scenarios...\n');
  
  // Test cases that might cause "Invalid time value" error
  const testCases = [
    {
      name: 'Valid date string and time',
      start_date: '2025-07-27',
      start_time: '10:45:19'
    },
    {
      name: 'Date object and time',
      start_date: new Date('2025-07-27'),
      start_time: '10:45:19'
    },
    {
      name: 'Malformed date string and time',
      start_date: 'Sun Jul 27 2025 00:00:00 GMT+0800 (Singapore Standard Time)',
      start_time: '10:45:19'
    },
    {
      name: 'NULL date',
      start_date: null,
      start_time: '10:45:19'
    },
    {
      name: 'NULL time',
      start_date: '2025-07-27',
      start_time: null
    },
    {
      name: 'Invalid date string',
      start_date: 'invalid-date',
      start_time: '10:45:19'
    }
  ];
  
  testCases.forEach((testCase, index) => {
    console.log(`Test ${index + 1}: ${testCase.name}`);
    console.log(`  Input: start_date=${testCase.start_date}, start_time=${testCase.start_time}`);
    
    try {
      // Simulate the original problematic code
      let startDateTime;
      let durationHours = 0;
      let durationMinutes = 0;
      
      // Validate that we have valid date and time values
      if (!testCase.start_date || !testCase.start_time) {
        throw new Error('Missing start_date or start_time');
      }
      
      // Ensure start_date is in proper format (handle Date objects or strings)
      let formattedStartDate;
      if (testCase.start_date instanceof Date) {
        formattedStartDate = testCase.start_date.toISOString().split('T')[0];
      } else {
        formattedStartDate = testCase.start_date;
      }
      
      startDateTime = new Date(`${formattedStartDate}T${testCase.start_time}`);
      
      // Check if the created date is valid
      if (isNaN(startDateTime.getTime())) {
        throw new Error('Invalid date/time combination');
      }
      
      const endDateTime = new Date();
      const durationMs = endDateTime - startDateTime;
      durationHours = Math.floor(durationMs / (1000 * 60 * 60));
      durationMinutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));
      
      // Ensure non-negative duration
      if (durationMs < 0) {
        durationHours = 0;
        durationMinutes = 0;
      }
      
      console.log(`  ✅ SUCCESS: startDateTime=${startDateTime.toISOString()}, duration=${durationHours}h ${durationMinutes}m`);
      
    } catch (error) {
      console.log(`  ❌ ERROR: ${error.message}`);
    }
    
    console.log('');
  });
}

// Run the test
testDateParsing();