# Post-Deployment Troubleshooting Guide

A comprehensive guide for troubleshooting common issues after deploying the Hauling QR Trip Management System.

## Quick Status Check

### Run System Status
```bash
cd /var/www/hauling-qr-system
./manage-server.sh status
```

### Expected Healthy Output
```
🎉 All services are running correctly!
✅ Application Server (PM2)
✅ Database (PostgreSQL)  
✅ Web Server (Nginx)
✅ Network Ports (80, 443, 5000)
```

---

## Issue 1: Nginx Not Running ❌

### Symptoms
```
❌ Nginx not running
❌ Network Ports (80, 443, 5000) - Missing ports 80, 443
```

### Diagnosis Commands
```bash
# Check nginx status
sudo systemctl status nginx

# Check nginx configuration
sudo nginx -t

# Check nginx error logs
sudo tail -f /var/log/nginx/error.log

# Check if nginx is installed
which nginx
nginx -v
```

### Fix 1: Start Nginx Service
```bash
# Start nginx
sudo systemctl start nginx
sudo systemctl enable nginx

# Verify it's running
sudo systemctl status nginx
./manage-server.sh status
```

### Fix 2: Nginx Configuration Issues
```bash
# Test nginx configuration
sudo nginx -t

# If config is broken, check the site config
sudo nano /etc/nginx/sites-available/hauling-qr-system

# Check if site is enabled
ls -la /etc/nginx/sites-enabled/
sudo ln -sf /etc/nginx/sites-available/hauling-qr-system /etc/nginx/sites-enabled/

# Reload nginx
sudo nginx -s reload
```

### Fix 3: Nginx Installation Issues
```bash
# Reinstall nginx if missing
sudo apt-get update
sudo apt-get install -y nginx

# Start and enable
sudo systemctl start nginx
sudo systemctl enable nginx
```

### Fix 4: Port Conflicts
```bash
# Check what's using port 80/443
sudo lsof -i :80
sudo lsof -i :443

# Kill conflicting processes if needed
sudo pkill -f apache2  # If Apache is running
sudo systemctl stop apache2

# Start nginx
sudo systemctl start nginx
```

### Fix 5: Permissions Issues
```bash
# Fix nginx user permissions
sudo chown -R www-data:www-data /var/www/hauling-qr-system
sudo chmod -R 755 /var/www/hauling-qr-system

# Check nginx user in config
grep "user " /etc/nginx/nginx.conf

# Restart nginx
sudo systemctl restart nginx
```

---

## Issue 2: PM2/Application Server Not Running ❌

### Symptoms
```
❌ PM2 not available
❌ Application Server (PM2)
❌ Network Ports - Missing port 5000
```

### Diagnosis Commands
```bash
# Check PM2 status
pm2 list
pm2 status

# Check if PM2 is installed
which pm2
pm2 --version

# Check application logs
pm2 logs hauling-qr-server
```

### Fix 1: Start PM2 Process
```bash
# Navigate to app directory
cd /var/www/hauling-qr-system

# Start the application
pm2 start server/server.js --name hauling-qr-server

# Save PM2 configuration
pm2 save

# Setup PM2 startup
pm2 startup
```

### Fix 2: PM2 Installation Issues
```bash
# Install PM2 globally
sudo npm install -g pm2

# Verify installation
pm2 --version

# Start application
cd /var/www/hauling-qr-system
pm2 start server/server.js --name hauling-qr-server
```

### Fix 3: Node.js/NPM Issues
```bash
# Check Node.js version
node --version
npm --version

# If missing, install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install application dependencies
cd /var/www/hauling-qr-system/server
npm install

# Start with PM2
pm2 start server.js --name hauling-qr-server
```

### Fix 4: Application Code Issues
```bash
# Check if server.js exists
ls -la /var/www/hauling-qr-system/server/server.js

# Test application manually
cd /var/www/hauling-qr-system/server
node server.js

# Check for missing dependencies
npm install

# Check environment variables
cat /var/www/hauling-qr-system/.env
```

### Fix 5: Port 5000 Conflicts
```bash
# Check what's using port 5000
sudo lsof -i :5000

# Kill conflicting process
sudo kill -9 $(sudo lsof -t -i:5000)

# Restart application
pm2 restart hauling-qr-server
```

---

## Issue 3: PostgreSQL Database Not Running ❌

### Symptoms
```
❌ PostgreSQL status unknown
❌ Database (PostgreSQL)
Database connection failed
```

### Diagnosis Commands
```bash
# Check PostgreSQL status
sudo systemctl status postgresql
service postgresql status

# Check if PostgreSQL is installed
which psql
psql --version

# Check database connection
./manage-database.sh test
```

### Fix 1: Start PostgreSQL Service
```bash
# Start PostgreSQL
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Verify it's running
sudo systemctl status postgresql
```

### Fix 2: PostgreSQL Installation Issues
```bash
# Install PostgreSQL
sudo apt-get update
sudo apt-get install -y postgresql postgresql-contrib

# Start service
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

### Fix 3: Database User/Database Issues
```bash
# Recreate database user and database
sudo -u postgres psql -c "DROP DATABASE IF EXISTS hauling_qr_system;"
sudo -u postgres psql -c "DROP USER IF EXISTS hauling_app;"
sudo -u postgres psql -c "CREATE USER hauling_app WITH PASSWORD 'PostgreSQLPassword123';"
sudo -u postgres psql -c "CREATE DATABASE hauling_qr_system OWNER hauling_app;"
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE hauling_qr_system TO hauling_app;"

# Initialize database
cd /var/www/hauling-qr-system
./manage-database.sh init
```

### Fix 4: PostgreSQL Configuration Issues
```bash
# Check PostgreSQL configuration
sudo nano /etc/postgresql/*/main/postgresql.conf

# Check authentication settings
sudo nano /etc/postgresql/*/main/pg_hba.conf

# Restart PostgreSQL
sudo systemctl restart postgresql
```

### Fix 5: Connection Issues
```bash
# Test connection as postgres user
sudo -u postgres psql -c "SELECT version();"

# Test connection as hauling_app user
PGPASSWORD="PostgreSQLPassword123" psql -h localhost -p 5432 -U hauling_app -d hauling_qr_system -c "SELECT version();"

# Check if database exists
sudo -u postgres psql -c "\l" | grep hauling
```

---

## Issue 4: Network Ports Not Listening ❌

### Symptoms
```
❌ No required ports found listening
❌ Network Ports (80, 443, 5000)
```

### Diagnosis Commands
```bash
# Check listening ports
sudo netstat -tlnp | grep -E ':(80|443|5000)'
sudo ss -tlnp | grep -E ':(80|443|5000)'
sudo lsof -i -P -n | grep LISTEN

# Install netstat if missing
sudo apt-get install -y net-tools
```

### Fix 1: Install Network Tools
```bash
# Install net-tools for netstat
sudo apt-get update
sudo apt-get install -y net-tools

# Verify installation
netstat --version

# Check ports again
sudo netstat -tlnp | grep -E ':(80|443|5000)'
```

### Fix 2: Firewall Issues
```bash
# Check firewall status
sudo ufw status

# Allow required ports
sudo ufw allow 80
sudo ufw allow 443
sudo ufw allow 5000

# Or disable firewall temporarily
sudo ufw disable
```

### Fix 3: Service Binding Issues
```bash
# Check if services are binding to correct interfaces
# For nginx (should bind to 0.0.0.0:80 and 0.0.0.0:443)
sudo netstat -tlnp | grep nginx

# For Node.js app (should bind to 0.0.0.0:5000)
sudo netstat -tlnp | grep node

# Check application configuration
grep -r "listen\|port" /var/www/hauling-qr-system/server/
```

---

## Issue 5: Client/Frontend Not Working ❌

### Symptoms
- Website not loading in browser
- 502 Bad Gateway errors
- Static files not serving

### Diagnosis Commands
```bash
# Check if client build exists
ls -la /var/www/hauling-qr-system/client/build/

# Check nginx configuration for client
sudo nginx -t
cat /etc/nginx/sites-available/hauling-qr-system
```

### Fix 1: Build Client Application
```bash
# Navigate to client directory
cd /var/www/hauling-qr-system/client

# Install dependencies
npm install

# Build production version
npm run build

# Verify build directory
ls -la build/

# Restart nginx
sudo systemctl restart nginx
```

### Fix 2: Nginx Client Configuration
```bash
# Check nginx site configuration
sudo nano /etc/nginx/sites-available/hauling-qr-system

# Ensure it has proper client serving configuration:
# location / {
#     root /var/www/hauling-qr-system/client/build;
#     try_files $uri $uri/ /index.html;
# }

# Test and reload nginx
sudo nginx -t
sudo systemctl reload nginx
```

### Fix 3: File Permissions
```bash
# Fix client build permissions
sudo chown -R www-data:www-data /var/www/hauling-qr-system/client/build
sudo chmod -R 755 /var/www/hauling-qr-system/client/build

# Restart nginx
sudo systemctl restart nginx
```

---

## Complete System Recovery Script

### Create Recovery Script
```bash
cat > /var/www/hauling-qr-system/recover-system.sh << 'EOF'
#!/bin/bash
echo "🔧 Hauling QR System Recovery Script"
echo "===================================="

# Stop all services
echo "🛑 Stopping all services..."
pm2 stop all 2>/dev/null
sudo systemctl stop nginx 2>/dev/null

# Start PostgreSQL
echo "🗄️ Starting PostgreSQL..."
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Start PM2 application
echo "🚀 Starting application server..."
cd /var/www/hauling-qr-system
pm2 start server/server.js --name hauling-qr-server 2>/dev/null || pm2 restart hauling-qr-server
pm2 save

# Start Nginx
echo "🌐 Starting web server..."
sudo systemctl start nginx
sudo systemctl enable nginx

# Install missing tools
echo "🔧 Installing missing tools..."
sudo apt-get update >/dev/null 2>&1
sudo apt-get install -y net-tools >/dev/null 2>&1

# Wait for services to start
echo "⏳ Waiting for services to start..."
sleep 5

# Check final status
echo "📊 Final system status:"
./manage-server.sh status

echo "✅ Recovery script completed!"
EOF

chmod +x /var/www/hauling-qr-system/recover-system.sh
```

### Run Recovery Script
```bash
cd /var/www/hauling-qr-system
sudo ./recover-system.sh
```

---

## Monitoring and Maintenance

### Daily Health Checks
```bash
# Add to crontab for daily checks
echo "0 9 * * * cd /var/www/hauling-qr-system && ./manage-server.sh health >> /var/log/hauling-health.log 2>&1" | sudo crontab -

# Manual health check
./manage-server.sh health
```

### Log Monitoring
```bash
# Application logs
pm2 logs hauling-qr-server --lines 50

# Nginx logs
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log

# PostgreSQL logs
sudo tail -f /var/log/postgresql/postgresql-*-main.log

# System logs
sudo journalctl -u nginx -f
sudo journalctl -u postgresql -f
```

### Performance Monitoring
```bash
# Check system resources
htop
df -h
free -h

# Check database performance
./manage-database.sh size
./manage-database.sh count

# Check application performance
pm2 monit
```

---

## Emergency Contacts and Resources

### Quick Commands Reference
```bash
# System status
./manage-server.sh status

# Database status  
./manage-database.sh verify

# Restart everything
sudo ./recover-system.sh

# View logs
pm2 logs hauling-qr-server
sudo tail -f /var/log/nginx/error.log
```

### Common File Locations
- **Application**: `/var/www/hauling-qr-system/`
- **Nginx Config**: `/etc/nginx/sites-available/hauling-qr-system`
- **Environment**: `/var/www/hauling-qr-system/.env`
- **Database Init**: `/var/www/hauling-qr-system/database/init.sql`
- **Logs**: `/var/log/nginx/`, PM2 logs via `pm2 logs`

### Support Commands
```bash
# Generate system report
./manage-server.sh status > system-report.txt
./manage-database.sh health >> system-report.txt
pm2 list >> system-report.txt
sudo systemctl status nginx >> system-report.txt
sudo systemctl status postgresql >> system-report.txt
```

This troubleshooting guide covers the most common post-deployment issues and provides step-by-step solutions for each problem.