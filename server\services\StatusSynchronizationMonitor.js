/**
 * Status Synchronization Monitor
 * 
 * Provides continuous monitoring of status synchronization between:
 * - Shift Management
 * - Assignment Management  
 * - Trip Monitoring
 * 
 * Features:
 * - Automated conflict detection and resolution
 * - Real-time alerts for critical issues
 * - Metrics tracking for status consistency
 * - Comprehensive logging for debugging
 */

const { getClient } = require('../config/database');
const StatusSynchronizationService = require('./StatusSynchronizationService');
const DataFlowLogger = require('../utils/DataFlowLogger');
const { logInfo, logError, logDebug } = require('../utils/logger');

class StatusSynchronizationMonitor {
  constructor() {
    this.isRunning = false;
    this.monitoringInterval = null;
    this.checkInterval = 60000; // 1 minute default
    this.lastCheck = null;
    this.consecutiveErrors = 0;
    this.maxConsecutiveErrors = 5;
    this.metrics = {
      totalChecks: 0,
      issuesDetected: 0,
      autoFixesApplied: 0,
      criticalIssues: 0,
      warningIssues: 0,
      lastSuccessfulCheck: null,
      averageCheckDuration: 0,
      uptime: Date.now()
    };
    this.alerts = [];
    this.alertHistory = [];
  }

  /**
   * Start the status synchronization monitoring
   * @param {number} interval Check interval in milliseconds
   */
  start(interval = 60000) {
    if (this.isRunning) {
      logInfo('STATUS_SYNC_MONITOR', 'Monitor already running');
      return;
    }

    this.checkInterval = interval;
    this.isRunning = true;
    this.metrics.uptime = Date.now();

    logInfo('STATUS_SYNC_MONITOR', 'Starting status synchronization monitoring', {
      check_interval_ms: this.checkInterval,
      check_interval_minutes: this.checkInterval / 60000
    });

    // Run initial check
    this.performMonitoringCheck();

    // Set up periodic monitoring
    this.monitoringInterval = setInterval(() => {
      this.performMonitoringCheck();
    }, this.checkInterval);

    logInfo('STATUS_SYNC_MONITOR', 'Status synchronization monitoring started successfully');
  }

  /**
   * Stop the status synchronization monitoring
   */
  stop() {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    logInfo('STATUS_SYNC_MONITOR', 'Status synchronization monitoring stopped', {
      total_checks: this.metrics.totalChecks,
      total_issues: this.metrics.issuesDetected,
      total_fixes: this.metrics.autoFixesApplied,
      uptime_minutes: Math.round((Date.now() - this.metrics.uptime) / 60000)
    });
  }

  /**
   * Perform a comprehensive monitoring check
   */
  async performMonitoringCheck() {
    const checkStartTime = Date.now();
    const correlationId = DataFlowLogger.createCorrelationId('sync_monitor');

    try {
      DataFlowLogger.logWithCorrelation(correlationId, 'sync_monitor', 'CHECK_START', 'Starting status synchronization monitoring check');

      // Run comprehensive status synchronization monitoring
      const syncResults = await StatusSynchronizationService.monitorStatusSynchronization();

      // Update metrics
      this.updateMetrics(syncResults, checkStartTime);

      // Process alerts
      await this.processAlerts(syncResults, correlationId);

      // Log successful check
      this.lastCheck = new Date();
      this.consecutiveErrors = 0;
      this.metrics.lastSuccessfulCheck = this.lastCheck;
      
      // Reset to normal check interval if we were in backoff mode
      this.resetErrorCount();

      DataFlowLogger.logWithCorrelation(correlationId, 'sync_monitor', 'CHECK_COMPLETE', 'Status synchronization monitoring check completed', {
        duration_ms: Date.now() - checkStartTime,
        issues_found: syncResults.sync_issues.length,
        auto_fixes_applied: syncResults.auto_fixes_applied,
        overall_status: syncResults.overall_status
      });

    } catch (error) {
      this.handleMonitoringError(error, correlationId, checkStartTime);
    }
  }

  /**
   * Update monitoring metrics
   * @param {Object} syncResults Synchronization results
   * @param {number} checkStartTime Check start timestamp
   */
  updateMetrics(syncResults, checkStartTime) {
    const checkDuration = Date.now() - checkStartTime;
    
    this.metrics.totalChecks++;
    this.metrics.issuesDetected += syncResults.sync_issues.length;
    this.metrics.autoFixesApplied += syncResults.auto_fixes_applied;
    this.metrics.criticalIssues += syncResults.sync_issues.filter(i => i.severity === 'critical').length;
    this.metrics.warningIssues += syncResults.sync_issues.filter(i => i.severity === 'warning').length;
    
    // Calculate rolling average check duration
    this.metrics.averageCheckDuration = Math.round(
      (this.metrics.averageCheckDuration * (this.metrics.totalChecks - 1) + checkDuration) / this.metrics.totalChecks
    );
  }

  /**
   * Process and manage alerts based on monitoring results
   * @param {Object} syncResults Synchronization results
   * @param {string} correlationId Correlation ID
   */
  async processAlerts(syncResults, correlationId) {
    try {
      // Generate new alerts
      const newAlerts = await StatusSynchronizationService.createSyncAlerts(syncResults);
      
      // Ensure newAlerts is an array
      const alertsArray = Array.isArray(newAlerts) ? newAlerts : [];
      
      // Process each new alert
      for (const alert of alertsArray) {
        await this.processAlert(alert, syncResults, correlationId);
      }

      // Clean up resolved alerts
      this.cleanupResolvedAlerts(syncResults);
    } catch (error) {
      logError('STATUS_SYNC_MONITOR', 'Error processing alerts', {
        error: error.message,
        correlation_id: correlationId
      });
    }
  }

  /**
   * Process a single alert
   * @param {Object} alert Alert configuration
   * @param {Object} syncResults Synchronization results
   * @param {string} correlationId Correlation ID
   */
  async processAlert(alert, syncResults, correlationId) {
    // Check if this is a duplicate alert
    const existingAlert = this.alerts.find(a => 
      a.type === alert.type && 
      a.priority === alert.priority &&
      Date.now() - a.created_at < 300000 // 5 minutes
    );

    if (existingAlert) {
      // Update existing alert
      existingAlert.count = (existingAlert.count || 1) + 1;
      existingAlert.last_occurrence = Date.now();
      existingAlert.updated_at = Date.now();
      return;
    }

    // Create new alert
    const newAlert = {
      ...alert,
      id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      created_at: Date.now(),
      updated_at: Date.now(),
      count: 1,
      correlation_id: correlationId,
      resolved: false
    };

    this.alerts.push(newAlert);

    // Log the alert
    const logLevel = alert.priority === 'high' ? 'error' : 'info';
    const logFunction = logLevel === 'error' ? logError : logInfo;
    
    logFunction('STATUS_SYNC_ALERT', `${alert.priority.toUpperCase()} ALERT: ${alert.title}`, {
      alert_id: newAlert.id,
      alert_type: alert.type,
      priority: alert.priority,
      description: alert.description,
      affected_systems: alert.affected_systems,
      recommended_action: alert.recommended_action,
      correlation_id: correlationId
    });

    DataFlowLogger.logWithCorrelation(correlationId, 'sync_monitor', 'ALERT_CREATED', `Alert created: ${alert.title}`, {
      alert_id: newAlert.id,
      alert_type: alert.type,
      priority: alert.priority,
      affected_systems: alert.affected_systems
    });

    // Send real-time notification if WebSocket is available
    this.sendAlertNotification(newAlert);
  }

  /**
   * Clean up resolved alerts
   * @param {Object} syncResults Current synchronization results
   */
  cleanupResolvedAlerts(syncResults) {
    const currentIssueTypes = new Set(syncResults.sync_issues.map(i => i.type));
    const resolvedAlerts = [];

    this.alerts = this.alerts.filter(alert => {
      // Check if alert is still relevant
      const isStillRelevant = this.isAlertStillRelevant(alert, currentIssueTypes, syncResults);
      
      if (!isStillRelevant) {
        alert.resolved = true;
        alert.resolved_at = Date.now();
        resolvedAlerts.push(alert);
        return false;
      }
      
      return true;
    });

    // Log resolved alerts
    for (const resolvedAlert of resolvedAlerts) {
      logInfo('STATUS_SYNC_ALERT', `Alert resolved: ${resolvedAlert.title}`, {
        alert_id: resolvedAlert.id,
        alert_type: resolvedAlert.type,
        duration_minutes: Math.round((resolvedAlert.resolved_at - resolvedAlert.created_at) / 60000),
        occurrence_count: resolvedAlert.count
      });

      // Move to alert history
      this.alertHistory.push(resolvedAlert);
    }

    // Limit alert history size
    if (this.alertHistory.length > 100) {
      this.alertHistory = this.alertHistory.slice(-50);
    }
  }

  /**
   * Check if an alert is still relevant
   * @param {Object} alert Alert to check
   * @param {Set} currentIssueTypes Current issue types
   * @param {Object} syncResults Current sync results
   * @returns {boolean} Whether alert is still relevant
   */
  isAlertStillRelevant(alert, currentIssueTypes, syncResults) {
    switch (alert.type) {
      case 'critical_sync_issues':
        return syncResults.sync_issues.filter(i => i.severity === 'critical').length > 0;
      
      case 'qr_shift_protection_violation':
        return currentIssueTypes.has('qr_shift_protection_violation');
      
      case 'driver_capture_failures':
        return currentIssueTypes.has('trip_driver_capture_failure');
      
      case 'auto_fixes_applied':
        // This type of alert is informational and resolves after one cycle
        return false;
      
      default:
        // For unknown alert types, check if the specific issue type still exists
        return currentIssueTypes.has(alert.type);
    }
  }

  /**
   * Send alert notification via WebSocket if available
   * @param {Object} alert Alert to send
   */
  sendAlertNotification(alert) {
    try {
      // Try to get WebSocket instance (if available)
      const websocket = require('../websocket');
      if (websocket && websocket.broadcast) {
        websocket.broadcast('status_sync_alert', {
          alert_id: alert.id,
          type: alert.type,
          priority: alert.priority,
          title: alert.title,
          description: alert.description,
          affected_systems: alert.affected_systems,
          recommended_action: alert.recommended_action,
          created_at: alert.created_at
        });
      }
    } catch (error) {
      // WebSocket not available or error sending - continue without notification
      logDebug('STATUS_SYNC_MONITOR', 'Could not send WebSocket notification for alert', {
        alert_id: alert.id,
        error: error.message
      });
    }
  }

  /**
   * Handle monitoring errors with exponential backoff
   * @param {Error} error The error that occurred
   * @param {string} correlationId Correlation ID
   * @param {number} checkStartTime Check start timestamp
   */
  handleMonitoringError(error, correlationId, checkStartTime) {
    this.consecutiveErrors++;
    
    logError('STATUS_SYNC_MONITOR', 'Monitoring check failed', {
      error: error.message,
      consecutive_errors: this.consecutiveErrors,
      correlation_id: correlationId,
      check_duration_ms: Date.now() - checkStartTime
    });

    DataFlowLogger.logWithCorrelation(correlationId, 'sync_monitor', 'CHECK_ERROR', 'Status synchronization monitoring check failed', {
      error: error.message,
      consecutive_errors: this.consecutiveErrors
    });

    // Create error alert if too many consecutive errors
    if (this.consecutiveErrors >= this.maxConsecutiveErrors) {
      const errorAlert = {
        type: 'monitoring_system_failure',
        priority: 'high',
        title: `Status Synchronization Monitor Failing (${this.consecutiveErrors} consecutive errors)`,
        description: `The status synchronization monitoring system has failed ${this.consecutiveErrors} consecutive times. Last error: ${error.message}`,
        affected_systems: ['monitoring_framework'],
        recommended_action: 'Check system logs and restart monitoring service if necessary',
        auto_executable: false
      };

      this.processAlert(errorAlert, { sync_issues: [] }, correlationId);
    }

    // Implement exponential backoff
    if (this.consecutiveErrors > 3) {
      const backoffDelay = Math.min(this.checkInterval * Math.pow(2, this.consecutiveErrors - 3), 300000); // Max 5 minutes
      
      logInfo('STATUS_SYNC_MONITOR', `Implementing exponential backoff: ${backoffDelay}ms`, {
        consecutive_errors: this.consecutiveErrors,
        normal_interval: this.checkInterval,
        backoff_delay: backoffDelay
      });

      // Temporarily increase check interval
      if (this.monitoringInterval) {
        clearInterval(this.monitoringInterval);
        this.monitoringInterval = setInterval(() => {
          this.performMonitoringCheck();
        }, backoffDelay);
      }
    }
  }

  /**
   * Force a monitoring check (for manual triggers)
   * @returns {Promise<Object>} Monitoring results
   */
  async forceCheck() {
    logInfo('STATUS_SYNC_MONITOR', 'Manual monitoring check requested');
    
    const checkStartTime = Date.now();
    const correlationId = DataFlowLogger.createCorrelationId('sync_monitor_manual');

    try {
      const syncResults = await StatusSynchronizationService.monitorStatusSynchronization();
      
      // Update metrics
      this.updateMetrics(syncResults, checkStartTime);
      
      // Process alerts
      await this.processAlerts(syncResults, correlationId);
      
      // Reset consecutive errors on successful check
      this.consecutiveErrors = 0;
      
      const duration = Date.now() - checkStartTime;
      
      return {
        success: true,
        results: syncResults,
        duration_ms: duration,
        correlation_id: correlationId
      };
    } catch (error) {
      this.handleMonitoringError(error, correlationId, checkStartTime);
      throw error;
    }
  }

  /**
   * Get current monitoring status and metrics
   * @returns {Object} Current status
   */
  getStatus() {
    return {
      is_running: this.isRunning,
      check_interval_ms: this.checkInterval,
      last_check: this.lastCheck,
      consecutive_errors: this.consecutiveErrors,
      metrics: {
        ...this.metrics,
        uptime_minutes: Math.round((Date.now() - this.metrics.uptime) / 60000)
      },
      active_alerts: this.alerts.length,
      alert_history_count: this.alertHistory.length,
      alerts: this.alerts.map(alert => ({
        id: alert.id,
        type: alert.type,
        priority: alert.priority,
        title: alert.title,
        created_at: alert.created_at,
        count: alert.count,
        age_minutes: Math.round((Date.now() - alert.created_at) / 60000)
      }))
    };
  }

  /**
   * Get detailed alert information
   * @param {string} alertId Optional alert ID to get specific alert
   * @returns {Object} Alert information
   */
  getAlerts(alertId = null) {
    if (alertId) {
      const alert = this.alerts.find(a => a.id === alertId) || 
                   this.alertHistory.find(a => a.id === alertId);
      return alert || null;
    }

    return {
      active_alerts: this.alerts,
      alert_history: this.alertHistory.slice(-20), // Last 20 resolved alerts
      summary: {
        total_active: this.alerts.length,
        high_priority: this.alerts.filter(a => a.priority === 'high').length,
        medium_priority: this.alerts.filter(a => a.priority === 'medium').length,
        low_priority: this.alerts.filter(a => a.priority === 'low').length,
        total_resolved: this.alertHistory.length
      }
    };
  }

  /**
   * Reset consecutive error count (called after successful check)
   */
  resetErrorCount() {
    if (this.consecutiveErrors > 0) {
      logInfo('STATUS_SYNC_MONITOR', 'Monitoring recovered, resetting error count', {
        previous_consecutive_errors: this.consecutiveErrors
      });
      
      this.consecutiveErrors = 0;
      
      // Reset to normal check interval
      if (this.monitoringInterval) {
        clearInterval(this.monitoringInterval);
        this.monitoringInterval = setInterval(() => {
          this.performMonitoringCheck();
        }, this.checkInterval);
      }
    }
  }
}

// Create singleton instance
const statusSyncMonitor = new StatusSynchronizationMonitor();

module.exports = statusSyncMonitor;