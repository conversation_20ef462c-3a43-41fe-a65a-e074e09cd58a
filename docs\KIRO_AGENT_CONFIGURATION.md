# Kiro Agent Configuration Guide

This guide provides instructions for configuring the Kiro agent for optimal performance with the Hauling QR Trip Management System.

## Overview

Ki<PERSON> is an AI assistant and IDE built to assist developers working on the Hauling QR Trip Management System. Proper configuration ensures optimal performance, especially when working with complex features like multi-location workflows, shift management, and system health monitoring.

## Model Selection

The Kiro agent uses Claude models for AI assistance. The model can be configured in the Kiro user settings:

```json
"kiroAgent.agentModelSelection": "CLAUDE_SONNET_4_20250514_V1_0"
```

### Available Models

- `CLAUDE_SONNET_4_20250514_V1_0` (Latest Sonnet model, updated May 2025)
  - Recommended for complex development tasks
  - Better understanding of multi-location workflows
  - Improved code generation for shift management features
  - Enhanced troubleshooting capabilities

- `CLAUDE_3_7_SONNET_20250219_V1_0` (Previous Sonnet model, February 2025)
  - Stable performance for most development tasks
  - May require more detailed instructions for complex features

## Configuration Settings

### Debug Logs

Enable debug logs for detailed information about Kiro agent operations:

```json
"kiroAgent.enableDebugLogs": true
```

### Tab Autocomplete

Enable tab autocomplete for improved code completion:

```json
"kiroAgent.enableTabAutocomplete": true
```

### Trusted Commands

Configure trusted commands that Kiro can execute without confirmation:

```json
"kiroAgent.trustedCommands": [
  "node",
  "npm",
  "git"
]
```

## MCP Integration

Kiro works seamlessly with the Model Context Protocol (MCP) to provide enhanced AI capabilities. For detailed MCP configuration, see the [MCP Integration Guide](MCP_INTEGRATION_GUIDE.md).

## Optimizing for Project Features

### Multi-Location Workflows

When working with multi-location workflows, ensure Kiro has access to:

1. The latest model (CLAUDE_SONNET_4_20250514_V1_0)
2. Relevant steering files in `.kiro/steering/`
3. Workflow documentation in `docs/MULTI_LOCATION_WORKFLOW_IMPLEMENTATION.md`

### Shift Management

For optimal assistance with shift management features:

1. Use the latest Claude model
2. Ensure Kiro has access to `docs/SHIFT_MANAGEMENT_SYSTEM.md`
3. Configure appropriate hooks in `.kiro/hooks/`

### System Health Monitoring

When working on system health monitoring:

1. Use the latest Claude model for improved diagnostic capabilities
2. Ensure Kiro has access to `docs/SYSTEM_HEALTH_MONITORING_GUIDE.md`
3. Configure appropriate hooks for automated health checks

## Troubleshooting

If you encounter issues with the Kiro agent:

1. Verify your model selection is up to date
2. Check that debug logs are enabled for detailed information
3. Ensure trusted commands are properly configured
4. Restart the Kiro agent if performance degrades
5. Check for any conflicts with MCP servers

For MCP-specific issues, refer to the [MCP Integration Guide](MCP_INTEGRATION_GUIDE.md).