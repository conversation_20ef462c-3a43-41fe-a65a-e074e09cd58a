const axios = require('axios');

/**
 * Test suite for the driver connect endpoint
 * Tests the /api/driver/connect endpoint with various scenarios
 */

// Test the driver connect endpoint
async function testDriverConnect() {
  const baseUrl = 'http://localhost:5000';
  
  // Sample driver QR data (you'll need to adjust this based on your actual data)
  const driverQrData = {
    id: "DRV_001_20250727",
    driver_id: 1,
    employee_id: "EMP001",
    generated_date: "2025-07-27T03:00:00.000Z"
  };
  
  // Sample truck QR data
  const truckQrData = {
    id: "T001",
    type: "truck"
  };
  
  try {
    console.log('Testing driver connect endpoint...');
    
    const response = await axios.post(`${baseUrl}/api/driver/connect`, {
      driver_qr_data: driverQrData,
      truck_qr_data: truckQrData,
      action: 'check_out' // Test check out to trigger the date parsing code
    });
    
    console.log('Success!');
    console.log('Response:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    if (error.response) {
      console.log('Error response:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.log('Error:', error.message);
    }
  }
}

// Run the test
testDriverConnect().then(() => {
  console.log('Test completed.');
}).catch(error => {
  console.error('Test failed:', error);
});