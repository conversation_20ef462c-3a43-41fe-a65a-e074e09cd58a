--
-- PostgreSQL database dump
--

-- Dumped from database version 17.4
-- Dumped by pg_dump version 17.4

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: pg_trgm; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS pg_trgm WITH SCHEMA public;


--
-- Name: EXTENSION pg_trgm; Type: COMMENT; Schema: -; Owner: -
--

COMMENT ON EXTENSION pg_trgm IS 'text similarity measurement and index searching based on trigrams';


--
-- Name: approval_status; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.approval_status AS ENUM (
    'pending',
    'approved',
    'rejected'
);


--
-- Name: assignment_status; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.assignment_status AS ENUM (
    'pending_approval',
    'assigned',
    'in_progress',
    'completed',
    'cancelled'
);


--
-- Name: driver_status; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.driver_status AS ENUM (
    'active',
    'inactive',
    'on_leave',
    'terminated'
);


--
-- Name: location_type; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.location_type AS ENUM (
    'loading',
    'unloading',
    'checkpoint'
);


--
-- Name: recurrence_pattern; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.recurrence_pattern AS ENUM (
    'single',
    'daily',
    'weekly',
    'weekdays',
    'weekends',
    'custom'
);


--
-- Name: scan_type; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.scan_type AS ENUM (
    'location_scan',
    'truck_scan',
    'loading_start',
    'loading_end',
    'unloading_start',
    'unloading_end'
);


--
-- Name: shift_status; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.shift_status AS ENUM (
    'scheduled',
    'active',
    'completed',
    'cancelled'
);


--
-- Name: shift_type; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.shift_type AS ENUM (
    'day',
    'night',
    'custom'
);


--
-- Name: trip_status; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.trip_status AS ENUM (
    'assigned',
    'loading_start',
    'loading_end',
    'unloading_start',
    'unloading_end',
    'trip_completed',
    'exception_pending',
    'cancelled',
    'stopped',
    'exception_triggered'
);


--
-- Name: truck_status; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.truck_status AS ENUM (
    'active',
    'inactive',
    'maintenance',
    'retired'
);


--
-- Name: user_role; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.user_role AS ENUM (
    'admin',
    'supervisor',
    'operator'
);


--
-- Name: add_sample_shift_data(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.add_sample_shift_data() RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_inserted_count INTEGER := 0;
BEGIN
    -- Only add sample data if no shifts exist
    IF NOT EXISTS (SELECT 1 FROM driver_shifts LIMIT 1) THEN
        -- Insert sample shifts for existing trucks (if they exist)
        INSERT INTO driver_shifts (truck_id, driver_id, shift_type, shift_date, start_time, end_time, status)
        SELECT
            dt.id as truck_id,
            d.id as driver_id,
            'day' as shift_type,
            CURRENT_DATE as shift_date,
            '06:00:00'::TIME as start_time,
            '18:00:00'::TIME as end_time,
            'scheduled' as status
        FROM dump_trucks dt
        CROSS JOIN drivers d
        WHERE dt.status = 'active'
          AND d.status = 'active'
        LIMIT 3; -- Limit to prevent too much sample data
        
        GET DIAGNOSTICS v_inserted_count = ROW_COUNT;
        
        -- Log the sample data creation
        PERFORM log_system_event(
            'SAMPLE_DATA_CREATION',
            'Sample shift data created for testing',
            jsonb_build_object(
                'shifts_created', v_inserted_count,
                'creation_time', CURRENT_TIMESTAMP
            )
        );
    END IF;
    
    RETURN v_inserted_count;
END;
$$;


--
-- Name: FUNCTION add_sample_shift_data(); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.add_sample_shift_data() IS 'Add sample shift data for testing purposes (only if no shifts exist)';


--
-- Name: auto_activate_shifts(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.auto_activate_shifts() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    -- Auto-activate shifts that should be starting now
    UPDATE driver_shifts 
    SET status = 'active', updated_at = CURRENT_TIMESTAMP
    WHERE status = 'scheduled'
        AND shift_date = CURRENT_DATE
        AND CURRENT_TIME >= start_time
        AND CURRENT_TIME < CASE 
            WHEN end_time < start_time 
            THEN end_time + interval '24 hours'
            ELSE end_time 
        END;
    
    RETURN NULL;
END;
$$;


--
-- Name: auto_activate_shifts_enhanced(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.auto_activate_shifts_enhanced() RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_activated_count INTEGER := 0;
    v_shift_record RECORD;
BEGIN
    -- Get all shifts that should be activated now
    FOR v_shift_record IN
        SELECT ds.id, ds.truck_id
        FROM driver_shifts ds
        WHERE ds.status = 'scheduled'
          AND is_shift_active_on_date(ds.id, CURRENT_DATE)
          AND CURRENT_TIME >= ds.start_time
          AND CURRENT_TIME < CASE 
              WHEN ds.end_time < ds.start_time 
              THEN ds.end_time + interval '24 hours'
              ELSE ds.end_time 
          END
    LOOP
        -- Deactivate any currently active shifts for this truck
        UPDATE driver_shifts 
        SET status = 'completed', updated_at = CURRENT_TIMESTAMP
        WHERE truck_id = v_shift_record.truck_id 
          AND status = 'active'
          AND id != v_shift_record.id;
        
        -- Activate the new shift
        UPDATE driver_shifts 
        SET status = 'active', updated_at = CURRENT_TIMESTAMP
        WHERE id = v_shift_record.id;
        
        v_activated_count := v_activated_count + 1;
    END LOOP;
    
    RETURN v_activated_count;
END;
$$;


--
-- Name: FUNCTION auto_activate_shifts_enhanced(); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.auto_activate_shifts_enhanced() IS 'Enhanced auto-activation with date range and recurrence support';


--
-- Name: auto_capture_trip_driver(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.auto_capture_trip_driver() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
    driver_info RECORD;
    truck_id INTEGER;
BEGIN
    -- Get truck_id from assignment
    SELECT a.truck_id INTO truck_id
    FROM assignments a
    WHERE a.id = NEW.assignment_id;
    
    -- Only capture driver info if not already set and trip is starting
    IF NEW.performed_by_driver_id IS NULL AND NEW.loading_start_time IS NOT NULL THEN
        -- Capture active driver at the time of loading start
        SELECT * INTO driver_info
        FROM capture_active_driver_for_trip(truck_id, NEW.loading_start_time);
        
        IF FOUND THEN
            NEW.performed_by_driver_id := driver_info.driver_id;
            NEW.performed_by_driver_name := driver_info.driver_name;
            NEW.performed_by_employee_id := driver_info.employee_id;
            NEW.performed_by_shift_id := driver_info.shift_id;
            NEW.performed_by_shift_type := driver_info.shift_type;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$;


--
-- Name: auto_complete_shifts_enhanced(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.auto_complete_shifts_enhanced() RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_completed_count INTEGER := 0;
BEGIN
    -- Complete shifts that have passed their end time
    UPDATE driver_shifts 
    SET status = 'completed', updated_at = CURRENT_TIMESTAMP
    WHERE status = 'active'
      AND (
        -- Single date shifts
        (recurrence_pattern = 'single' AND shift_date = CURRENT_DATE)
        OR
        -- Date range shifts that are active today
        (recurrence_pattern != 'single' AND is_shift_active_on_date(id, CURRENT_DATE))
      )
      AND CURRENT_TIME > CASE 
          WHEN end_time < start_time 
          THEN end_time + interval '24 hours'
          ELSE end_time 
      END;
    
    GET DIAGNOSTICS v_completed_count = ROW_COUNT;
    RETURN v_completed_count;
END;
$$;


--
-- Name: FUNCTION auto_complete_shifts_enhanced(); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.auto_complete_shifts_enhanced() IS 'Enhanced auto-completion with date range and recurrence support';


--
-- Name: auto_populate_driver_from_shift(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.auto_populate_driver_from_shift() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_current_driver_id INTEGER;
BEGIN
    -- Only auto-populate if driver_id is NULL
    IF NEW.driver_id IS NULL THEN
        -- Get current active driver for the truck
        SELECT ds.driver_id INTO v_current_driver_id
        FROM driver_shifts ds
        WHERE ds.truck_id = NEW.truck_id
          AND ds.status = 'active'
          AND ds.shift_date = CURRENT_DATE
          AND CURRENT_TIME BETWEEN ds.start_time AND 
              CASE 
                WHEN ds.end_time < ds.start_time 
                THEN ds.end_time + interval '24 hours'
                ELSE ds.end_time 
              END
        ORDER BY ds.created_at DESC
        LIMIT 1;
        
        -- If we found an active driver, use it
        IF v_current_driver_id IS NOT NULL THEN
            NEW.driver_id := v_current_driver_id;
            
            -- Add note about auto-assignment
            IF NEW.notes IS NULL OR NEW.notes = '' THEN
                NEW.notes := '[Auto-assigned driver from active shift]';
            ELSE
                NEW.notes := NEW.notes || ' [Auto-assigned driver from active shift]';
            END IF;
        ELSE
            -- No active driver found, leave driver_id as NULL
            -- Add note about missing driver
            IF NEW.notes IS NULL OR NEW.notes = '' THEN
                NEW.notes := '[No active driver found - manual assignment required]';
            ELSE
                NEW.notes := NEW.notes || ' [No active driver found - manual assignment required]';
            END IF;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$;


--
-- Name: calculate_shift_end_timestamp(date, time without time zone, boolean); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.calculate_shift_end_timestamp(end_date date, end_time time without time zone, is_overnight boolean) RETURNS timestamp without time zone
    LANGUAGE plpgsql IMMUTABLE
    AS $$
BEGIN
    IF is_overnight THEN
        RETURN (end_date + INTERVAL '1 day')::DATE + end_time;
    ELSE
        RETURN end_date::DATE + end_time;
    END IF;
END;
$$;


--
-- Name: calculate_shift_status(boolean, boolean); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.calculate_shift_status(within_date_range boolean, within_time_window boolean) RETURNS text
    LANGUAGE plpgsql IMMUTABLE
    AS $$
BEGIN
    CASE 
        WHEN within_date_range AND within_time_window THEN
            RETURN 'active';
        WHEN within_date_range AND NOT within_time_window THEN
            RETURN 'scheduled';
        ELSE
            RETURN 'scheduled';
    END CASE;
END;
$$;


--
-- Name: calculate_trip_durations(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.calculate_trip_durations() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
  -- Calculate loading duration
  IF NEW.loading_start_time IS NOT NULL AND NEW.loading_end_time IS NOT NULL THEN
    NEW.loading_duration_minutes := EXTRACT(EPOCH FROM (NEW.loading_end_time - NEW.loading_start_time)) / 60;
  END IF;
  
  -- Calculate travel duration
  IF NEW.loading_end_time IS NOT NULL AND NEW.unloading_start_time IS NOT NULL THEN
    NEW.travel_duration_minutes := EXTRACT(EPOCH FROM (NEW.unloading_start_time - NEW.loading_end_time)) / 60;
  END IF;
  
  -- Calculate unloading duration
  IF NEW.unloading_start_time IS NOT NULL AND NEW.unloading_end_time IS NOT NULL THEN
    NEW.unloading_duration_minutes := EXTRACT(EPOCH FROM (NEW.unloading_end_time - NEW.unloading_start_time)) / 60;
  END IF;
  
  -- Calculate total duration
  IF NEW.loading_start_time IS NOT NULL AND NEW.trip_completed_time IS NOT NULL THEN
    NEW.total_duration_minutes := EXTRACT(EPOCH FROM (NEW.trip_completed_time - NEW.loading_start_time)) / 60;
  END IF;
  
  RETURN NEW;
END;
$$;


--
-- Name: capture_active_driver_for_trip(integer, timestamp without time zone); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.capture_active_driver_for_trip(p_truck_id integer, p_timestamp timestamp without time zone DEFAULT CURRENT_TIMESTAMP) RETURNS TABLE(driver_id integer, driver_name character varying, employee_id character varying, shift_id integer, shift_type public.shift_type)
    LANGUAGE plpgsql
    AS $$
      BEGIN
        RETURN QUERY
        SELECT 
          enh.driver_id,
          enh.driver_name,
          enh.employee_id,
          enh.shift_id,
          enh.shift_type
        FROM capture_active_driver_for_trip_enhanced(p_truck_id, p_timestamp) enh;
      END;
      $$;


--
-- Name: FUNCTION capture_active_driver_for_trip(p_truck_id integer, p_timestamp timestamp without time zone); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.capture_active_driver_for_trip(p_truck_id integer, p_timestamp timestamp without time zone) IS 'Captures the active driver for a truck at a specific timestamp for trip history';


--
-- Name: capture_active_driver_for_trip_enhanced(integer, timestamp without time zone); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.capture_active_driver_for_trip_enhanced(p_truck_id integer, p_timestamp timestamp without time zone DEFAULT CURRENT_TIMESTAMP) RETURNS TABLE(driver_id integer, driver_name character varying, employee_id character varying, shift_id integer, shift_type public.shift_type, display_type public.shift_type)
    LANGUAGE plpgsql
    AS $$
      BEGIN
        RETURN QUERY
        SELECT 
          ds.driver_id,
          d.full_name as driver_name,
          d.employee_id,
          ds.id as shift_id,
          ds.shift_type,
          COALESCE(ds.display_type, ds.shift_type) as display_type
        FROM driver_shifts ds
        JOIN drivers d ON ds.driver_id = d.id
        WHERE ds.truck_id = p_truck_id
          AND ds.status = 'active'
          AND (
            -- Single date shifts (backward compatibility)
            (ds.recurrence_pattern = 'single' AND ds.shift_date = p_timestamp::date)
            OR
            -- Date range shifts with recurrence patterns
            (ds.recurrence_pattern != 'single' AND p_timestamp::date BETWEEN ds.start_date AND ds.end_date
              AND (
                (ds.recurrence_pattern = 'daily')
                OR
                (ds.recurrence_pattern = 'weekly' AND EXTRACT(DOW FROM p_timestamp::date) = EXTRACT(DOW FROM ds.start_date))
                OR
                (ds.recurrence_pattern = 'weekdays' AND EXTRACT(DOW FROM p_timestamp::date) BETWEEN 1 AND 5)
                OR
                (ds.recurrence_pattern = 'weekends' AND EXTRACT(DOW FROM p_timestamp::date) IN (0, 6))
                OR
                (ds.recurrence_pattern = 'custom')
              )
            )
          )
          AND p_timestamp::time BETWEEN ds.start_time AND 
              CASE 
                WHEN ds.end_time < ds.start_time 
                THEN ds.end_time + interval '24 hours'
                ELSE ds.end_time 
              END
        ORDER BY ds.created_at DESC
        LIMIT 1;
      END;
      $$;


--
-- Name: capture_active_driver_for_trip_enhanced_test(integer, timestamp without time zone); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.capture_active_driver_for_trip_enhanced_test(p_truck_id integer, p_timestamp timestamp without time zone DEFAULT CURRENT_TIMESTAMP) RETURNS TABLE(driver_id integer, driver_name character varying, employee_id character varying, shift_id integer, shift_type public.shift_type, source character varying)
    LANGUAGE plpgsql
    AS $$
        BEGIN
          -- First try to get active driver
          RETURN QUERY
          SELECT 
            ds.driver_id,
            d.full_name as driver_name,
            d.employee_id,
            ds.id as shift_id,
            ds.shift_type,
            'active'::VARCHAR(20) as source
          FROM driver_shifts ds
          JOIN drivers d ON ds.driver_id = d.id
          WHERE ds.truck_id = p_truck_id
            AND ds.status = 'active'
            AND (
              (ds.start_date IS NULL AND ds.shift_date = p_timestamp::date) OR
              (ds.start_date IS NOT NULL AND p_timestamp::date BETWEEN ds.start_date AND ds.end_date)
            )
            AND p_timestamp::time BETWEEN ds.start_time AND 
                CASE 
                  WHEN ds.end_time < ds.start_time 
                  THEN ds.end_time + interval '24 hours'
                  ELSE ds.end_time 
                END
          ORDER BY ds.created_at DESC
          LIMIT 1;
          
          -- If no active driver found, get most recent completed shift
          IF NOT FOUND THEN
            RETURN QUERY
            SELECT 
              ds.driver_id,
              d.full_name as driver_name,
              d.employee_id,
              ds.id as shift_id,
              ds.shift_type,
              'fallback'::VARCHAR(20) as source
            FROM driver_shifts ds
            JOIN drivers d ON ds.driver_id = d.id
            WHERE ds.truck_id = p_truck_id
              AND ds.status = 'completed'
              AND (
                (ds.shift_type = 'day' AND p_timestamp::time BETWEEN '06:00:00' AND '18:00:00') OR
                (ds.shift_type = 'night' AND (p_timestamp::time >= '18:00:00' OR p_timestamp::time <= '06:00:00'))
              )
            ORDER BY 
              CASE 
                WHEN ds.end_date IS NOT NULL THEN ds.end_date
                ELSE ds.shift_date
              END DESC,
              ds.created_at DESC
            LIMIT 1;
          END IF;
        END;
        $$;


--
-- Name: check_shift_status_consistency(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.check_shift_status_consistency() RETURNS TABLE(shift_id integer, current_status public.shift_status, expected_status text, truck_id integer, driver_id integer, shift_type public.shift_type, start_date date, end_date date, start_time time without time zone, end_time time without time zone, issue_description text)
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_shift RECORD;
    v_expected_status TEXT;
BEGIN
    -- Check all shifts for status consistency
    FOR v_shift IN
        SELECT 
            ds.id, 
            ds.truck_id, 
            ds.driver_id, 
            ds.shift_type, 
            ds.status, 
            ds.start_date, 
            ds.end_date, 
            ds.start_time, 
            ds.end_time
        FROM driver_shifts ds
        WHERE ds.status IN ('scheduled', 'active', 'completed')
    LOOP
        -- Get the expected status using the 2-parameter function
        v_expected_status := evaluate_shift_status(v_shift.id, CURRENT_TIMESTAMP);
        
        -- If there's a mismatch, report it
        IF v_expected_status != v_shift.status::TEXT THEN
            RETURN QUERY SELECT 
                v_shift.id,
                v_shift.status,
                v_expected_status,
                v_shift.truck_id,
                v_shift.driver_id,
                v_shift.shift_type,
                v_shift.start_date,
                v_shift.end_date,
                v_shift.start_time,
                v_shift.end_time,
                CASE 
                    WHEN v_shift.status = 'completed' AND v_expected_status != 'completed' THEN 
                        'Shift incorrectly marked as completed'
                    WHEN v_shift.status = 'scheduled' AND v_expected_status = 'active' THEN 
                        'Shift should be active but is still scheduled'
                    WHEN v_shift.status = 'active' AND v_expected_status = 'completed' THEN 
                        'Shift should be completed but is still active'
                    ELSE 
                        'Status mismatch detected'
                END;
        END IF;
    END LOOP;
END;
$$;


--
-- Name: FUNCTION check_shift_status_consistency(); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.check_shift_status_consistency() IS 'Checks for shift status inconsistencies - uses 2-parameter evaluate_shift_status';


--
-- Name: classify_shift_by_time(time without time zone, time without time zone); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.classify_shift_by_time(p_start_time time without time zone, p_end_time time without time zone) RETURNS public.shift_type
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_start_minutes INTEGER;
    v_end_minutes INTEGER;
    v_tolerance INTEGER := 30; -- 30 minutes tolerance
BEGIN
    -- Convert times to minutes since midnight
    v_start_minutes := EXTRACT(HOUR FROM p_start_time) * 60 + EXTRACT(MINUTE FROM p_start_time);
    v_end_minutes := EXTRACT(HOUR FROM p_end_time) * 60 + EXTRACT(MINUTE FROM p_end_time);
    
    -- Check for standard day shift patterns (6AM-6PM, 7AM-7PM, 8AM-5PM, etc.)
    IF (
        (ABS(v_start_minutes - 360) <= v_tolerance AND ABS(v_end_minutes - 1080) <= v_tolerance) OR -- 6AM-6PM
        (ABS(v_start_minutes - 420) <= v_tolerance AND ABS(v_end_minutes - 1140) <= v_tolerance) OR -- 7AM-7PM
        (ABS(v_start_minutes - 480) <= v_tolerance AND ABS(v_end_minutes - 1020) <= v_tolerance) OR -- 8AM-5PM
        (ABS(v_start_minutes - 360) <= v_tolerance AND ABS(v_end_minutes - 1020) <= v_tolerance) OR -- 6AM-5PM
        (ABS(v_start_minutes - 420) <= v_tolerance AND ABS(v_end_minutes - 1080) <= v_tolerance) OR -- 7AM-6PM
        (ABS(v_start_minutes - 480) <= v_tolerance AND ABS(v_end_minutes - 960) <= v_tolerance) OR  -- 8AM-4PM
        (ABS(v_start_minutes - 540) <= v_tolerance AND ABS(v_end_minutes - 1020) <= v_tolerance)    -- 9AM-5PM
    ) THEN
        RETURN 'day';
    END IF;
    
    -- Check for standard night shift patterns (6PM-6AM, 7PM-7AM, etc.)
    -- Handle midnight crossing
    IF (
        (ABS(v_start_minutes - 1080) <= v_tolerance AND (v_end_minutes < v_start_minutes AND ABS(v_end_minutes - 360) <= v_tolerance)) OR -- 6PM-6AM
        (ABS(v_start_minutes - 1140) <= v_tolerance AND (v_end_minutes < v_start_minutes AND ABS(v_end_minutes - 420) <= v_tolerance)) OR -- 7PM-7AM
        (ABS(v_start_minutes - 1320) <= v_tolerance AND (v_end_minutes < v_start_minutes AND ABS(v_end_minutes - 360) <= v_tolerance)) OR -- 10PM-6AM
        (ABS(v_start_minutes - 1380) <= v_tolerance AND (v_end_minutes < v_start_minutes AND ABS(v_end_minutes - 420) <= v_tolerance)) OR -- 11PM-7AM
        (ABS(v_start_minutes - 1200) <= v_tolerance AND (v_end_minutes < v_start_minutes AND ABS(v_end_minutes - 480) <= v_tolerance))    -- 8PM-8AM
    ) THEN
        RETURN 'night';
    END IF;
    
    -- If no standard pattern matches, return custom
    RETURN 'custom';
END;
$$;


--
-- Name: FUNCTION classify_shift_by_time(p_start_time time without time zone, p_end_time time without time zone); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.classify_shift_by_time(p_start_time time without time zone, p_end_time time without time zone) IS 'Intelligently classify shift type based on time patterns';


--
-- Name: cleanup_shift_system(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.cleanup_shift_system() RETURNS text
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_cleaned_count INTEGER := 0;
    v_result TEXT;
BEGIN
    -- Remove any problematic 3-parameter functions
    BEGIN
        DROP FUNCTION IF EXISTS fix_incorrectly_completed_shifts(INTEGER, TIMESTAMP, BOOLEAN);
        DROP FUNCTION IF EXISTS check_shift_status_consistency(INTEGER, TIMESTAMP, BOOLEAN);
        DROP FUNCTION IF EXISTS evaluate_shift_status(INTEGER, TIMESTAMP, BOOLEAN);
        v_cleaned_count := v_cleaned_count + 1;
    EXCEPTION WHEN OTHERS THEN
        -- Functions may not exist, continue
    END;
    
    -- Run auto-activation to fix any status inconsistencies
    PERFORM schedule_auto_activation();
    v_cleaned_count := v_cleaned_count + 1;
    
    v_result := 'Cleanup completed: ' || v_cleaned_count || ' operations performed';
    RAISE NOTICE '%', v_result;
    
    RETURN v_result;
END;
$$;


--
-- Name: FUNCTION cleanup_shift_system(); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.cleanup_shift_system() IS 'Automatic cleanup function to fix common shift system issues';


--
-- Name: complete_shift_manually(integer, integer); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.complete_shift_manually(p_shift_id integer, p_completed_by integer DEFAULT NULL::integer) RETURNS boolean
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_shift_exists BOOLEAN;
BEGIN
    -- Check if shift exists and is active
    SELECT EXISTS(
        SELECT 1 FROM driver_shifts
        WHERE id = p_shift_id AND status = 'active'
    ) INTO v_shift_exists;
    
    IF NOT v_shift_exists THEN
        RETURN FALSE;
    END IF;
    
    -- Manually complete the shift
    UPDATE driver_shifts
    SET status = 'completed',
        updated_at = CURRENT_TIMESTAMP
    WHERE id = p_shift_id;
    
    -- Log the manual completion
    PERFORM log_system_event(
        'SHIFT_MANUAL_COMPLETION',
        'Shift manually completed by user',
        jsonb_build_object(
            'shift_id', p_shift_id,
            'completed_by', p_completed_by,
            'completion_time', CURRENT_TIMESTAMP
        ),
        p_completed_by
    );
    
    RETURN TRUE;
END;
$$;


--
-- Name: FUNCTION complete_shift_manually(p_shift_id integer, p_completed_by integer); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.complete_shift_manually(p_shift_id integer, p_completed_by integer) IS 'Manually complete a shift - no automatic completion allowed';


--
-- Name: create_assignment_with_auto_driver(character varying, integer, integer, integer, date, character varying, integer, text); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.create_assignment_with_auto_driver(p_assignment_code character varying, p_truck_id integer, p_loading_location_id integer, p_unloading_location_id integer, p_assigned_date date DEFAULT CURRENT_DATE, p_priority character varying DEFAULT 'normal'::character varying, p_expected_loads integer DEFAULT 1, p_notes text DEFAULT NULL::text) RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_assignment_id INTEGER;
    v_driver_id INTEGER;
    v_final_notes TEXT;
BEGIN
    -- Get current active driver
    v_driver_id := get_current_active_driver(p_truck_id);
    
    -- Prepare notes
    v_final_notes := COALESCE(p_notes, '');
    IF v_driver_id IS NOT NULL THEN
        v_final_notes := v_final_notes || ' [Driver auto-assigned from active shift]';
    END IF;
    
    -- Create the assignment
    INSERT INTO assignments (
        assignment_code, truck_id, driver_id,
        loading_location_id, unloading_location_id,
        assigned_date, status, priority,
        expected_loads_per_day, notes,
        created_at, updated_at
    ) VALUES (
        p_assignment_code, p_truck_id, v_driver_id,
        p_loading_location_id, p_unloading_location_id,
        p_assigned_date, 'assigned', p_priority,
        p_expected_loads, v_final_notes,
        CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
    ) RETURNING id INTO v_assignment_id;
    
    RETURN v_assignment_id;
END;
$$;


--
-- Name: create_deviation_assignment(integer, integer, integer, integer, character varying, integer); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.create_deviation_assignment(p_truck_id integer, p_driver_id integer, p_loading_location_id integer, p_unloading_location_id integer, p_priority character varying DEFAULT 'normal'::character varying, p_expected_loads integer DEFAULT 1) RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_assignment_id INTEGER;
    v_assignment_code VARCHAR(50);
BEGIN
    -- Check if assignment already exists for today
    SELECT id INTO v_assignment_id
    FROM assignments
    WHERE truck_id = p_truck_id 
      AND driver_id = p_driver_id 
      AND loading_location_id = p_loading_location_id
      AND assigned_date = CURRENT_DATE
      AND status IN ('assigned', 'in_progress')
    LIMIT 1;

    -- If exists, return existing ID
    IF v_assignment_id IS NOT NULL THEN
        RETURN v_assignment_id;
    END IF;

    -- Generate unique assignment code
    v_assignment_code := 'ASG-' || TO_CHAR(CURRENT_TIMESTAMP, 'YYYYMMDD-HH24MISS') || '-AUTO';

    -- Create new assignment
    INSERT INTO assignments (
        assignment_code, truck_id, driver_id, 
        loading_location_id, unloading_location_id, 
        assigned_date, status, priority, 
        expected_loads_per_day, notes, created_at, updated_at
    )
    VALUES (
        v_assignment_code, p_truck_id, p_driver_id,
        p_loading_location_id, p_unloading_location_id,
        CURRENT_DATE, 'assigned', p_priority,
        p_expected_loads, '[Auto-created for route deviation]', 
        CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
    )
    RETURNING id INTO v_assignment_id;

    RETURN v_assignment_id;
END;
$$;


--
-- Name: FUNCTION create_deviation_assignment(p_truck_id integer, p_driver_id integer, p_loading_location_id integer, p_unloading_location_id integer, p_priority character varying, p_expected_loads integer); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.create_deviation_assignment(p_truck_id integer, p_driver_id integer, p_loading_location_id integer, p_unloading_location_id integer, p_priority character varying, p_expected_loads integer) IS 'Creates or returns assignment for route deviation scenarios';


--
-- Name: create_shift_assignment(integer, integer, integer, integer); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.create_shift_assignment(p_truck_id integer, p_shift_id integer, p_loading_location_id integer, p_unloading_location_id integer) RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_assignment_id INTEGER;
    v_driver_id INTEGER;
    v_assignment_code VARCHAR(50);
BEGIN
    -- Get driver from shift
    SELECT driver_id INTO v_driver_id
    FROM driver_shifts
    WHERE id = p_shift_id;
    
    IF v_driver_id IS NULL THEN
        RAISE EXCEPTION 'Invalid shift ID: %', p_shift_id;
    END IF;
    
    -- Generate assignment code
    v_assignment_code := 'SHIFT-' || TO_CHAR(CURRENT_TIMESTAMP, 'YYYYMMDD-HH24MISS') || '-' || p_shift_id;
    
    -- Create assignment
    INSERT INTO assignments (
        assignment_code, truck_id, driver_id,
        loading_location_id, unloading_location_id,
        shift_id, is_shift_assignment,
        assigned_date, status, priority,
        expected_loads_per_day, notes
    ) VALUES (
        v_assignment_code, p_truck_id, v_driver_id,
        p_loading_location_id, p_unloading_location_id,
        p_shift_id, true,
        CURRENT_DATE, 'assigned', 'normal',
        1, 'Auto-created for shift-based assignment'
    ) RETURNING id INTO v_assignment_id;
    
    -- Update shift with assignment reference
    UPDATE driver_shifts 
    SET assignment_id = v_assignment_id,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = p_shift_id;
    
    RETURN v_assignment_id;
END;
$$;


--
-- Name: create_unified_shift(integer, integer, public.shift_type, date, date, time without time zone, time without time zone, public.shift_status, text); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.create_unified_shift(p_truck_id integer, p_driver_id integer, p_shift_type public.shift_type, p_start_date date, p_end_date date, p_start_time time without time zone, p_end_time time without time zone, p_status public.shift_status DEFAULT 'scheduled'::public.shift_status, p_handover_notes text DEFAULT ''::text) RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_shift_id INTEGER;
BEGIN
    INSERT INTO driver_shifts (
        truck_id,
        driver_id,
        shift_type,
        shift_date,      -- Will be auto-set to start_date by trigger
        start_date,
        end_date,
        start_time,
        end_time,
        status,
        handover_notes,
        recurrence_pattern
    ) VALUES (
        p_truck_id,
        p_driver_id,
        p_shift_type,
        p_start_date,    -- This will be synced by trigger
        p_start_date,
        p_end_date,
        p_start_time,
        p_end_time,
        p_status,
        p_handover_notes,
        CASE WHEN p_start_date = p_end_date THEN 'single' ELSE 'custom' END
    ) RETURNING id INTO v_shift_id;
    
    RETURN v_shift_id;
END;
$$;


--
-- Name: debug_shift_status(integer, timestamp with time zone); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.debug_shift_status(p_shift_id integer, p_reference_timestamp timestamp with time zone DEFAULT CURRENT_TIMESTAMP) RETURNS TABLE(shift_id integer, shift_start_date date, shift_end_date date, shift_start_time time without time zone, shift_end_time time without time zone, current_date_val date, current_time_val time without time zone, is_overnight boolean, is_within_date_range boolean, is_within_time_window boolean, shift_end_datetime timestamp without time zone, is_past_completion boolean, calculated_status text)
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_shift RECORD;
    v_current_date DATE;
    v_current_time TIME;
    v_is_overnight BOOLEAN;
    v_is_within_date_range BOOLEAN;
    v_is_within_time_window BOOLEAN;
    v_is_past_completion BOOLEAN;
    v_new_status TEXT;
    v_shift_end_datetime TIMESTAMP;
BEGIN
    -- Get shift details
    SELECT
        id,
        start_date,
        end_date,
        start_time,
        end_time,
        shift_type,
        status,
        recurrence_pattern
    INTO v_shift
    FROM driver_shifts
    WHERE id = p_shift_id;

    IF NOT FOUND THEN
        RETURN;
    END IF;

    -- Extract current date and time from reference timestamp
    v_current_date := p_reference_timestamp::DATE;
    v_current_time := p_reference_timestamp::TIME;

    -- Check if shift spans overnight (night shift logic)
    v_is_overnight := v_shift.end_time < v_shift.start_time;

    -- Enhanced date range validation with unified approach
    v_is_within_date_range := v_current_date BETWEEN v_shift.start_date AND v_shift.end_date;

    -- Enhanced time window validation with proper overnight logic
    IF v_is_overnight THEN
        -- Night shift: Use dual condition logic for overnight spans
        v_is_within_time_window := (v_current_time >= v_shift.start_time OR v_current_time <= v_shift.end_time);
    ELSE
        -- Day shift: Use simple BETWEEN logic for same-day shifts
        v_is_within_time_window := (v_current_time BETWEEN v_shift.start_time AND v_shift.end_time);
    END IF;

    -- Enhanced completion logic with proper overnight handling (FOR DEBUG ONLY)
    IF v_is_overnight THEN
        -- For overnight shifts: would be completed when past end_time on the next day
        v_shift_end_datetime := (v_shift.end_date + INTERVAL '1 day')::DATE + v_shift.end_time;
        v_is_past_completion := p_reference_timestamp > v_shift_end_datetime;
    ELSE
        -- For day shifts: would be completed when past end_time on the same day
        v_shift_end_datetime := v_shift.end_date::DATE + v_shift.end_time;
        v_is_past_completion := p_reference_timestamp > v_shift_end_datetime;
    END IF;

    -- CORRECTED: Apply status rules with MANUAL-ONLY completion
    -- DEBUG NOTE: This shows what the status WOULD be, but completion is MANUAL ONLY
    IF v_is_within_date_range AND v_is_within_time_window THEN
        -- Rule 1: Active - within date range AND within time window
        v_new_status := 'active';
    ELSIF v_is_within_date_range AND NOT v_is_within_time_window THEN
        -- Rule 2: Scheduled - within date range BUT outside time window
        v_new_status := 'scheduled';
    ELSE
        -- Default: Scheduled for future dates
        -- NOTE: Even if past completion time, we return 'scheduled' because completion is MANUAL ONLY
        v_new_status := 'scheduled';
    END IF;

    -- Return all debug information with explicit variable references
    RETURN QUERY SELECT
        v_shift.id::INTEGER,
        v_shift.start_date::DATE,
        v_shift.end_date::DATE,
        v_shift.start_time::TIME,
        v_shift.end_time::TIME,
        v_current_date::DATE,
        v_current_time::TIME,
        v_is_overnight::BOOLEAN,
        v_is_within_date_range::BOOLEAN,
        v_is_within_time_window::BOOLEAN,
        v_shift_end_datetime::TIMESTAMP,
        v_is_past_completion::BOOLEAN,
        v_new_status::TEXT;
END;
$$;


--
-- Name: FUNCTION debug_shift_status(p_shift_id integer, p_reference_timestamp timestamp with time zone); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.debug_shift_status(p_shift_id integer, p_reference_timestamp timestamp with time zone) IS 'Debug function for shift status evaluation - shows calculated status with MANUAL-ONLY completion policy. Even if past completion time, only returns active/scheduled since completion requires manual user action.';


--
-- Name: evaluate_shift_status(integer); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.evaluate_shift_status(p_shift_id integer) RETURNS text
    LANGUAGE plpgsql
    AS $$
BEGIN
    RETURN evaluate_shift_status(p_shift_id, CURRENT_TIMESTAMP);
END;
$$;


--
-- Name: FUNCTION evaluate_shift_status(p_shift_id integer); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.evaluate_shift_status(p_shift_id integer) IS 'Backward compatibility wrapper';


--
-- Name: evaluate_shift_status(integer, timestamp with time zone); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.evaluate_shift_status(p_shift_id integer, p_reference_timestamp timestamp with time zone DEFAULT CURRENT_TIMESTAMP) RETURNS text
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_shift_type TEXT;
    v_start_date DATE;
    v_end_date DATE;
    v_start_time TIME;
    v_end_time TIME;
    v_current_status TEXT;
    v_current_date DATE;
    v_current_time TIME;
    v_is_overnight BOOLEAN;
    v_is_within_date_range BOOLEAN;
    v_is_within_time_window BOOLEAN;
    v_shift_end_datetime TIMESTAMP;
    v_is_past_completion BOOLEAN;
BEGIN
    -- Get shift details
    SELECT 
        shift_type,
        start_date,
        end_date,
        start_time,
        end_time,
        status::TEXT,
        start_time > end_time
    INTO
        v_shift_type,
        v_start_date,
        v_end_date,
        v_start_time,
        v_end_time,
        v_current_status,
        v_is_overnight
    FROM driver_shifts
    WHERE id = p_shift_id;
    
    -- If shift not found or cancelled, return error
    IF v_shift_type IS NULL OR v_current_status = 'cancelled' THEN
        RETURN 'error';
    END IF;
    
    -- If shift is already completed, keep it completed
    IF v_current_status = 'completed' THEN
        RETURN 'completed';
    END IF;
    
    -- Extract date and time from reference timestamp
    v_current_date := p_reference_timestamp::DATE;
    v_current_time := p_reference_timestamp::TIME;
    
    -- Calculate the actual shift end datetime for proper completion logic
    IF v_is_overnight THEN
        -- For overnight shifts: end_time on the day after end_date
        v_shift_end_datetime := (v_end_date + INTERVAL '1 day')::DATE + v_end_time;
    ELSE
        -- For day shifts: end_time on the same day as end_date
        v_shift_end_datetime := v_end_date::DATE + v_end_time;
    END IF;
    
    -- Check if we're past the shift's actual end datetime (AND logic)
    v_is_past_completion := p_reference_timestamp > v_shift_end_datetime;
    
    -- Apply business rules for status determination
    -- Rule 1: Completed - when BOTH end_date AND end_time conditions are met
    -- Rule 2: Active - within date range AND within time window
    -- Rule 3: Scheduled - within date range BUT outside time window
    
    IF v_is_past_completion THEN
        -- Shift has ended (both date and time conditions met)
        RETURN 'completed';
    ELSIF v_current_date BETWEEN v_start_date AND v_end_date THEN
        -- Shift is within its date range
        IF v_is_overnight THEN
            -- Night shift: active if time >= start_time OR time <= end_time
            v_is_within_time_window := (v_current_time >= v_start_time OR v_current_time <= v_end_time);
        ELSE
            -- Day shift: active if time between start_time and end_time
            v_is_within_time_window := (v_current_time BETWEEN v_start_time AND v_end_time);
        END IF;
        
        IF v_is_within_time_window THEN
            RETURN 'active';
        ELSE
            RETURN 'scheduled';
        END IF;
    ELSE
        -- Future shifts
        RETURN 'scheduled';
    END IF;
END;
$$;


--
-- Name: FUNCTION evaluate_shift_status(p_shift_id integer, p_reference_timestamp timestamp with time zone); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.evaluate_shift_status(p_shift_id integer, p_reference_timestamp timestamp with time zone) IS 'Evaluates shift status with proper completion logic: completed when both end_date and end_time conditions are met';


--
-- Name: fix_incorrectly_completed_shifts(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.fix_incorrectly_completed_shifts() RETURNS TABLE(shift_id integer, old_status public.shift_status, new_status public.shift_status, truck_id integer, driver_id integer, shift_type public.shift_type, start_date date, end_date date)
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_shift RECORD;
    v_correct_status TEXT;
    v_fixed_count INTEGER := 0;
BEGIN
    -- Find all completed shifts and check if they should actually be completed
    FOR v_shift IN
        SELECT ds.id, ds.truck_id, ds.driver_id, ds.shift_type, ds.status, ds.start_date, ds.end_date
        FROM driver_shifts ds
        WHERE ds.status = 'completed'
    LOOP
        -- Get the correct status using the 2-parameter function
        v_correct_status := evaluate_shift_status(v_shift.id, CURRENT_TIMESTAMP);
        
        -- If the correct status is different from current status, fix it
        IF v_correct_status != v_shift.status::TEXT THEN
            UPDATE driver_shifts
            SET 
                status = v_correct_status::shift_status,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = v_shift.id;
            
            v_fixed_count := v_fixed_count + 1;
            
            -- Return the fixed shift information
            RETURN QUERY SELECT 
                v_shift.id,
                v_shift.status,
                v_correct_status::shift_status,
                v_shift.truck_id,
                v_shift.driver_id,
                v_shift.shift_type,
                v_shift.start_date,
                v_shift.end_date;
        END IF;
    END LOOP;
    
    RAISE NOTICE 'Fixed % incorrectly completed shifts', v_fixed_count;
END;
$$;


--
-- Name: FUNCTION fix_incorrectly_completed_shifts(); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.fix_incorrectly_completed_shifts() IS 'Fixes shifts that are incorrectly marked as completed - uses 2-parameter evaluate_shift_status';


--
-- Name: get_active_shifts_for_date(date); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.get_active_shifts_for_date(p_check_date date DEFAULT CURRENT_DATE) RETURNS TABLE(shift_id integer, truck_id integer, driver_id integer, shift_type public.shift_type, display_type public.shift_type, recurrence_pattern public.recurrence_pattern, start_time time without time zone, end_time time without time zone, status public.shift_status)
    LANGUAGE plpgsql
    AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ds.id as shift_id,
        ds.truck_id,
        ds.driver_id,
        ds.shift_type,
        COALESCE(ds.display_type, ds.shift_type) as display_type,
        ds.recurrence_pattern,
        ds.start_time,
        ds.end_time,
        ds.status
    FROM driver_shifts ds
    WHERE ds.status IN ('scheduled', 'active')
      AND (
        -- Single date shifts (backward compatibility)
        (ds.recurrence_pattern = 'single' AND ds.shift_date = p_check_date)
        OR
        -- Date range shifts with recurrence patterns
        (ds.recurrence_pattern != 'single' AND is_shift_active_on_date(ds.id, p_check_date))
      )
    ORDER BY ds.truck_id, ds.start_time;
END;
$$;


--
-- Name: FUNCTION get_active_shifts_for_date(p_check_date date); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.get_active_shifts_for_date(p_check_date date) IS 'Get all active shifts for a date with recurrence pattern support';


--
-- Name: get_advanced_exception_analytics(date, date); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.get_advanced_exception_analytics(p_start_date date DEFAULT (CURRENT_DATE - '30 days'::interval), p_end_date date DEFAULT CURRENT_DATE) RETURNS TABLE(metric_category character varying, metric_name character varying, metric_value numeric, metric_unit character varying, metric_trend character varying)
    LANGUAGE plpgsql
    AS $$
BEGIN
  RETURN QUERY
  WITH current_period AS (
    SELECT 
      COUNT(*) as total_exceptions,
      COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_exceptions,
      COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_exceptions,
      COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_exceptions,
      COUNT(CASE WHEN exception_type = 'route_deviation' THEN 1 END) as route_deviations,
      AVG(CASE 
        WHEN status != 'pending' AND reviewed_at IS NOT NULL 
        THEN EXTRACT(EPOCH FROM (reviewed_at - requested_at))/3600 
      END) as avg_resolution_hours
    FROM approvals
    WHERE created_at BETWEEN p_start_date AND p_end_date
  ),
  previous_period AS (
    SELECT 
      COUNT(*) as total_exceptions,
      COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_exceptions,
      AVG(CASE 
        WHEN status != 'pending' AND reviewed_at IS NOT NULL 
        THEN EXTRACT(EPOCH FROM (reviewed_at - requested_at))/3600 
      END) as avg_resolution_hours
    FROM approvals
    WHERE created_at BETWEEN (p_start_date - (p_end_date - p_start_date)) AND p_start_date
  )
  SELECT 'exceptions'::VARCHAR(50), 'total_count', cp.total_exceptions::NUMERIC, 'count'::VARCHAR(20),
    CASE 
      WHEN pp.total_exceptions = 0 THEN 'new'
      WHEN cp.total_exceptions > pp.total_exceptions THEN 'increasing'
      WHEN cp.total_exceptions < pp.total_exceptions THEN 'decreasing'
      ELSE 'stable'
    END::VARCHAR(20)
  FROM current_period cp, previous_period pp
  
  UNION ALL
  
  SELECT 'exceptions', 'pending_count', cp.pending_exceptions::NUMERIC, 'count',
    CASE WHEN cp.pending_exceptions > 5 THEN 'high' ELSE 'normal' END
  FROM current_period cp
  
  UNION ALL
  
  SELECT 'exceptions', 'approval_rate', 
    ROUND(cp.approved_exceptions::NUMERIC / NULLIF(cp.total_exceptions, 0) * 100, 2), 'percentage',
    CASE 
      WHEN cp.approved_exceptions::NUMERIC / NULLIF(cp.total_exceptions, 0) > 0.8 THEN 'good'
      WHEN cp.approved_exceptions::NUMERIC / NULLIF(cp.total_exceptions, 0) > 0.6 THEN 'moderate'
      ELSE 'low'
    END
  FROM current_period cp
  
  UNION ALL
  
  SELECT 'performance', 'avg_resolution_time', 
    ROUND(cp.avg_resolution_hours::NUMERIC, 2), 'hours',
    CASE 
      WHEN cp.avg_resolution_hours < 2 THEN 'excellent'
      WHEN cp.avg_resolution_hours < 8 THEN 'good' 
      WHEN cp.avg_resolution_hours < 24 THEN 'acceptable'
      ELSE 'slow'
    END
  FROM current_period cp
  
  UNION ALL
  
  SELECT 'patterns', 'route_deviation_rate',
    ROUND(cp.route_deviations::NUMERIC / NULLIF(cp.total_exceptions, 0) * 100, 2), 'percentage',
    CASE 
      WHEN cp.route_deviations::NUMERIC / NULLIF(cp.total_exceptions, 0) > 0.5 THEN 'high'
      WHEN cp.route_deviations::NUMERIC / NULLIF(cp.total_exceptions, 0) > 0.3 THEN 'moderate'
      ELSE 'low'
    END
  FROM current_period cp;
END;
$$;


--
-- Name: FUNCTION get_advanced_exception_analytics(p_start_date date, p_end_date date); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.get_advanced_exception_analytics(p_start_date date, p_end_date date) IS 'Comprehensive exception analytics with trend analysis';


--
-- Name: get_current_active_driver(integer); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.get_current_active_driver(p_truck_id integer) RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_driver_id INTEGER;
BEGIN
    -- Get the current active driver for the truck from shift management
    SELECT ds.driver_id INTO v_driver_id
    FROM driver_shifts ds
    WHERE ds.truck_id = p_truck_id
      AND ds.status = 'active'
      AND (
        -- Day shift or night shift that doesn't cross midnight
        (ds.shift_date = CURRENT_DATE AND CURRENT_TIME BETWEEN ds.start_time AND ds.end_time)
        OR
        -- Night shift that crosses midnight - started yesterday, still active today
        (ds.shift_type = 'night' AND ds.end_time < ds.start_time AND 
         ds.shift_date = CURRENT_DATE - 1 AND CURRENT_TIME <= ds.end_time)
        OR
        -- Night shift that crosses midnight - started today
        (ds.shift_type = 'night' AND ds.end_time < ds.start_time AND 
         ds.shift_date = CURRENT_DATE AND CURRENT_TIME >= ds.start_time)
      )
    ORDER BY ds.created_at DESC
    LIMIT 1;
    
    RETURN v_driver_id;
END;
$$;


--
-- Name: get_current_driver_for_truck(integer); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.get_current_driver_for_truck(p_truck_id integer) RETURNS TABLE(driver_id integer, driver_name character varying, shift_id integer, shift_type public.shift_type, shift_start time without time zone, shift_end time without time zone)
    LANGUAGE plpgsql
    AS $$
BEGIN
    RETURN QUERY
    SELECT 
        d.id as driver_id,
        d.full_name as driver_name,
        ds.id as shift_id,
        ds.shift_type,
        ds.start_time as shift_start,
        ds.end_time as shift_end
    FROM driver_shifts ds
    JOIN drivers d ON ds.driver_id = d.id
    WHERE ds.truck_id = p_truck_id
        AND ds.status = 'active'
        AND ds.shift_date = CURRENT_DATE
        AND CURRENT_TIME BETWEEN ds.start_time AND 
            CASE 
                WHEN ds.end_time < ds.start_time 
                THEN ds.end_time + interval '24 hours'
                ELSE ds.end_time 
            END
    ORDER BY ds.created_at DESC
    LIMIT 1;
END;
$$;


--
-- Name: get_current_driver_for_truck_enhanced(integer, date, time without time zone); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.get_current_driver_for_truck_enhanced(p_truck_id integer, p_check_date date DEFAULT CURRENT_DATE, p_check_time time without time zone DEFAULT CURRENT_TIME) RETURNS TABLE(driver_id integer, driver_name character varying, employee_id character varying, shift_type public.shift_type, display_type public.shift_type, shift_id integer)
    LANGUAGE plpgsql
    AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ds.driver_id,
        d.full_name as driver_name,
        d.employee_id,
        ds.shift_type,
        COALESCE(ds.display_type, ds.shift_type) as display_type,
        ds.id as shift_id
    FROM driver_shifts ds
    JOIN drivers d ON ds.driver_id = d.id
    WHERE ds.truck_id = p_truck_id
      AND ds.status = 'active'
      AND is_shift_active_on_date(ds.id, p_check_date)
      AND p_check_time BETWEEN ds.start_time AND 
          CASE 
            WHEN ds.end_time < ds.start_time 
            THEN ds.end_time + interval '24 hours'
            ELSE ds.end_time 
          END
    ORDER BY ds.created_at DESC
    LIMIT 1;
END;
$$;


--
-- Name: FUNCTION get_current_driver_for_truck_enhanced(p_truck_id integer, p_check_date date, p_check_time time without time zone); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.get_current_driver_for_truck_enhanced(p_truck_id integer, p_check_date date, p_check_time time without time zone) IS 'Get current active driver for truck with date range support';


--
-- Name: get_database_performance_metrics(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.get_database_performance_metrics() RETURNS TABLE(metric_name character varying, metric_value numeric, metric_unit character varying, recommendation text)
    LANGUAGE plpgsql
    AS $$
BEGIN
  RETURN QUERY
  WITH table_stats AS (
    SELECT 
      schemaname,
      tablename,
      n_tup_ins as inserts,
      n_tup_upd as updates,
      n_tup_del as deletes,
      n_live_tup as live_tuples,
      n_dead_tup as dead_tuples,
      last_vacuum,
      last_autovacuum,
      last_analyze,
      last_autoanalyze
    FROM pg_stat_user_tables
    WHERE schemaname = 'public'
  ),
  index_usage AS (
    SELECT 
      schemaname,
      tablename,
      indexname,
      idx_tup_read,
      idx_tup_fetch
    FROM pg_stat_user_indexes
    WHERE schemaname = 'public'
  )
  SELECT 'table_stats'::VARCHAR(100), 
         SUM(ts.live_tuples)::NUMERIC, 
         'rows'::VARCHAR(20),
         'Total live tuples across all tables'::TEXT
  FROM table_stats ts
  
  UNION ALL
  
  SELECT 'dead_tuples_ratio',
         ROUND(SUM(ts.dead_tuples)::NUMERIC / NULLIF(SUM(ts.live_tuples), 0) * 100, 2),
         'percentage',
         CASE 
           WHEN SUM(ts.dead_tuples)::NUMERIC / NULLIF(SUM(ts.live_tuples), 0) > 0.1 
           THEN 'Consider running VACUUM on heavily updated tables'
           ELSE 'Dead tuple ratio is healthy'
         END
  FROM table_stats ts
  
  UNION ALL
  
  SELECT 'index_efficiency',
         ROUND(SUM(iu.idx_tup_fetch)::NUMERIC / NULLIF(SUM(iu.idx_tup_read), 0) * 100, 2),
         'percentage',
         CASE 
           WHEN SUM(iu.idx_tup_fetch)::NUMERIC / NULLIF(SUM(iu.idx_tup_read), 0) < 0.9
           THEN 'Some indexes may be underutilized'
           ELSE 'Index usage is efficient'
         END
  FROM index_usage iu;
END;
$$;


--
-- Name: get_exception_analytics(integer); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.get_exception_analytics(p_days integer DEFAULT 30) RETURNS TABLE(metric_name character varying, metric_value numeric, metric_unit character varying)
    LANGUAGE plpgsql
    AS $$
BEGIN
    RETURN QUERY
    WITH date_range AS (
        SELECT CURRENT_DATE - (p_days || ' days')::INTERVAL as start_date
    ),
    metrics AS (
        SELECT 
            COUNT(*) as total_exceptions,
            COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_exceptions,
            COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_exceptions,
            COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_exceptions,
            AVG(CASE 
                WHEN status != 'pending' AND reviewed_at IS NOT NULL 
                THEN EXTRACT(EPOCH FROM (reviewed_at - requested_at))/3600 
            END) as avg_resolution_hours
        FROM approvals, date_range
        WHERE created_at >= date_range.start_date
    ),
    trip_metrics AS (
        SELECT COUNT(*) as total_trips
        FROM trip_logs, date_range
        WHERE created_at >= date_range.start_date
    )
    SELECT 'total_exceptions'::VARCHAR(50), total_exceptions::NUMERIC, 'count'::VARCHAR(20) FROM metrics
    UNION ALL
    SELECT 'pending_exceptions', pending_exceptions::NUMERIC, 'count' FROM metrics
    UNION ALL
    SELECT 'approved_exceptions', approved_exceptions::NUMERIC, 'count' FROM metrics
    UNION ALL
    SELECT 'rejected_exceptions', rejected_exceptions::NUMERIC, 'count' FROM metrics
    UNION ALL
    SELECT 'avg_resolution_time', ROUND(avg_resolution_hours::NUMERIC, 2), 'hours' FROM metrics
    UNION ALL
    SELECT 'total_trips', total_trips::NUMERIC, 'count' FROM trip_metrics
    UNION ALL
    SELECT 'exception_rate', 
           ROUND((SELECT total_exceptions FROM metrics)::NUMERIC / 
                 NULLIF((SELECT total_trips FROM trip_metrics), 0) * 100, 2), 
           'percentage';
END;
$$;


--
-- Name: FUNCTION get_exception_analytics(p_days integer); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.get_exception_analytics(p_days integer) IS 'Returns key exception metrics for the specified number of days';


--
-- Name: get_shift_display_date(date, date, date, text); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.get_shift_display_date(p_shift_date date, p_start_date date, p_end_date date, p_recurrence_pattern text) RETURNS text
    LANGUAGE plpgsql
    AS $$
BEGIN
    -- Unified approach: always use start_date/end_date
    IF p_start_date = p_end_date THEN
        -- Single day shift
        RETURN p_start_date::TEXT;
    ELSE
        -- Multi-day shift
        RETURN p_start_date::TEXT || ' to ' || p_end_date::TEXT;
    END IF;
END;
$$;


--
-- Name: get_shift_status_summary(timestamp without time zone); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.get_shift_status_summary(p_reference_timestamp timestamp without time zone DEFAULT CURRENT_TIMESTAMP) RETURNS TABLE(total_shifts integer, active_shifts integer, scheduled_shifts integer, completed_shifts integer, cancelled_shifts integer, needs_activation integer, needs_completion integer, overnight_active integer)
    LANGUAGE plpgsql
    AS $$
BEGIN
    RETURN QUERY
    WITH shift_analysis AS (
        SELECT
            ds.id,
            ds.status as current_status,
            evaluate_shift_status(ds.id, p_reference_timestamp) as calculated_status,
            CASE WHEN ds.end_time < ds.start_time THEN 1 ELSE 0 END as is_overnight
        FROM driver_shifts ds
        WHERE ds.status != 'cancelled'
    )
    SELECT
        COUNT(*)::INTEGER as total_shifts,
        COUNT(CASE WHEN current_status = 'active' THEN 1 END)::INTEGER as active_shifts,
        COUNT(CASE WHEN current_status = 'scheduled' THEN 1 END)::INTEGER as scheduled_shifts,
        COUNT(CASE WHEN current_status = 'completed' THEN 1 END)::INTEGER as completed_shifts,
        COUNT(CASE WHEN current_status = 'cancelled' THEN 1 END)::INTEGER as cancelled_shifts,
        COUNT(CASE WHEN current_status = 'scheduled' AND calculated_status = 'active' THEN 1 END)::INTEGER as needs_activation,
        COUNT(CASE WHEN current_status = 'active' AND calculated_status = 'completed' THEN 1 END)::INTEGER as needs_completion,
        COUNT(CASE WHEN current_status = 'active' AND is_overnight = 1 THEN 1 END)::INTEGER as overnight_active
    FROM shift_analysis;
END;
$$;


--
-- Name: FUNCTION get_shift_status_summary(p_reference_timestamp timestamp without time zone); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.get_shift_status_summary(p_reference_timestamp timestamp without time zone) IS 'Provides real-time summary of shift statuses for monitoring and validation';


--
-- Name: is_overnight_shift(time without time zone, time without time zone); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.is_overnight_shift(start_time time without time zone, end_time time without time zone) RETURNS boolean
    LANGUAGE plpgsql IMMUTABLE
    AS $$
BEGIN
    RETURN end_time < start_time;
END;
$$;


--
-- Name: is_shift_active_on_date(integer, date); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.is_shift_active_on_date(p_shift_id integer, p_check_date date DEFAULT CURRENT_DATE) RETURNS boolean
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_shift RECORD;
    v_is_active BOOLEAN := FALSE;
BEGIN
    -- Get shift details
    SELECT 
        shift_date, start_date, end_date, recurrence_pattern,
        EXTRACT(DOW FROM start_date) as start_dow
    INTO v_shift
    FROM driver_shifts 
    WHERE id = p_shift_id;
    
    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;
    
    -- Check based on recurrence pattern
    CASE v_shift.recurrence_pattern
        WHEN 'single' THEN
            v_is_active := (v_shift.shift_date = p_check_date);
            
        WHEN 'daily' THEN
            v_is_active := (p_check_date BETWEEN v_shift.start_date AND v_shift.end_date);
            
        WHEN 'weekly' THEN
            v_is_active := (
                p_check_date BETWEEN v_shift.start_date AND v_shift.end_date AND
                EXTRACT(DOW FROM p_check_date) = v_shift.start_dow
            );
            
        WHEN 'weekdays' THEN
            v_is_active := (
                p_check_date BETWEEN v_shift.start_date AND v_shift.end_date AND
                EXTRACT(DOW FROM p_check_date) BETWEEN 1 AND 5
            );
            
        WHEN 'weekends' THEN
            v_is_active := (
                p_check_date BETWEEN v_shift.start_date AND v_shift.end_date AND
                EXTRACT(DOW FROM p_check_date) IN (0, 6)
            );
            
        WHEN 'custom' THEN
            -- Custom patterns handled by application logic
            v_is_active := (p_check_date BETWEEN v_shift.start_date AND v_shift.end_date);
            
        ELSE
            v_is_active := FALSE;
    END CASE;
    
    RETURN v_is_active;
END;
$$;


--
-- Name: FUNCTION is_shift_active_on_date(p_shift_id integer, p_check_date date); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.is_shift_active_on_date(p_shift_id integer, p_check_date date) IS 'Check if a shift is active on a specific date based on recurrence pattern';


--
-- Name: is_trip_terminal(public.trip_status); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.is_trip_terminal(p_status public.trip_status) RETURNS boolean
    LANGUAGE plpgsql
    AS $$
BEGIN
    RETURN p_status IN ('trip_completed', 'stopped', 'cancelled');
END;
$$;


--
-- Name: log_automated_fix(character varying, character varying, boolean, text, jsonb, integer); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.log_automated_fix(p_module_name character varying, p_fix_type character varying, p_success boolean, p_message text, p_details jsonb DEFAULT NULL::jsonb, p_affected_records integer DEFAULT 0) RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_log_id INTEGER;
BEGIN
    INSERT INTO automated_fix_logs (
        module_name, fix_type, success, message, details, affected_records
    ) VALUES (
        p_module_name, p_fix_type, p_success, p_message, p_details, p_affected_records
    ) RETURNING id INTO v_log_id;
    
    RETURN v_log_id;
END;
$$;


--
-- Name: FUNCTION log_automated_fix(p_module_name character varying, p_fix_type character varying, p_success boolean, p_message text, p_details jsonb, p_affected_records integer); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.log_automated_fix(p_module_name character varying, p_fix_type character varying, p_success boolean, p_message text, p_details jsonb, p_affected_records integer) IS 'Logs automated fix operations with success status and affected records';


--
-- Name: log_system_event(character varying, text, jsonb, integer); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.log_system_event(p_log_type character varying, p_message text, p_details jsonb DEFAULT NULL::jsonb, p_user_id integer DEFAULT NULL::integer) RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_log_id INTEGER;
BEGIN
    INSERT INTO system_logs (log_type, message, details, user_id)
    VALUES (p_log_type, p_message, p_details, p_user_id)
    RETURNING id INTO v_log_id;
    
    RETURN v_log_id;
END;
$$;


--
-- Name: FUNCTION log_system_event(p_log_type character varying, p_message text, p_details jsonb, p_user_id integer); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.log_system_event(p_log_type character varying, p_message text, p_details jsonb, p_user_id integer) IS 'Logs system events with optional details and user context';


--
-- Name: monitor_shift_system(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.monitor_shift_system() RETURNS text
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_health_record RECORD;
    v_issues_found INTEGER := 0;
    v_result TEXT;
BEGIN
    -- Run health check
    FOR v_health_record IN SELECT * FROM shift_system_health_check()
    LOOP
        IF NOT v_health_record.passed THEN
            v_issues_found := v_issues_found + 1;
            RAISE WARNING 'Health Check Failed: % - %', v_health_record.check_name, v_health_record.details;
        END IF;
    END LOOP;
    
    -- Auto-cleanup if issues found
    IF v_issues_found > 0 THEN
        PERFORM cleanup_shift_system();
        v_result := 'Issues detected and auto-cleanup performed';
    ELSE
        v_result := 'System healthy - no issues detected';
    END IF;
    
    RAISE NOTICE '%', v_result;
    RETURN v_result;
END;
$$;


--
-- Name: FUNCTION monitor_shift_system(); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.monitor_shift_system() IS 'Continuous monitoring function with auto-cleanup capabilities';


--
-- Name: refresh_all_analytics_views(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.refresh_all_analytics_views() RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
  REFRESH MATERIALIZED VIEW mv_fleet_status_summary;
  IF EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'trip_logs' AND column_name = 'breakdown_reported_at'
  ) THEN
    REFRESH MATERIALIZED VIEW mv_breakdown_analytics_summary;
  END IF;
END;
$$;


--
-- Name: FUNCTION refresh_all_analytics_views(); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.refresh_all_analytics_views() IS 'Refresh all analytics materialized views for optimal performance';


--
-- Name: refresh_breakdown_analytics_summary(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.refresh_breakdown_analytics_summary() RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'trip_logs' AND column_name = 'breakdown_reported_at'
  ) THEN
    REFRESH MATERIALIZED VIEW mv_breakdown_analytics_summary;
  END IF;
END;
$$;


--
-- Name: refresh_fleet_status_summary(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.refresh_fleet_status_summary() RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
  REFRESH MATERIALIZED VIEW mv_fleet_status_summary;
END;
$$;


--
-- Name: refresh_trip_performance_summary(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.refresh_trip_performance_summary() RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
  REFRESH MATERIALIZED VIEW CONCURRENTLY mv_trip_performance_summary;
END;
$$;


--
-- Name: schedule_auto_activation(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.schedule_auto_activation() RETURNS void
    LANGUAGE plpgsql
    AS $$
DECLARE
    shift_record RECORD;
    calculated_status TEXT;
    current_status TEXT;
    activated_count INTEGER := 0;
    scheduled_count INTEGER := 0;
    completed_count INTEGER := 0;
    updated_count INTEGER := 0;
BEGIN
    -- Process all non-cancelled shifts
    FOR shift_record IN 
        SELECT id, status FROM driver_shifts 
        WHERE status != 'cancelled'
    LOOP
        current_status := shift_record.status;
        calculated_status := evaluate_shift_status(shift_record.id, CURRENT_TIMESTAMP);
        
        -- Only update if status has changed and is not 'error'
        IF calculated_status != current_status AND calculated_status != 'error' THEN
            -- Update the shift status
            UPDATE driver_shifts 
            SET status = calculated_status::shift_status, 
                updated_at = CURRENT_TIMESTAMP 
            WHERE id = shift_record.id;
            
            updated_count := updated_count + 1;
            
            -- Count by status type
            CASE calculated_status
                WHEN 'active' THEN activated_count := activated_count + 1;
                WHEN 'scheduled' THEN scheduled_count := scheduled_count + 1;
                WHEN 'completed' THEN completed_count := completed_count + 1;
            END CASE;
        END IF;
    END LOOP;
    
    -- Log the results if any updates were made
    IF updated_count > 0 THEN
        INSERT INTO system_logs (
            log_type, 
            message, 
            details
        ) VALUES (
            'SHIFT_AUTO_UPDATE',
            'Auto-updated shifts based on current time',
            jsonb_build_object(
                'updated_count', updated_count,
                'activated_count', activated_count,
                'scheduled_count', scheduled_count,
                'completed_count', completed_count,
                'timestamp', CURRENT_TIMESTAMP
            )
        );
    END IF;
END;
$$;


--
-- Name: FUNCTION schedule_auto_activation(); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.schedule_auto_activation() IS 'Auto-updates shifts including completion when appropriate';


--
-- Name: schedule_shift_activation(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.schedule_shift_activation() RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
    PERFORM auto_activate_shifts();
END;
$$;


--
-- Name: set_display_type_trigger(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.set_display_type_trigger() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    -- If display_type is not explicitly set, compute it intelligently
    IF NEW.display_type IS NULL THEN
        -- If user selected day or night explicitly, respect that
        IF NEW.shift_type IN ('day', 'night') THEN
            NEW.display_type := NEW.shift_type;
        ELSE
            -- For custom shifts, use intelligent classification
            NEW.display_type := classify_shift_by_time(NEW.start_time, NEW.end_time);
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$;


--
-- Name: shift_includes_date(date, date, date); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.shift_includes_date(p_start_date date, p_end_date date, p_query_date date) RETURNS boolean
    LANGUAGE plpgsql IMMUTABLE
    AS $$
BEGIN
    RETURN p_query_date BETWEEN p_start_date AND p_end_date;
END;
$$;


--
-- Name: shift_overlaps_range(date, date, date, date); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.shift_overlaps_range(p_start_date date, p_end_date date, p_range_start date, p_range_end date) RETURNS boolean
    LANGUAGE plpgsql IMMUTABLE
    AS $$
BEGIN
    RETURN p_start_date <= p_range_end AND p_end_date >= p_range_start;
END;
$$;


--
-- Name: shift_system_health_check(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.shift_system_health_check() RETURNS TABLE(check_name text, status text, details text, passed boolean)
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_function_count INTEGER;
    v_signature_issues INTEGER;
    v_status_inconsistencies INTEGER;
    v_assignment_issues INTEGER;
BEGIN
    -- Check 1: Verify all required functions exist with correct signatures
    SELECT COUNT(*) INTO v_function_count
    FROM pg_proc 
    WHERE proname IN ('evaluate_shift_status', 'schedule_auto_activation', 'update_all_shift_statuses')
    AND pg_get_function_arguments(oid) NOT LIKE '%boolean%'; -- No 3-parameter functions
    
    RETURN QUERY SELECT 
        'Function Signatures'::TEXT,
        CASE WHEN v_function_count = 3 THEN 'PASS' ELSE 'FAIL' END::TEXT,
        CASE WHEN v_function_count = 3 THEN 'All functions have correct signatures' 
             ELSE 'Missing functions or incorrect signatures detected' END::TEXT,
        v_function_count = 3;
    
    -- Check 2: Verify no problematic 3-parameter functions exist
    SELECT COUNT(*) INTO v_signature_issues
    FROM pg_proc 
    WHERE proname LIKE '%shift%' 
    AND pg_get_function_arguments(oid) LIKE '%boolean%';
    
    RETURN QUERY SELECT 
        'No Legacy Functions'::TEXT,
        CASE WHEN v_signature_issues = 0 THEN 'PASS' ELSE 'FAIL' END::TEXT,
        CASE WHEN v_signature_issues = 0 THEN 'No problematic 3-parameter functions found'
             ELSE v_signature_issues || ' problematic functions detected' END::TEXT,
        v_signature_issues = 0;
    
    -- Check 3: Verify shift status consistency
    SELECT COUNT(*) INTO v_status_inconsistencies
    FROM driver_shifts ds
    WHERE ds.status != 'cancelled'
    AND ds.status::TEXT != evaluate_shift_status(ds.id, CURRENT_TIMESTAMP);
    
    RETURN QUERY SELECT 
        'Status Consistency'::TEXT,
        CASE WHEN v_status_inconsistencies = 0 THEN 'PASS' ELSE 'WARN' END::TEXT,
        CASE WHEN v_status_inconsistencies = 0 THEN 'All shift statuses are consistent'
             ELSE v_status_inconsistencies || ' shifts have inconsistent statuses' END::TEXT,
        v_status_inconsistencies = 0;
    
    -- Check 4: Verify assignment display logic
    SELECT COUNT(*) INTO v_assignment_issues
    FROM assignments a
    JOIN dump_trucks t ON a.truck_id = t.id
    LEFT JOIN driver_shifts ds ON (
        ds.truck_id = a.truck_id
        AND ds.status = 'active'
        AND CURRENT_DATE BETWEEN ds.start_date AND ds.end_date
        AND (
            (ds.end_time < ds.start_time AND
             (CURRENT_TIME >= ds.start_time OR CURRENT_TIME <= ds.end_time))
            OR
            (ds.end_time >= ds.start_time AND
             CURRENT_TIME BETWEEN ds.start_time AND ds.end_time)
        )
    )
    WHERE ds.id IS NULL; -- No active shift found when there should be one
    
    RETURN QUERY SELECT 
        'Assignment Display'::TEXT,
        CASE WHEN v_assignment_issues = 0 THEN 'PASS' ELSE 'WARN' END::TEXT,
        CASE WHEN v_assignment_issues = 0 THEN 'All assignments showing active shifts correctly'
             ELSE v_assignment_issues || ' assignments may show "No Active Shift"' END::TEXT,
        v_assignment_issues = 0;
    
    -- Check 5: Overall system health
    RETURN QUERY SELECT 
        'Overall Health'::TEXT,
        CASE WHEN v_function_count = 3 AND v_signature_issues = 0 THEN 'PASS' ELSE 'FAIL' END::TEXT,
        'System health based on critical checks'::TEXT,
        v_function_count = 3 AND v_signature_issues = 0;
END;
$$;


--
-- Name: FUNCTION shift_system_health_check(); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.shift_system_health_check() IS 'Comprehensive health check for shift management system - prevents regression';


--
-- Name: sync_shift_date_with_start_date(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.sync_shift_date_with_start_date() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
  -- Auto-sync shift_date with start_date for backward compatibility
  -- This ensures existing code that references shift_date continues to work
  NEW.shift_date = NEW.start_date;
  RETURN NEW;
END;
$$;


--
-- Name: test_shift_scenarios(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.test_shift_scenarios() RETURNS TABLE(scenario text, shift_type text, start_time time without time zone, end_time time without time zone, test_time time without time zone, expected_status text, actual_status text, passed boolean)
    LANGUAGE plpgsql
    AS $$
DECLARE
    test_shift_id INTEGER;
    test_date DATE := CURRENT_DATE;
BEGIN
    -- Test Day Shift Scenarios
    -- Scenario 1: Day shift before start
    INSERT INTO driver_shifts (truck_id, driver_id, shift_type, start_date, end_date, start_time, end_time, status, recurrence_pattern)
    VALUES (1, 1, 'day', test_date, test_date, '08:00:00', '16:00:00', 'scheduled', 'single')
    RETURNING id INTO test_shift_id;
    
    RETURN QUERY SELECT 
        'Day shift before start'::TEXT,
        'day'::TEXT,
        '08:00:00'::TIME,
        '16:00:00'::TIME,
        '07:00:00'::TIME,
        'scheduled'::TEXT,
        evaluate_shift_status(test_shift_id, test_date + '07:00:00'::TIME),
        evaluate_shift_status(test_shift_id, test_date + '07:00:00'::TIME) = 'scheduled';
    
    DELETE FROM driver_shifts WHERE id = test_shift_id;
    
    -- Scenario 2: Day shift during active period
    INSERT INTO driver_shifts (truck_id, driver_id, shift_type, start_date, end_date, start_time, end_time, status, recurrence_pattern)
    VALUES (1, 1, 'day', test_date, test_date, '08:00:00', '16:00:00', 'scheduled', 'single')
    RETURNING id INTO test_shift_id;
    
    RETURN QUERY SELECT 
        'Day shift during active'::TEXT,
        'day'::TEXT,
        '08:00:00'::TIME,
        '16:00:00'::TIME,
        '12:00:00'::TIME,
        'active'::TEXT,
        evaluate_shift_status(test_shift_id, test_date + '12:00:00'::TIME),
        evaluate_shift_status(test_shift_id, test_date + '12:00:00'::TIME) = 'active';
    
    DELETE FROM driver_shifts WHERE id = test_shift_id;
    
    -- Scenario 3: Day shift after end
    INSERT INTO driver_shifts (truck_id, driver_id, shift_type, start_date, end_date, start_time, end_time, status, recurrence_pattern)
    VALUES (1, 1, 'day', test_date, test_date, '08:00:00', '16:00:00', 'active', 'single')
    RETURNING id INTO test_shift_id;
    
    RETURN QUERY SELECT 
        'Day shift after end'::TEXT,
        'day'::TEXT,
        '08:00:00'::TIME,
        '16:00:00'::TIME,
        '17:00:00'::TIME,
        'completed'::TEXT,
        evaluate_shift_status(test_shift_id, test_date + '17:00:00'::TIME),
        evaluate_shift_status(test_shift_id, test_date + '17:00:00'::TIME) = 'completed';
    
    DELETE FROM driver_shifts WHERE id = test_shift_id;
    
    -- Test Night Shift Scenarios
    -- Scenario 4: Night shift before start (same day)
    INSERT INTO driver_shifts (truck_id, driver_id, shift_type, start_date, end_date, start_time, end_time, status, recurrence_pattern)
    VALUES (1, 1, 'night', test_date, test_date, '22:00:00', '06:00:00', 'scheduled', 'single')
    RETURNING id INTO test_shift_id;
    
    RETURN QUERY SELECT 
        'Night shift before start'::TEXT,
        'night'::TEXT,
        '22:00:00'::TIME,
        '06:00:00'::TIME,
        '20:00:00'::TIME,
        'scheduled'::TEXT,
        evaluate_shift_status(test_shift_id, test_date + '20:00:00'::TIME),
        evaluate_shift_status(test_shift_id, test_date + '20:00:00'::TIME) = 'scheduled';
    
    DELETE FROM driver_shifts WHERE id = test_shift_id;
    
    -- Scenario 5: Night shift during active (evening)
    INSERT INTO driver_shifts (truck_id, driver_id, shift_type, start_date, end_date, start_time, end_time, status, recurrence_pattern)
    VALUES (1, 1, 'night', test_date, test_date, '22:00:00', '06:00:00', 'scheduled', 'single')
    RETURNING id INTO test_shift_id;
    
    RETURN QUERY SELECT 
        'Night shift active evening'::TEXT,
        'night'::TEXT,
        '22:00:00'::TIME,
        '06:00:00'::TIME,
        '23:00:00'::TIME,
        'active'::TEXT,
        evaluate_shift_status(test_shift_id, test_date + '23:00:00'::TIME),
        evaluate_shift_status(test_shift_id, test_date + '23:00:00'::TIME) = 'active';
    
    DELETE FROM driver_shifts WHERE id = test_shift_id;
    
    -- Scenario 6: Night shift during active (morning next day)
    INSERT INTO driver_shifts (truck_id, driver_id, shift_type, start_date, end_date, start_time, end_time, status, recurrence_pattern)
    VALUES (1, 1, 'night', test_date, test_date, '22:00:00', '06:00:00', 'active', 'single')
    RETURNING id INTO test_shift_id;
    
    RETURN QUERY SELECT 
        'Night shift active morning'::TEXT,
        'night'::TEXT,
        '22:00:00'::TIME,
        '06:00:00'::TIME,
        '03:00:00'::TIME,
        'active'::TEXT,
        evaluate_shift_status(test_shift_id, (test_date + INTERVAL '1 day') + '03:00:00'::TIME),
        evaluate_shift_status(test_shift_id, (test_date + INTERVAL '1 day') + '03:00:00'::TIME) = 'active';
    
    DELETE FROM driver_shifts WHERE id = test_shift_id;
    
    -- Scenario 7: Night shift completed (next day after end)
    INSERT INTO driver_shifts (truck_id, driver_id, shift_type, start_date, end_date, start_time, end_time, status, recurrence_pattern)
    VALUES (1, 1, 'night', test_date, test_date, '22:00:00', '06:00:00', 'active', 'single')
    RETURNING id INTO test_shift_id;
    
    RETURN QUERY SELECT 
        'Night shift completed'::TEXT,
        'night'::TEXT,
        '22:00:00'::TIME,
        '06:00:00'::TIME,
        '07:00:00'::TIME,
        'completed'::TEXT,
        evaluate_shift_status(test_shift_id, (test_date + INTERVAL '1 day') + '07:00:00'::TIME),
        evaluate_shift_status(test_shift_id, (test_date + INTERVAL '1 day') + '07:00:00'::TIME) = 'completed';
    
    DELETE FROM driver_shifts WHERE id = test_shift_id;
END;
$$;


--
-- Name: FUNCTION test_shift_scenarios(); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.test_shift_scenarios() IS 'Comprehensive testing function for day and night shift logic validation';


--
-- Name: test_shift_time_logic(time without time zone, time without time zone, time without time zone, boolean); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.test_shift_time_logic(p_start_time time without time zone, p_end_time time without time zone, p_test_time time without time zone, p_is_overnight boolean DEFAULT NULL::boolean) RETURNS TABLE(is_overnight boolean, is_within_window boolean, logic_used text)
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_is_overnight BOOLEAN;
    v_is_within_window BOOLEAN;
    v_logic_used TEXT;
BEGIN
    -- Determine if overnight (or use provided value for testing)
    v_is_overnight := COALESCE(p_is_overnight, p_end_time < p_start_time);

    IF v_is_overnight THEN
        -- Night shift: Use dual condition logic
        v_is_within_window := (p_test_time >= p_start_time OR p_test_time <= p_end_time);
        v_logic_used := 'dual_condition_overnight';
    ELSE
        -- Day shift: Use simple BETWEEN logic
        v_is_within_window := (p_test_time BETWEEN p_start_time AND p_end_time);
        v_logic_used := 'simple_between_day';
    END IF;

    RETURN QUERY SELECT v_is_overnight, v_is_within_window, v_logic_used;
END;
$$;


--
-- Name: FUNCTION test_shift_time_logic(p_start_time time without time zone, p_end_time time without time zone, p_test_time time without time zone, p_is_overnight boolean); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.test_shift_time_logic(p_start_time time without time zone, p_end_time time without time zone, p_test_time time without time zone, p_is_overnight boolean) IS 'Testing function to validate shift time logic for day and night shifts';


--
-- Name: update_all_shift_statuses(timestamp with time zone); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.update_all_shift_statuses(p_reference_timestamp timestamp with time zone DEFAULT CURRENT_TIMESTAMP) RETURNS TABLE(updated_count integer, activated_count integer, scheduled_count integer, completed_count integer, total_count integer)
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_updated_count INTEGER := 0;
    v_activated_count INTEGER := 0;
    v_scheduled_count INTEGER := 0;
    v_completed_count INTEGER := 0;
    v_total_count INTEGER := 0;
    v_shift RECORD;
    v_calculated_status TEXT;
BEGIN
    -- Count total non-cancelled shifts
    SELECT COUNT(*) INTO v_total_count FROM driver_shifts WHERE status != 'cancelled';
    
    -- Process all non-cancelled shifts
    FOR v_shift IN 
        SELECT id, status::TEXT FROM driver_shifts 
        WHERE status != 'cancelled'
    LOOP
        -- Calculate the correct status
        v_calculated_status := evaluate_shift_status(v_shift.id, p_reference_timestamp);
        
        -- Only update if status has changed and is not 'error'
        IF v_calculated_status != v_shift.status AND v_calculated_status != 'error' THEN
            -- Update the shift status
            UPDATE driver_shifts 
            SET status = v_calculated_status::shift_status, 
                updated_at = p_reference_timestamp
            WHERE id = v_shift.id;
            
            v_updated_count := v_updated_count + 1;
            
            -- Count by status type
            CASE v_calculated_status
                WHEN 'active' THEN v_activated_count := v_activated_count + 1;
                WHEN 'scheduled' THEN v_scheduled_count := v_scheduled_count + 1;
                WHEN 'completed' THEN v_completed_count := v_completed_count + 1;
            END CASE;
        END IF;
    END LOOP;
    
    -- Return the statistics
    updated_count := v_updated_count;
    activated_count := v_activated_count;
    scheduled_count := v_scheduled_count;
    completed_count := v_completed_count;
    total_count := v_total_count;
    
    RETURN NEXT;
END;
$$;


--
-- Name: FUNCTION update_all_shift_statuses(p_reference_timestamp timestamp with time zone); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.update_all_shift_statuses(p_reference_timestamp timestamp with time zone) IS 'Updates shift statuses including automatic completion when both date and time conditions are met';


--
-- Name: update_assignment_on_trip_complete(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.update_assignment_on_trip_complete() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_completed_trips INTEGER;
    v_expected_loads INTEGER;
BEGIN
    -- Only process if trip is completed
    IF NEW.status = 'trip_completed' AND OLD.status != 'trip_completed' THEN
        -- Count completed trips for this assignment today
        SELECT COUNT(*), MAX(a.expected_loads_per_day)
        INTO v_completed_trips, v_expected_loads
        FROM trip_logs tl
        JOIN assignments a ON tl.assignment_id = a.id
        WHERE tl.assignment_id = NEW.assignment_id
          AND tl.status = 'trip_completed'
          AND DATE(tl.created_at) = CURRENT_DATE
        GROUP BY a.expected_loads_per_day;

        -- Update assignment status if all expected loads completed
        IF v_completed_trips >= v_expected_loads THEN
            UPDATE assignments
            SET status = 'completed',
                end_time = NEW.trip_completed_time,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = NEW.assignment_id;
        END IF;
    END IF;

    RETURN NEW;
END;
$$;


--
-- Name: update_shift_status(integer, timestamp without time zone); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.update_shift_status(p_shift_id integer, p_reference_timestamp timestamp without time zone DEFAULT CURRENT_TIMESTAMP) RETURNS TABLE(old_status text, new_status text, status_changed boolean, truck_id integer, driver_id integer)
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_new_status TEXT;
    v_old_status TEXT;
    v_truck_id INTEGER;
    v_driver_id INTEGER;
    v_shift_info RECORD;
BEGIN
    -- Get current shift information
    SELECT status, truck_id, driver_id, shift_type
    INTO v_shift_info
    FROM driver_shifts
    WHERE id = p_shift_id;

    IF NOT FOUND THEN
        RETURN QUERY SELECT 'shift_not_found'::TEXT, 'error'::TEXT, FALSE, NULL::INTEGER, NULL::INTEGER;
        RETURN;
    END IF;

    v_old_status := v_shift_info.status;
    v_truck_id := v_shift_info.truck_id;
    v_driver_id := v_shift_info.driver_id;

    -- Evaluate new status using enhanced logic
    v_new_status := evaluate_shift_status(p_shift_id, p_reference_timestamp);

    -- Update if status has changed and new status is valid
    IF v_new_status != v_old_status AND v_new_status != 'error' THEN
        UPDATE driver_shifts
        SET status = v_new_status::shift_status,
            updated_at = p_reference_timestamp
        WHERE id = p_shift_id;

        -- Log the transition for monitoring
        RAISE NOTICE 'Shift status updated: ID=%, Truck=%, Driver=%, %->%',
            p_shift_id, v_truck_id, v_driver_id, v_old_status, v_new_status;

        RETURN QUERY SELECT v_old_status, v_new_status, TRUE, v_truck_id, v_driver_id;
    ELSE
        RETURN QUERY SELECT v_old_status, v_old_status, FALSE, v_truck_id, v_driver_id;
    END IF;
END;
$$;


--
-- Name: FUNCTION update_shift_status(p_shift_id integer, p_reference_timestamp timestamp without time zone); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.update_shift_status(p_shift_id integer, p_reference_timestamp timestamp without time zone) IS 'Updates status for a specific shift with enhanced validation and logging';


--
-- Name: update_updated_at_column(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.update_updated_at_column() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$;


--
-- Name: validate_all_shift_statuses(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.validate_all_shift_statuses() RETURNS TABLE(shift_id integer, truck_id integer, driver_id integer, shift_type text, current_status text, calculated_status text, needs_update boolean, is_overnight boolean)
    LANGUAGE plpgsql
    AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ds.id,
        ds.truck_id,
        ds.driver_id,
        ds.shift_type,
        ds.status::TEXT,
        evaluate_shift_status(ds.id, CURRENT_TIMESTAMP),
        (ds.status::TEXT != evaluate_shift_status(ds.id, CURRENT_TIMESTAMP)),
        (ds.end_time < ds.start_time)
    FROM driver_shifts ds
    WHERE ds.status != 'cancelled'
    ORDER BY ds.truck_id, ds.start_time;
END;
$$;


--
-- Name: FUNCTION validate_all_shift_statuses(); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.validate_all_shift_statuses() IS 'Validates all current shift statuses against calculated statuses';


SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: approvals; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.approvals (
    id integer NOT NULL,
    trip_log_id integer NOT NULL,
    exception_type character varying(50) NOT NULL,
    exception_description text NOT NULL,
    severity character varying(20) DEFAULT 'medium'::character varying,
    reported_by integer,
    requested_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    reviewed_by integer,
    reviewed_at timestamp without time zone,
    status public.approval_status DEFAULT 'pending'::public.approval_status NOT NULL,
    notes text,
    is_adaptive_exception boolean DEFAULT false,
    adaptation_strategy character varying(50),
    adaptation_confidence character varying(20),
    auto_approved boolean DEFAULT false,
    adaptation_metadata jsonb,
    suggested_assignment_id integer,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    reason text,
    CONSTRAINT approvals_severity_check CHECK (((severity)::text = ANY ((ARRAY['low'::character varying, 'medium'::character varying, 'high'::character varying, 'critical'::character varying])::text[]))),
    CONSTRAINT chk_approvals_adaptation_confidence CHECK (((adaptation_confidence IS NULL) OR ((adaptation_confidence)::text = ANY ((ARRAY['high'::character varying, 'medium'::character varying, 'low'::character varying])::text[])))),
    CONSTRAINT chk_approvals_adaptation_strategy CHECK (((adaptation_strategy IS NULL) OR ((adaptation_strategy)::text = ANY ((ARRAY['pattern_based'::character varying, 'proximity_based'::character varying, 'efficiency_based'::character varying, 'manual_override'::character varying])::text[]))))
);


--
-- Name: approvals_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.approvals_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: approvals_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.approvals_id_seq OWNED BY public.approvals.id;


--
-- Name: assignments; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.assignments (
    id integer NOT NULL,
    assignment_code character varying(50),
    truck_id integer NOT NULL,
    driver_id integer,
    loading_location_id integer NOT NULL,
    unloading_location_id integer NOT NULL,
    status public.assignment_status DEFAULT 'assigned'::public.assignment_status NOT NULL,
    priority character varying(20) DEFAULT 'normal'::character varying,
    assigned_date date,
    start_time timestamp without time zone,
    end_time timestamp without time zone,
    expected_loads_per_day integer DEFAULT 1,
    is_adaptive boolean DEFAULT false,
    adaptation_strategy character varying(50),
    adaptation_confidence character varying(20),
    adaptation_metadata jsonb,
    notes text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    shift_id integer,
    is_shift_assignment boolean DEFAULT false,
    shift_handover_id integer,
    auto_created boolean DEFAULT false,
    CONSTRAINT assignments_priority_check CHECK (((priority)::text = ANY ((ARRAY['low'::character varying, 'normal'::character varying, 'high'::character varying, 'urgent'::character varying])::text[]))),
    CONSTRAINT chk_assignment_integrity CHECK (((driver_id IS NOT NULL) OR ((notes IS NOT NULL) AND ((notes ~~ '%Auto-assigned%'::text) OR (notes ~~ '%No active driver%'::text) OR (notes ~~ '%manual assignment required%'::text))) OR ((assignment_code IS NOT NULL) AND ((assignment_code)::text ~~ '%AUTO%'::text)))),
    CONSTRAINT chk_assignments_adaptation_confidence CHECK (((adaptation_confidence IS NULL) OR ((adaptation_confidence)::text = ANY ((ARRAY['high'::character varying, 'medium'::character varying, 'low'::character varying])::text[])))),
    CONSTRAINT chk_assignments_adaptation_strategy CHECK (((adaptation_strategy IS NULL) OR ((adaptation_strategy)::text = ANY ((ARRAY['pattern_based'::character varying, 'proximity_based'::character varying, 'efficiency_based'::character varying, 'manual_override'::character varying])::text[]))))
);


--
-- Name: COLUMN assignments.assigned_date; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.assignments.assigned_date IS 'Planned date for the assignment, can be null for flexible assignments';


--
-- Name: assignments_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.assignments_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: assignments_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.assignments_id_seq OWNED BY public.assignments.id;


--
-- Name: automated_fix_logs; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.automated_fix_logs (
    id integer NOT NULL,
    module_name character varying(50) NOT NULL,
    fix_type character varying(50) DEFAULT 'automated_fix'::character varying NOT NULL,
    success boolean DEFAULT false NOT NULL,
    message text NOT NULL,
    details jsonb,
    affected_records integer DEFAULT 0,
    executed_at timestamp with time zone DEFAULT now() NOT NULL,
    created_at timestamp with time zone DEFAULT now()
);


--
-- Name: TABLE automated_fix_logs; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.automated_fix_logs IS 'Audit log for automated system health fixes';


--
-- Name: COLUMN automated_fix_logs.module_name; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.automated_fix_logs.module_name IS 'Module that was fixed (shift_management, assignment_management, trip_monitoring)';


--
-- Name: COLUMN automated_fix_logs.fix_type; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.automated_fix_logs.fix_type IS 'Type of fix operation performed';


--
-- Name: COLUMN automated_fix_logs.success; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.automated_fix_logs.success IS 'Whether the fix operation completed successfully';


--
-- Name: COLUMN automated_fix_logs.message; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.automated_fix_logs.message IS 'Human-readable summary of the fix operation';


--
-- Name: COLUMN automated_fix_logs.details; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.automated_fix_logs.details IS 'Detailed information about the fix operation in JSON format';


--
-- Name: COLUMN automated_fix_logs.affected_records; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.automated_fix_logs.affected_records IS 'Number of database records affected by the fix';


--
-- Name: COLUMN automated_fix_logs.executed_at; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.automated_fix_logs.executed_at IS 'When the fix operation was executed';


--
-- Name: automated_fix_logs_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.automated_fix_logs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: automated_fix_logs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.automated_fix_logs_id_seq OWNED BY public.automated_fix_logs.id;


--
-- Name: driver_shifts; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.driver_shifts (
    id integer NOT NULL,
    truck_id integer NOT NULL,
    driver_id integer NOT NULL,
    shift_type public.shift_type DEFAULT 'day'::public.shift_type NOT NULL,
    start_time time without time zone NOT NULL,
    end_time time without time zone NOT NULL,
    status public.shift_status DEFAULT 'scheduled'::public.shift_status NOT NULL,
    previous_shift_id integer,
    handover_notes text,
    handover_completed_at timestamp without time zone,
    assignment_id integer,
    auto_created boolean DEFAULT false,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    start_date date,
    end_date date,
    recurrence_pattern public.recurrence_pattern DEFAULT 'single'::public.recurrence_pattern NOT NULL,
    display_type public.shift_type,
    shift_date date,
    completion_notes text,
    cancellation_reason text,
    CONSTRAINT valid_unified_date_range CHECK (((start_date IS NOT NULL) AND (end_date IS NOT NULL) AND (start_date <= end_date)))
);


--
-- Name: COLUMN driver_shifts.start_date; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.driver_shifts.start_date IS 'Start date of shift range. For single-day shifts, same as end_date.';


--
-- Name: COLUMN driver_shifts.end_date; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.driver_shifts.end_date IS 'End date of shift range. For single-day shifts, same as start_date.';


--
-- Name: COLUMN driver_shifts.recurrence_pattern; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.driver_shifts.recurrence_pattern IS 'Recurrence pattern: single, daily, weekly, weekdays, weekends, custom';


--
-- Name: COLUMN driver_shifts.display_type; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.driver_shifts.display_type IS 'Computed display type for UI (may differ from shift_type for intelligent classification)';


--
-- Name: COLUMN driver_shifts.shift_date; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.driver_shifts.shift_date IS 'Optional backward compatibility column. Auto-synced with start_date via trigger. Maintained for legacy code compatibility while unified approach uses start_date/end_date.';


--
-- Name: driver_shifts_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.driver_shifts_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: driver_shifts_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.driver_shifts_id_seq OWNED BY public.driver_shifts.id;


--
-- Name: drivers; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.drivers (
    id integer NOT NULL,
    employee_id character varying(20) NOT NULL,
    full_name character varying(100) NOT NULL,
    license_number character varying(30) NOT NULL,
    license_expiry date NOT NULL,
    phone character varying(20),
    email character varying(100),
    address text,
    hire_date date NOT NULL,
    status public.driver_status DEFAULT 'active'::public.driver_status NOT NULL,
    notes text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


--
-- Name: drivers_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.drivers_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: drivers_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.drivers_id_seq OWNED BY public.drivers.id;


--
-- Name: dump_trucks; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.dump_trucks (
    id integer NOT NULL,
    truck_number character varying(20) NOT NULL,
    license_plate character varying(20) NOT NULL,
    make character varying(50),
    model character varying(50),
    year integer,
    capacity_tons numeric(5,2),
    qr_code_data jsonb NOT NULL,
    status public.truck_status DEFAULT 'active'::public.truck_status NOT NULL,
    notes text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


--
-- Name: dump_trucks_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.dump_trucks_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: dump_trucks_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.dump_trucks_id_seq OWNED BY public.dump_trucks.id;


--
-- Name: health_check_logs; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.health_check_logs (
    id integer NOT NULL,
    check_name character varying(100) NOT NULL,
    check_type character varying(50) NOT NULL,
    status character varying(20) NOT NULL,
    message text,
    details jsonb,
    execution_time_ms integer,
    checked_at timestamp with time zone DEFAULT now(),
    created_at timestamp with time zone DEFAULT now()
);


--
-- Name: TABLE health_check_logs; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.health_check_logs IS 'System health check results and monitoring';


--
-- Name: health_check_logs_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.health_check_logs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: health_check_logs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.health_check_logs_id_seq OWNED BY public.health_check_logs.id;


--
-- Name: locations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.locations (
    id integer NOT NULL,
    location_code character varying(20) NOT NULL,
    name character varying(100) NOT NULL,
    type public.location_type NOT NULL,
    address text,
    coordinates character varying(50),
    qr_code_data jsonb NOT NULL,
    status character varying(20) DEFAULT 'active'::character varying NOT NULL,
    notes text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    is_active boolean GENERATED ALWAYS AS (((status)::text = 'active'::text)) STORED
);


--
-- Name: locations_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.locations_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: locations_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.locations_id_seq OWNED BY public.locations.id;


--
-- Name: migration_log; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.migration_log (
    id integer NOT NULL,
    migration_name character varying(255) NOT NULL,
    executed_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    description text
);


--
-- Name: migration_log_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.migration_log_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: migration_log_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.migration_log_id_seq OWNED BY public.migration_log.id;


--
-- Name: migrations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.migrations (
    id integer NOT NULL,
    filename character varying(255) NOT NULL,
    executed_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: migrations_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.migrations_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: migrations_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.migrations_id_seq OWNED BY public.migrations.id;


--
-- Name: trip_logs; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.trip_logs (
    id integer NOT NULL,
    assignment_id integer NOT NULL,
    trip_number integer NOT NULL,
    status public.trip_status DEFAULT 'assigned'::public.trip_status NOT NULL,
    loading_start_time timestamp without time zone,
    loading_end_time timestamp without time zone,
    unloading_start_time timestamp without time zone,
    unloading_end_time timestamp without time zone,
    trip_completed_time timestamp without time zone,
    actual_loading_location_id integer,
    actual_unloading_location_id integer,
    is_exception boolean DEFAULT false NOT NULL,
    exception_reason text,
    exception_approved_by integer,
    exception_approved_at timestamp without time zone,
    total_duration_minutes integer,
    loading_duration_minutes integer,
    travel_duration_minutes integer,
    unloading_duration_minutes integer,
    notes jsonb,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    location_sequence jsonb,
    is_extended_trip boolean DEFAULT false,
    workflow_type character varying(50) DEFAULT 'standard'::character varying,
    baseline_trip_id integer,
    cycle_number integer DEFAULT 1,
    performed_by_driver_id integer,
    performed_by_driver_name character varying(100),
    performed_by_employee_id character varying(20),
    performed_by_shift_id integer,
    performed_by_shift_type public.shift_type,
    stopped_reported_at timestamp without time zone,
    stopped_reason text,
    stopped_resolved_at timestamp without time zone,
    stopped_resolved_by integer,
    previous_status public.trip_status,
    CONSTRAINT chk_cycle_number CHECK ((cycle_number >= 1)),
    CONSTRAINT chk_duration_non_negative CHECK ((((total_duration_minutes IS NULL) OR (total_duration_minutes >= 0)) AND ((loading_duration_minutes IS NULL) OR (loading_duration_minutes >= 0)) AND ((travel_duration_minutes IS NULL) OR (travel_duration_minutes >= 0)) AND ((unloading_duration_minutes IS NULL) OR (unloading_duration_minutes >= 0)))),
    CONSTRAINT chk_trip_timing_sequence CHECK ((((loading_start_time IS NULL) OR (loading_end_time IS NULL) OR (loading_end_time >= loading_start_time)) AND ((loading_end_time IS NULL) OR (unloading_start_time IS NULL) OR (unloading_start_time >= loading_end_time)) AND ((unloading_start_time IS NULL) OR (unloading_end_time IS NULL) OR (unloading_end_time >= unloading_start_time)) AND ((unloading_end_time IS NULL) OR (trip_completed_time IS NULL) OR (trip_completed_time >= unloading_end_time)))),
    CONSTRAINT chk_workflow_type CHECK (((workflow_type)::text = ANY ((ARRAY['standard'::character varying, 'extended'::character varying, 'cycle'::character varying, 'dynamic'::character varying])::text[])))
);


--
-- Name: COLUMN trip_logs.location_sequence; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.trip_logs.location_sequence IS 'JSONB array storing complete route sequence with confirmation status';


--
-- Name: COLUMN trip_logs.is_extended_trip; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.trip_logs.is_extended_trip IS 'Boolean flag indicating if this trip is part of an extended workflow';


--
-- Name: COLUMN trip_logs.workflow_type; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.trip_logs.workflow_type IS 'Type of workflow: standard, extended, cycle, or dynamic';


--
-- Name: COLUMN trip_logs.baseline_trip_id; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.trip_logs.baseline_trip_id IS 'Reference to original A→B trip for extended workflows';


--
-- Name: COLUMN trip_logs.cycle_number; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.trip_logs.cycle_number IS 'Sequential number for cycle trips (1-based)';


--
-- Name: COLUMN trip_logs.performed_by_driver_id; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.trip_logs.performed_by_driver_id IS 'ID of driver who actually performed this trip';


--
-- Name: COLUMN trip_logs.performed_by_driver_name; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.trip_logs.performed_by_driver_name IS 'Name of driver who performed this trip (for historical accuracy)';


--
-- Name: COLUMN trip_logs.performed_by_employee_id; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.trip_logs.performed_by_employee_id IS 'Employee ID of driver who performed this trip';


--
-- Name: COLUMN trip_logs.performed_by_shift_id; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.trip_logs.performed_by_shift_id IS 'Shift ID during which this trip was performed';


--
-- Name: COLUMN trip_logs.performed_by_shift_type; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.trip_logs.performed_by_shift_type IS 'Type of shift (day/night) during trip execution';


--
-- Name: COLUMN trip_logs.stopped_reported_at; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.trip_logs.stopped_reported_at IS 'Timestamp when the trip was stopped/reported as having issues';


--
-- Name: COLUMN trip_logs.stopped_reason; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.trip_logs.stopped_reason IS 'Reason why the trip was stopped (mechanical issue, accident, etc.)';


--
-- Name: COLUMN trip_logs.stopped_resolved_at; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.trip_logs.stopped_resolved_at IS 'Timestamp when the stopped trip was resolved';


--
-- Name: COLUMN trip_logs.stopped_resolved_by; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.trip_logs.stopped_resolved_by IS 'User ID who resolved the stopped trip';


--
-- Name: mv_fleet_performance_summary; Type: MATERIALIZED VIEW; Schema: public; Owner: -
--

CREATE MATERIALIZED VIEW public.mv_fleet_performance_summary AS
 SELECT dt.truck_number,
    dt.id AS truck_id,
    d.full_name AS driver_name,
    ll.name AS loading_location,
    ul.name AS unloading_location,
        CASE
            WHEN ((tl.status = 'loading_start'::public.trip_status) AND (tl.loading_start_time IS NOT NULL)) THEN (EXTRACT(epoch FROM (now() - (tl.loading_start_time)::timestamp with time zone)) / (60)::numeric)
            WHEN ((tl.status = 'loading_end'::public.trip_status) AND (tl.loading_end_time IS NOT NULL)) THEN (EXTRACT(epoch FROM (now() - (tl.loading_end_time)::timestamp with time zone)) / (60)::numeric)
            WHEN ((tl.status = 'unloading_start'::public.trip_status) AND (tl.unloading_start_time IS NOT NULL)) THEN (EXTRACT(epoch FROM (now() - (tl.unloading_start_time)::timestamp with time zone)) / (60)::numeric)
            WHEN ((tl.status = 'unloading_end'::public.trip_status) AND (tl.unloading_end_time IS NOT NULL)) THEN (EXTRACT(epoch FROM (now() - (tl.unloading_end_time)::timestamp with time zone)) / (60)::numeric)
            WHEN ((tl.status = 'stopped'::public.trip_status) AND (tl.stopped_reported_at IS NOT NULL)) THEN (EXTRACT(epoch FROM (now() - (tl.stopped_reported_at)::timestamp with time zone)) / (60)::numeric)
            ELSE (0)::numeric
        END AS time_in_current_phase_minutes,
    tl.total_duration_minutes,
    tl.is_exception,
    tl.stopped_reason,
    a.priority,
    a.is_adaptive,
    now() AS last_updated
   FROM (((((public.trip_logs tl
     JOIN public.assignments a ON ((tl.assignment_id = a.id)))
     JOIN public.dump_trucks dt ON ((a.truck_id = dt.id)))
     LEFT JOIN public.drivers d ON ((a.driver_id = d.id)))
     LEFT JOIN public.locations ll ON ((a.loading_location_id = ll.id)))
     LEFT JOIN public.locations ul ON ((a.unloading_location_id = ul.id)))
  WHERE (tl.created_at >= (CURRENT_DATE - '7 days'::interval))
  ORDER BY tl.created_at DESC
  WITH NO DATA;


--
-- Name: MATERIALIZED VIEW mv_fleet_performance_summary; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON MATERIALIZED VIEW public.mv_fleet_performance_summary IS 'Fleet performance metrics with stopped terminology';


--
-- Name: mv_fleet_status_summary; Type: MATERIALIZED VIEW; Schema: public; Owner: -
--

CREATE MATERIALIZED VIEW public.mv_fleet_status_summary AS
 SELECT dt.id AS truck_id,
    dt.truck_number,
    dt.status AS truck_status,
    d.full_name AS driver_name,
    a.assignment_code,
    a.status AS assignment_status,
    tl.status AS current_trip_status,
    tl.trip_number,
    tl.created_at AS trip_started_at,
    COALESCE(al.name, ll.name) AS current_loading_location,
    COALESCE(aul.name, ul.name) AS current_unloading_location,
        CASE
            WHEN ((tl.status = 'loading_start'::public.trip_status) AND (tl.loading_start_time IS NOT NULL)) THEN (EXTRACT(epoch FROM (now() - (tl.loading_start_time)::timestamp with time zone)) / (60)::numeric)
            WHEN ((tl.status = 'loading_end'::public.trip_status) AND (tl.loading_end_time IS NOT NULL)) THEN (EXTRACT(epoch FROM (now() - (tl.loading_end_time)::timestamp with time zone)) / (60)::numeric)
            WHEN ((tl.status = 'unloading_start'::public.trip_status) AND (tl.unloading_start_time IS NOT NULL)) THEN (EXTRACT(epoch FROM (now() - (tl.unloading_start_time)::timestamp with time zone)) / (60)::numeric)
            WHEN ((tl.status = 'unloading_end'::public.trip_status) AND (tl.unloading_end_time IS NOT NULL)) THEN (EXTRACT(epoch FROM (now() - (tl.unloading_end_time)::timestamp with time zone)) / (60)::numeric)
            ELSE (0)::numeric
        END AS time_in_current_phase_minutes,
    tl.total_duration_minutes,
    tl.is_exception,
    a.priority,
    a.is_adaptive,
    now() AS last_updated
   FROM (((((((public.dump_trucks dt
     LEFT JOIN public.assignments a ON (((dt.id = a.truck_id) AND (a.status = ANY (ARRAY['assigned'::public.assignment_status, 'in_progress'::public.assignment_status])))))
     LEFT JOIN public.drivers d ON ((a.driver_id = d.id)))
     LEFT JOIN public.trip_logs tl ON (((a.id = tl.assignment_id) AND (tl.id = ( SELECT tl2.id
           FROM public.trip_logs tl2
          WHERE (tl2.assignment_id = a.id)
          ORDER BY tl2.created_at DESC
         LIMIT 1)))))
     LEFT JOIN public.locations ll ON ((a.loading_location_id = ll.id)))
     LEFT JOIN public.locations ul ON ((a.unloading_location_id = ul.id)))
     LEFT JOIN public.locations al ON ((tl.actual_loading_location_id = al.id)))
     LEFT JOIN public.locations aul ON ((tl.actual_unloading_location_id = aul.id)))
  WHERE (dt.status = 'active'::public.truck_status)
  WITH NO DATA;


--
-- Name: MATERIALIZED VIEW mv_fleet_status_summary; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON MATERIALIZED VIEW public.mv_fleet_status_summary IS 'Real-time fleet status for Analytics & Reports dashboard';


--
-- Name: mv_stopped_analytics_summary; Type: MATERIALIZED VIEW; Schema: public; Owner: -
--

CREATE MATERIALIZED VIEW public.mv_stopped_analytics_summary AS
 SELECT dt.truck_number,
    dt.id AS truck_id,
    d.full_name AS driver_name,
    count(*) AS total_stopped,
    avg((EXTRACT(epoch FROM (tl.stopped_resolved_at - tl.stopped_reported_at)) / (60)::numeric)) AS avg_resolution_time_minutes,
    max(tl.stopped_reported_at) AS last_stopped_date,
    count(
        CASE
            WHEN (tl.previous_status = 'loading_start'::public.trip_status) THEN 1
            ELSE NULL::integer
        END) AS loading_phase_stopped,
    count(
        CASE
            WHEN (tl.previous_status = 'loading_end'::public.trip_status) THEN 1
            ELSE NULL::integer
        END) AS travel_to_unload_stopped,
    count(
        CASE
            WHEN (tl.previous_status = 'unloading_start'::public.trip_status) THEN 1
            ELSE NULL::integer
        END) AS unloading_phase_stopped,
    count(
        CASE
            WHEN (tl.previous_status = 'unloading_end'::public.trip_status) THEN 1
            ELSE NULL::integer
        END) AS travel_to_load_stopped,
    mode() WITHIN GROUP (ORDER BY tl.stopped_reason) AS most_common_reason,
    count(DISTINCT tl.stopped_reason) AS unique_stopped_reasons,
    mode() WITHIN GROUP (ORDER BY (EXTRACT(hour FROM tl.stopped_reported_at))) AS most_common_hour,
    mode() WITHIN GROUP (ORDER BY (EXTRACT(dow FROM tl.stopped_reported_at))) AS most_common_day_of_week,
    avg(tl.total_duration_minutes) AS avg_trip_duration_with_stopped,
    now() AS last_updated
   FROM (((public.trip_logs tl
     JOIN public.assignments a ON ((tl.assignment_id = a.id)))
     JOIN public.dump_trucks dt ON ((a.truck_id = dt.id)))
     LEFT JOIN public.drivers d ON ((a.driver_id = d.id)))
  WHERE ((tl.status = 'stopped'::public.trip_status) AND (tl.stopped_reported_at >= (CURRENT_DATE - '90 days'::interval)))
  GROUP BY dt.truck_number, dt.id, d.full_name
  WITH NO DATA;


--
-- Name: MATERIALIZED VIEW mv_stopped_analytics_summary; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON MATERIALIZED VIEW public.mv_stopped_analytics_summary IS 'Summary analytics for stopped trips (formerly breakdown analytics)';


--
-- Name: mv_trip_performance_summary; Type: MATERIALIZED VIEW; Schema: public; Owner: -
--

CREATE MATERIALIZED VIEW public.mv_trip_performance_summary AS
 SELECT date_trunc('day'::text, tl.created_at) AS trip_date,
    dt.truck_number,
    d.full_name AS driver_name,
    ll.name AS loading_location,
    ul.name AS unloading_location,
    count(*) AS total_trips,
    count(
        CASE
            WHEN (tl.status = 'trip_completed'::public.trip_status) THEN 1
            ELSE NULL::integer
        END) AS completed_trips,
    count(
        CASE
            WHEN tl.is_exception THEN 1
            ELSE NULL::integer
        END) AS exception_trips,
    avg(tl.total_duration_minutes) AS avg_duration,
    avg(tl.loading_duration_minutes) AS avg_loading_duration,
    avg(tl.travel_duration_minutes) AS avg_travel_duration,
    avg(tl.unloading_duration_minutes) AS avg_unloading_duration,
    round((((count(
        CASE
            WHEN tl.is_exception THEN 1
            ELSE NULL::integer
        END))::numeric / (NULLIF(count(*), 0))::numeric) * (100)::numeric), 2) AS exception_rate_percent
   FROM (((((public.trip_logs tl
     JOIN public.assignments a ON ((tl.assignment_id = a.id)))
     JOIN public.dump_trucks dt ON ((a.truck_id = dt.id)))
     LEFT JOIN public.drivers d ON ((a.driver_id = d.id)))
     LEFT JOIN public.locations ll ON ((a.loading_location_id = ll.id)))
     LEFT JOIN public.locations ul ON ((a.unloading_location_id = ul.id)))
  WHERE (tl.created_at >= (CURRENT_DATE - '90 days'::interval))
  GROUP BY (date_trunc('day'::text, tl.created_at)), dt.truck_number, d.full_name, ll.name, ul.name
  WITH NO DATA;


--
-- Name: MATERIALIZED VIEW mv_trip_performance_summary; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON MATERIALIZED VIEW public.mv_trip_performance_summary IS 'Pre-aggregated trip performance data for fast dashboard queries';


--
-- Name: scan_logs; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.scan_logs (
    id integer NOT NULL,
    trip_log_id integer,
    scan_type public.scan_type NOT NULL,
    scanned_data text NOT NULL,
    scanned_location_id integer,
    scanned_truck_id integer,
    scanner_user_id integer,
    scan_timestamp timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    is_valid boolean DEFAULT true NOT NULL,
    validation_error text,
    ip_address inet,
    user_agent text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


--
-- Name: scan_logs_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.scan_logs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: scan_logs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.scan_logs_id_seq OWNED BY public.scan_logs.id;


--
-- Name: shift_handovers; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.shift_handovers (
    id integer NOT NULL,
    truck_id integer NOT NULL,
    outgoing_shift_id integer NOT NULL,
    incoming_shift_id integer NOT NULL,
    active_trip_id integer,
    trip_status_at_handover character varying(50),
    location_at_handover integer,
    handover_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    handover_notes text,
    fuel_level numeric(5,2),
    vehicle_condition text,
    approved_by integer,
    approved_at timestamp without time zone,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


--
-- Name: shift_handovers_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.shift_handovers_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: shift_handovers_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.shift_handovers_id_seq OWNED BY public.shift_handovers.id;


--
-- Name: system_health_logs; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.system_health_logs (
    id integer NOT NULL,
    module character varying(50) NOT NULL,
    status character varying(20) NOT NULL,
    issues jsonb,
    metrics jsonb,
    checked_at timestamp with time zone DEFAULT now(),
    CONSTRAINT system_health_logs_status_check CHECK (((status)::text = ANY ((ARRAY['operational'::character varying, 'warning'::character varying, 'critical'::character varying])::text[])))
);


--
-- Name: TABLE system_health_logs; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.system_health_logs IS 'Logs of system health status checks';


--
-- Name: COLUMN system_health_logs.module; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.system_health_logs.module IS 'Module that was checked (shift_management, assignment_management, trip_monitoring)';


--
-- Name: COLUMN system_health_logs.status; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.system_health_logs.status IS 'Health status of the module';


--
-- Name: COLUMN system_health_logs.issues; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.system_health_logs.issues IS 'Detailed information about detected issues in JSON format';


--
-- Name: COLUMN system_health_logs.metrics; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.system_health_logs.metrics IS 'Performance metrics and other data in JSON format';


--
-- Name: COLUMN system_health_logs.checked_at; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.system_health_logs.checked_at IS 'When the health check was performed';


--
-- Name: system_health_logs_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.system_health_logs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: system_health_logs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.system_health_logs_id_seq OWNED BY public.system_health_logs.id;


--
-- Name: system_logs; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.system_logs (
    id integer NOT NULL,
    log_type character varying(50) NOT NULL,
    message text NOT NULL,
    details jsonb,
    user_id integer,
    created_at timestamp with time zone DEFAULT now()
);


--
-- Name: TABLE system_logs; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.system_logs IS 'System-wide logging for automated and manual operations';


--
-- Name: COLUMN system_logs.log_type; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.system_logs.log_type IS 'Type of log entry (e.g., SHIFT_AUTO_ACTIVATION, SHIFT_MANUAL_COMPLETION)';


--
-- Name: COLUMN system_logs.details; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.system_logs.details IS 'Additional details in JSON format';


--
-- Name: system_logs_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.system_logs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: system_logs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.system_logs_id_seq OWNED BY public.system_logs.id;


--
-- Name: system_tasks; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.system_tasks (
    id integer NOT NULL,
    type character varying(50) NOT NULL,
    priority character varying(20) DEFAULT 'medium'::character varying NOT NULL,
    status character varying(20) DEFAULT 'pending'::character varying NOT NULL,
    title character varying(255) NOT NULL,
    description text,
    created_at timestamp with time zone DEFAULT now(),
    scheduled_for timestamp with time zone,
    completed_at timestamp with time zone,
    estimated_duration integer,
    auto_executable boolean DEFAULT false,
    metadata jsonb,
    created_by integer,
    CONSTRAINT system_tasks_priority_check CHECK (((priority)::text = ANY ((ARRAY['low'::character varying, 'medium'::character varying, 'high'::character varying, 'critical'::character varying])::text[]))),
    CONSTRAINT system_tasks_status_check CHECK (((status)::text = ANY ((ARRAY['pending'::character varying, 'in_progress'::character varying, 'completed'::character varying, 'failed'::character varying, 'cancelled'::character varying])::text[])))
);


--
-- Name: TABLE system_tasks; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.system_tasks IS 'Maintenance tasks for system health management';


--
-- Name: COLUMN system_tasks.type; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.system_tasks.type IS 'Type of task (maintenance, cleanup, monitoring, optimization)';


--
-- Name: COLUMN system_tasks.priority; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.system_tasks.priority IS 'Priority level of the task';


--
-- Name: COLUMN system_tasks.status; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.system_tasks.status IS 'Current status of the task';


--
-- Name: COLUMN system_tasks.title; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.system_tasks.title IS 'Short descriptive title of the task';


--
-- Name: COLUMN system_tasks.description; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.system_tasks.description IS 'Detailed description of the task';


--
-- Name: COLUMN system_tasks.scheduled_for; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.system_tasks.scheduled_for IS 'When the task is scheduled to be executed';


--
-- Name: COLUMN system_tasks.completed_at; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.system_tasks.completed_at IS 'When the task was completed';


--
-- Name: COLUMN system_tasks.estimated_duration; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.system_tasks.estimated_duration IS 'Estimated duration in seconds';


--
-- Name: COLUMN system_tasks.auto_executable; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.system_tasks.auto_executable IS 'Whether the task can be executed automatically';


--
-- Name: COLUMN system_tasks.metadata; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.system_tasks.metadata IS 'Additional task-specific data in JSON format';


--
-- Name: COLUMN system_tasks.created_by; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.system_tasks.created_by IS 'User who created the task';


--
-- Name: system_tasks_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.system_tasks_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: system_tasks_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.system_tasks_id_seq OWNED BY public.system_tasks.id;


--
-- Name: trip_logs_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.trip_logs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: trip_logs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.trip_logs_id_seq OWNED BY public.trip_logs.id;


--
-- Name: users; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.users (
    id integer NOT NULL,
    username character varying(50) NOT NULL,
    email character varying(100) NOT NULL,
    password_hash character varying(255) NOT NULL,
    full_name character varying(100) NOT NULL,
    role public.user_role DEFAULT 'operator'::public.user_role NOT NULL,
    status character varying(20) DEFAULT 'active'::character varying NOT NULL,
    last_login timestamp without time zone,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    CONSTRAINT users_status_check CHECK (((status)::text = ANY ((ARRAY['active'::character varying, 'inactive'::character varying])::text[])))
);


--
-- Name: users_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.users_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: users_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.users_id_seq OWNED BY public.users.id;


--
-- Name: v_active_exceptions; Type: VIEW; Schema: public; Owner: -
--

CREATE VIEW public.v_active_exceptions AS
 SELECT a.id AS approval_id,
    a.trip_log_id,
    a.exception_type,
    a.exception_description,
    COALESCE(a.severity, 'medium'::character varying) AS severity,
    a.status AS approval_status,
    a.requested_at,
    a.reviewed_at,
    (EXTRACT(epoch FROM (COALESCE((a.reviewed_at)::timestamp with time zone, CURRENT_TIMESTAMP) - (a.requested_at)::timestamp with time zone)) / (3600)::numeric) AS resolution_hours,
    tl.trip_number,
    tl.status AS trip_status,
    dt.truck_number,
    d.full_name AS driver_name,
    ll.name AS assigned_loading_location,
    ul.name AS assigned_unloading_location,
    al.name AS actual_loading_location,
    aul.name AS actual_unloading_location,
    u1.full_name AS reported_by_name,
    u2.full_name AS reviewed_by_name
   FROM ((((((((((public.approvals a
     JOIN public.trip_logs tl ON ((a.trip_log_id = tl.id)))
     JOIN public.assignments ass ON ((tl.assignment_id = ass.id)))
     JOIN public.dump_trucks dt ON ((ass.truck_id = dt.id)))
     LEFT JOIN public.drivers d ON ((ass.driver_id = d.id)))
     LEFT JOIN public.locations ll ON ((ass.loading_location_id = ll.id)))
     LEFT JOIN public.locations ul ON ((ass.unloading_location_id = ul.id)))
     LEFT JOIN public.locations al ON ((tl.actual_loading_location_id = al.id)))
     LEFT JOIN public.locations aul ON ((tl.actual_unloading_location_id = aul.id)))
     LEFT JOIN public.users u1 ON ((a.reported_by = u1.id)))
     LEFT JOIN public.users u2 ON ((a.reviewed_by = u2.id)))
  WHERE (a.created_at >= (CURRENT_DATE - '7 days'::interval))
  ORDER BY a.created_at DESC;


--
-- Name: VIEW v_active_exceptions; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON VIEW public.v_active_exceptions IS 'Real-time view of active exceptions and their resolution status';


--
-- Name: v_dynamic_assignment_analytics; Type: VIEW; Schema: public; Owner: -
--

CREATE VIEW public.v_dynamic_assignment_analytics AS
 SELECT count(*) AS total_adaptive_assignments,
    count(
        CASE
            WHEN (status = 'assigned'::public.assignment_status) THEN 1
            ELSE NULL::integer
        END) AS active_adaptive_assignments,
    count(
        CASE
            WHEN (status = 'pending_approval'::public.assignment_status) THEN 1
            ELSE NULL::integer
        END) AS pending_adaptive_assignments,
    count(
        CASE
            WHEN ((adaptation_strategy)::text = 'pattern_based'::text) THEN 1
            ELSE NULL::integer
        END) AS pattern_based_count,
    count(
        CASE
            WHEN ((adaptation_strategy)::text = 'proximity_based'::text) THEN 1
            ELSE NULL::integer
        END) AS proximity_based_count,
    count(
        CASE
            WHEN ((adaptation_strategy)::text = 'efficiency_based'::text) THEN 1
            ELSE NULL::integer
        END) AS efficiency_based_count,
    count(
        CASE
            WHEN ((adaptation_strategy)::text = 'manual_override'::text) THEN 1
            ELSE NULL::integer
        END) AS manual_override_count,
    count(
        CASE
            WHEN ((adaptation_confidence)::text = 'high'::text) THEN 1
            ELSE NULL::integer
        END) AS high_confidence_count,
    count(
        CASE
            WHEN ((adaptation_confidence)::text = 'medium'::text) THEN 1
            ELSE NULL::integer
        END) AS medium_confidence_count,
    count(
        CASE
            WHEN ((adaptation_confidence)::text = 'low'::text) THEN 1
            ELSE NULL::integer
        END) AS low_confidence_count,
    round(avg(
        CASE
            WHEN (created_at >= (CURRENT_DATE - '7 days'::interval)) THEN 1
            ELSE 0
        END), 2) AS weekly_creation_rate,
    round((((count(
        CASE
            WHEN (status = 'assigned'::public.assignment_status) THEN 1
            ELSE NULL::integer
        END))::numeric / (NULLIF(count(*), 0))::numeric) * (100)::numeric), 2) AS success_rate_percent,
    CURRENT_TIMESTAMP AS last_updated
   FROM public.assignments
  WHERE (is_adaptive = true);


--
-- Name: v_realtime_dashboard; Type: VIEW; Schema: public; Owner: -
--

CREATE VIEW public.v_realtime_dashboard AS
 SELECT ( SELECT count(*) AS count
           FROM public.trip_logs
          WHERE ((trip_logs.status = ANY (ARRAY['loading_start'::public.trip_status, 'loading_end'::public.trip_status, 'unloading_start'::public.trip_status, 'unloading_end'::public.trip_status])) AND (date(trip_logs.created_at) = CURRENT_DATE))) AS active_trips,
    ( SELECT count(*) AS count
           FROM public.approvals
          WHERE (approvals.status = 'pending'::public.approval_status)) AS pending_exceptions,
    ( SELECT count(*) AS count
           FROM public.trip_logs
          WHERE ((trip_logs.status = 'trip_completed'::public.trip_status) AND (date(trip_logs.created_at) = CURRENT_DATE))) AS completed_trips_today,
    round((((( SELECT count(*) AS count
           FROM public.trip_logs
          WHERE ((trip_logs.is_exception = true) AND (date(trip_logs.created_at) = CURRENT_DATE))))::numeric / (NULLIF(( SELECT count(*) AS count
           FROM public.trip_logs
          WHERE (date(trip_logs.created_at) = CURRENT_DATE)), 0))::numeric) * (100)::numeric), 2) AS exception_rate_today,
    round(( SELECT avg(trip_logs.total_duration_minutes) AS avg
           FROM public.trip_logs
          WHERE ((trip_logs.status = 'trip_completed'::public.trip_status) AND (date(trip_logs.created_at) = CURRENT_DATE))), 2) AS avg_trip_duration_today,
    ( SELECT count(DISTINCT a.truck_id) AS count
           FROM public.assignments a
          WHERE ((a.status = ANY (ARRAY['assigned'::public.assignment_status, 'in_progress'::public.assignment_status])) AND (a.assigned_date = CURRENT_DATE))) AS active_trucks,
    CURRENT_TIMESTAMP AS last_updated;


--
-- Name: VIEW v_realtime_dashboard; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON VIEW public.v_realtime_dashboard IS 'Real-time dashboard metrics for operational monitoring';


--
-- Name: v_trip_performance; Type: VIEW; Schema: public; Owner: -
--

CREATE VIEW public.v_trip_performance AS
 SELECT date(tl.created_at) AS trip_date,
    dt.truck_number,
    d.full_name AS driver_name,
    count(DISTINCT tl.id) AS total_trips,
    count(DISTINCT
        CASE
            WHEN (tl.status = 'trip_completed'::public.trip_status) THEN tl.id
            ELSE NULL::integer
        END) AS completed_trips,
    count(DISTINCT
        CASE
            WHEN tl.is_exception THEN tl.id
            ELSE NULL::integer
        END) AS exception_trips,
    avg(tl.total_duration_minutes) AS avg_trip_duration,
    avg(tl.loading_duration_minutes) AS avg_loading_time,
    avg(tl.unloading_duration_minutes) AS avg_unloading_time,
    avg(tl.travel_duration_minutes) AS avg_travel_time,
    round((((count(DISTINCT
        CASE
            WHEN tl.is_exception THEN tl.id
            ELSE NULL::integer
        END))::numeric / (NULLIF(count(DISTINCT tl.id), 0))::numeric) * (100)::numeric), 2) AS exception_rate
   FROM (((public.trip_logs tl
     JOIN public.assignments a ON ((tl.assignment_id = a.id)))
     JOIN public.dump_trucks dt ON ((a.truck_id = dt.id)))
     LEFT JOIN public.drivers d ON ((a.driver_id = d.id)))
  WHERE (tl.created_at >= (CURRENT_DATE - '30 days'::interval))
  GROUP BY (date(tl.created_at)), dt.truck_number, d.full_name
  ORDER BY (date(tl.created_at)) DESC, dt.truck_number;


--
-- Name: VIEW v_trip_performance; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON VIEW public.v_trip_performance IS 'Trip performance metrics aggregated by date and truck';


--
-- Name: v_trip_summary; Type: VIEW; Schema: public; Owner: -
--

CREATE VIEW public.v_trip_summary AS
 SELECT tl.id,
    a.assignment_code,
    a.assigned_date,
    dt.truck_number,
    d.full_name AS driver_name,
    tl.trip_number,
    tl.status,
    tl.previous_status,
    tl.loading_start_time,
    tl.trip_completed_time,
    tl.total_duration_minutes,
    tl.loading_duration_minutes,
    tl.travel_duration_minutes,
    tl.unloading_duration_minutes,
    tl.is_exception,
    tl.exception_reason,
    tl.stopped_reported_at,
    tl.stopped_reason,
    tl.stopped_resolved_at,
    ll.name AS loading_location,
    ul.name AS unloading_location,
    COALESCE(al.name, ll.name) AS actual_loading_location,
    COALESCE(aul.name, ul.name) AS actual_unloading_location
   FROM (((((((public.trip_logs tl
     JOIN public.assignments a ON ((tl.assignment_id = a.id)))
     JOIN public.dump_trucks dt ON ((a.truck_id = dt.id)))
     JOIN public.drivers d ON ((a.driver_id = d.id)))
     LEFT JOIN public.locations ll ON ((a.loading_location_id = ll.id)))
     LEFT JOIN public.locations ul ON ((a.unloading_location_id = ul.id)))
     LEFT JOIN public.locations al ON ((tl.actual_loading_location_id = al.id)))
     LEFT JOIN public.locations aul ON ((tl.actual_unloading_location_id = aul.id)))
  ORDER BY tl.created_at DESC;


--
-- Name: v_workflow_analytics; Type: VIEW; Schema: public; Owner: -
--

CREATE VIEW public.v_workflow_analytics AS
 SELECT workflow_type,
    count(*) AS total_trips,
    count(
        CASE
            WHEN (status = 'trip_completed'::public.trip_status) THEN 1
            ELSE NULL::integer
        END) AS completed_trips,
    avg(total_duration_minutes) AS avg_duration_minutes,
    avg(cycle_number) AS avg_cycle_number
   FROM public.trip_logs
  WHERE (workflow_type IS NOT NULL)
  GROUP BY workflow_type;


--
-- Name: VIEW v_workflow_analytics; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON VIEW public.v_workflow_analytics IS 'Analytics view for multi-location workflow performance metrics';


--
-- Name: approvals id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.approvals ALTER COLUMN id SET DEFAULT nextval('public.approvals_id_seq'::regclass);


--
-- Name: assignments id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.assignments ALTER COLUMN id SET DEFAULT nextval('public.assignments_id_seq'::regclass);


--
-- Name: automated_fix_logs id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.automated_fix_logs ALTER COLUMN id SET DEFAULT nextval('public.automated_fix_logs_id_seq'::regclass);


--
-- Name: driver_shifts id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_shifts ALTER COLUMN id SET DEFAULT nextval('public.driver_shifts_id_seq'::regclass);


--
-- Name: drivers id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.drivers ALTER COLUMN id SET DEFAULT nextval('public.drivers_id_seq'::regclass);


--
-- Name: dump_trucks id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dump_trucks ALTER COLUMN id SET DEFAULT nextval('public.dump_trucks_id_seq'::regclass);


--
-- Name: health_check_logs id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.health_check_logs ALTER COLUMN id SET DEFAULT nextval('public.health_check_logs_id_seq'::regclass);


--
-- Name: locations id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.locations ALTER COLUMN id SET DEFAULT nextval('public.locations_id_seq'::regclass);


--
-- Name: migration_log id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.migration_log ALTER COLUMN id SET DEFAULT nextval('public.migration_log_id_seq'::regclass);


--
-- Name: migrations id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.migrations ALTER COLUMN id SET DEFAULT nextval('public.migrations_id_seq'::regclass);


--
-- Name: scan_logs id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.scan_logs ALTER COLUMN id SET DEFAULT nextval('public.scan_logs_id_seq'::regclass);


--
-- Name: shift_handovers id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shift_handovers ALTER COLUMN id SET DEFAULT nextval('public.shift_handovers_id_seq'::regclass);


--
-- Name: system_health_logs id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.system_health_logs ALTER COLUMN id SET DEFAULT nextval('public.system_health_logs_id_seq'::regclass);


--
-- Name: system_logs id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.system_logs ALTER COLUMN id SET DEFAULT nextval('public.system_logs_id_seq'::regclass);


--
-- Name: system_tasks id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.system_tasks ALTER COLUMN id SET DEFAULT nextval('public.system_tasks_id_seq'::regclass);


--
-- Name: trip_logs id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.trip_logs ALTER COLUMN id SET DEFAULT nextval('public.trip_logs_id_seq'::regclass);


--
-- Name: users id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users ALTER COLUMN id SET DEFAULT nextval('public.users_id_seq'::regclass);


--
-- Name: approvals approvals_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.approvals
    ADD CONSTRAINT approvals_pkey PRIMARY KEY (id);


--
-- Name: assignments assignments_assignment_code_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.assignments
    ADD CONSTRAINT assignments_assignment_code_key UNIQUE (assignment_code);


--
-- Name: assignments assignments_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.assignments
    ADD CONSTRAINT assignments_pkey PRIMARY KEY (id);


--
-- Name: automated_fix_logs automated_fix_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.automated_fix_logs
    ADD CONSTRAINT automated_fix_logs_pkey PRIMARY KEY (id);


--
-- Name: driver_shifts driver_shifts_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_shifts
    ADD CONSTRAINT driver_shifts_pkey PRIMARY KEY (id);


--
-- Name: drivers drivers_employee_id_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.drivers
    ADD CONSTRAINT drivers_employee_id_key UNIQUE (employee_id);


--
-- Name: drivers drivers_license_number_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.drivers
    ADD CONSTRAINT drivers_license_number_key UNIQUE (license_number);


--
-- Name: drivers drivers_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.drivers
    ADD CONSTRAINT drivers_pkey PRIMARY KEY (id);


--
-- Name: dump_trucks dump_trucks_license_plate_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dump_trucks
    ADD CONSTRAINT dump_trucks_license_plate_key UNIQUE (license_plate);


--
-- Name: dump_trucks dump_trucks_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dump_trucks
    ADD CONSTRAINT dump_trucks_pkey PRIMARY KEY (id);


--
-- Name: dump_trucks dump_trucks_truck_number_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dump_trucks
    ADD CONSTRAINT dump_trucks_truck_number_key UNIQUE (truck_number);


--
-- Name: health_check_logs health_check_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.health_check_logs
    ADD CONSTRAINT health_check_logs_pkey PRIMARY KEY (id);


--
-- Name: locations locations_location_code_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.locations
    ADD CONSTRAINT locations_location_code_key UNIQUE (location_code);


--
-- Name: locations locations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.locations
    ADD CONSTRAINT locations_pkey PRIMARY KEY (id);


--
-- Name: migration_log migration_log_migration_name_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.migration_log
    ADD CONSTRAINT migration_log_migration_name_key UNIQUE (migration_name);


--
-- Name: migration_log migration_log_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.migration_log
    ADD CONSTRAINT migration_log_pkey PRIMARY KEY (id);


--
-- Name: migrations migrations_filename_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.migrations
    ADD CONSTRAINT migrations_filename_key UNIQUE (filename);


--
-- Name: migrations migrations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.migrations
    ADD CONSTRAINT migrations_pkey PRIMARY KEY (id);


--
-- Name: scan_logs scan_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.scan_logs
    ADD CONSTRAINT scan_logs_pkey PRIMARY KEY (id);


--
-- Name: shift_handovers shift_handovers_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shift_handovers
    ADD CONSTRAINT shift_handovers_pkey PRIMARY KEY (id);


--
-- Name: system_health_logs system_health_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.system_health_logs
    ADD CONSTRAINT system_health_logs_pkey PRIMARY KEY (id);


--
-- Name: system_logs system_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.system_logs
    ADD CONSTRAINT system_logs_pkey PRIMARY KEY (id);


--
-- Name: system_tasks system_tasks_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.system_tasks
    ADD CONSTRAINT system_tasks_pkey PRIMARY KEY (id);


--
-- Name: trip_logs trip_logs_assignment_id_trip_number_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.trip_logs
    ADD CONSTRAINT trip_logs_assignment_id_trip_number_key UNIQUE (assignment_id, trip_number);


--
-- Name: trip_logs trip_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.trip_logs
    ADD CONSTRAINT trip_logs_pkey PRIMARY KEY (id);


--
-- Name: users users_email_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_email_key UNIQUE (email);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: users users_username_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_username_key UNIQUE (username);


--
-- Name: idx_approvals_adaptation_confidence; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_approvals_adaptation_confidence ON public.approvals USING btree (adaptation_confidence) WHERE (adaptation_confidence IS NOT NULL);


--
-- Name: idx_approvals_adaptation_strategy; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_approvals_adaptation_strategy ON public.approvals USING btree (adaptation_strategy) WHERE (adaptation_strategy IS NOT NULL);


--
-- Name: idx_approvals_adaptive; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_approvals_adaptive ON public.approvals USING btree (is_adaptive_exception) WHERE (is_adaptive_exception = true);


--
-- Name: idx_approvals_adaptive_metadata_gin; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_approvals_adaptive_metadata_gin ON public.approvals USING gin (adaptation_metadata) WHERE (adaptation_metadata IS NOT NULL);


--
-- Name: idx_approvals_auto_approved; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_approvals_auto_approved ON public.approvals USING btree (auto_approved) WHERE (auto_approved = true);


--
-- Name: idx_approvals_reported_by; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_approvals_reported_by ON public.approvals USING btree (reported_by);


--
-- Name: idx_approvals_requested_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_approvals_requested_at ON public.approvals USING btree (requested_at);


--
-- Name: idx_approvals_severity; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_approvals_severity ON public.approvals USING btree (severity);


--
-- Name: idx_approvals_severity_created; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_approvals_severity_created ON public.approvals USING btree (severity, created_at) WHERE (status = 'pending'::public.approval_status);


--
-- Name: idx_approvals_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_approvals_status ON public.approvals USING btree (status);


--
-- Name: idx_approvals_suggested_assignment; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_approvals_suggested_assignment ON public.approvals USING btree (suggested_assignment_id) WHERE (suggested_assignment_id IS NOT NULL);


--
-- Name: idx_approvals_trip; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_approvals_trip ON public.approvals USING btree (trip_log_id);


--
-- Name: idx_approvals_trip_status_created; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_approvals_trip_status_created ON public.approvals USING btree (trip_log_id, status, created_at);


--
-- Name: idx_assignments_active_operations; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_assignments_active_operations ON public.assignments USING btree (truck_id, status, created_at) WHERE (status = ANY (ARRAY['assigned'::public.assignment_status, 'in_progress'::public.assignment_status]));


--
-- Name: idx_assignments_adaptation_confidence; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_assignments_adaptation_confidence ON public.assignments USING btree (adaptation_confidence) WHERE (adaptation_confidence IS NOT NULL);


--
-- Name: idx_assignments_adaptation_metadata_gin; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_assignments_adaptation_metadata_gin ON public.assignments USING gin (adaptation_metadata) WHERE (adaptation_metadata IS NOT NULL);


--
-- Name: idx_assignments_adaptation_strategy; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_assignments_adaptation_strategy ON public.assignments USING btree (adaptation_strategy) WHERE (adaptation_strategy IS NOT NULL);


--
-- Name: idx_assignments_adaptive; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_assignments_adaptive ON public.assignments USING btree (is_adaptive) WHERE (is_adaptive = true);


--
-- Name: idx_assignments_adaptive_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_assignments_adaptive_status ON public.assignments USING btree (is_adaptive, status, created_at) WHERE (is_adaptive = true);


--
-- Name: idx_assignments_assigned_date_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_assignments_assigned_date_status ON public.assignments USING btree (assigned_date DESC, status);


--
-- Name: idx_assignments_assignment_code; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_assignments_assignment_code ON public.assignments USING btree (assignment_code);


--
-- Name: idx_assignments_auto_created; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_assignments_auto_created ON public.assignments USING btree (auto_created) WHERE (auto_created = true);


--
-- Name: idx_assignments_date; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_assignments_date ON public.assignments USING btree (assigned_date);


--
-- Name: idx_assignments_driver; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_assignments_driver ON public.assignments USING btree (driver_id);


--
-- Name: idx_assignments_exact_duplicate; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX idx_assignments_exact_duplicate ON public.assignments USING btree (truck_id, loading_location_id, unloading_location_id) WHERE (status = ANY (ARRAY['assigned'::public.assignment_status, 'in_progress'::public.assignment_status]));


--
-- Name: idx_assignments_locations_date; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_assignments_locations_date ON public.assignments USING btree (loading_location_id, assigned_date, status);


--
-- Name: idx_assignments_priority; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_assignments_priority ON public.assignments USING btree (priority);


--
-- Name: idx_assignments_shift; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_assignments_shift ON public.assignments USING btree (shift_id) WHERE (shift_id IS NOT NULL);


--
-- Name: idx_assignments_shift_truck; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_assignments_shift_truck ON public.assignments USING btree (truck_id, shift_id) WHERE (is_shift_assignment = true);


--
-- Name: idx_assignments_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_assignments_status ON public.assignments USING btree (status);


--
-- Name: idx_assignments_status_assigned_date; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_assignments_status_assigned_date ON public.assignments USING btree (status, assigned_date) WHERE (status = ANY (ARRAY['assigned'::public.assignment_status, 'in_progress'::public.assignment_status]));


--
-- Name: idx_assignments_status_date; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_assignments_status_date ON public.assignments USING btree (status, assigned_date) WHERE (status = ANY (ARRAY['assigned'::public.assignment_status, 'in_progress'::public.assignment_status]));


--
-- Name: idx_assignments_truck; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_assignments_truck ON public.assignments USING btree (truck_id);


--
-- Name: idx_assignments_truck_driver_date; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_assignments_truck_driver_date ON public.assignments USING btree (truck_id, driver_id, assigned_date);


--
-- Name: idx_assignments_truck_driver_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_assignments_truck_driver_status ON public.assignments USING btree (truck_id, driver_id, status);


--
-- Name: idx_assignments_truck_id_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_assignments_truck_id_status ON public.assignments USING btree (truck_id, status);


--
-- Name: idx_assignments_truck_locations; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_assignments_truck_locations ON public.assignments USING btree (truck_id, loading_location_id, unloading_location_id, assigned_date);


--
-- Name: idx_automated_fix_logs_executed_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_automated_fix_logs_executed_at ON public.automated_fix_logs USING btree (executed_at DESC);


--
-- Name: idx_automated_fix_logs_module_name; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_automated_fix_logs_module_name ON public.automated_fix_logs USING btree (module_name);


--
-- Name: idx_automated_fix_logs_success; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_automated_fix_logs_success ON public.automated_fix_logs USING btree (success);


--
-- Name: idx_baseline_trip; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_baseline_trip ON public.trip_logs USING btree (baseline_trip_id) WHERE (baseline_trip_id IS NOT NULL);


--
-- Name: idx_driver_shifts_date_range; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_shifts_date_range ON public.driver_shifts USING btree (start_date, end_date, recurrence_pattern);


--
-- Name: idx_driver_shifts_date_range_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_shifts_date_range_status ON public.driver_shifts USING btree (start_date, end_date, status) WHERE (status = ANY (ARRAY['active'::public.shift_status, 'scheduled'::public.shift_status]));


--
-- Name: idx_driver_shifts_driver_truck_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_shifts_driver_truck_status ON public.driver_shifts USING btree (driver_id, truck_id, status);


--
-- Name: idx_driver_shifts_end_date; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_shifts_end_date ON public.driver_shifts USING btree (end_date);


--
-- Name: idx_driver_shifts_recurrence_active; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_shifts_recurrence_active ON public.driver_shifts USING btree (recurrence_pattern, status) WHERE (status = ANY (ARRAY['scheduled'::public.shift_status, 'active'::public.shift_status]));


--
-- Name: idx_driver_shifts_recurrence_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_shifts_recurrence_status ON public.driver_shifts USING btree (recurrence_pattern, status, start_date, end_date);


--
-- Name: idx_driver_shifts_shift_date; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_shifts_shift_date ON public.driver_shifts USING btree (shift_date);


--
-- Name: idx_driver_shifts_start_date; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_shifts_start_date ON public.driver_shifts USING btree (start_date);


--
-- Name: idx_driver_shifts_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_shifts_status ON public.driver_shifts USING btree (status);


--
-- Name: idx_driver_shifts_status_date; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_shifts_status_date ON public.driver_shifts USING btree (status, start_date, end_date);


--
-- Name: idx_driver_shifts_time_range; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_shifts_time_range ON public.driver_shifts USING btree (start_time, end_time);


--
-- Name: idx_driver_shifts_truck_shift_date; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_shifts_truck_shift_date ON public.driver_shifts USING btree (truck_id, shift_date);


--
-- Name: idx_driver_shifts_truck_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_shifts_truck_status ON public.driver_shifts USING btree (truck_id, status) WHERE (status = ANY (ARRAY['active'::public.shift_status, 'scheduled'::public.shift_status]));


--
-- Name: idx_driver_shifts_truck_status_date_time; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_shifts_truck_status_date_time ON public.driver_shifts USING btree (truck_id, status, start_date, end_date, start_time, end_time);


--
-- Name: idx_driver_shifts_unified_date_range; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_shifts_unified_date_range ON public.driver_shifts USING btree (truck_id, start_date, end_date, status);


--
-- Name: idx_driver_shifts_unified_range; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_shifts_unified_range ON public.driver_shifts USING btree (truck_id, driver_id, start_date, end_date, status);


--
-- Name: idx_drivers_active_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_drivers_active_status ON public.drivers USING btree (status, employee_id) WHERE (status = 'active'::public.driver_status);


--
-- Name: idx_drivers_employee_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_drivers_employee_id ON public.drivers USING btree (employee_id);


--
-- Name: idx_drivers_license; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_drivers_license ON public.drivers USING btree (license_number);


--
-- Name: idx_drivers_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_drivers_status ON public.drivers USING btree (status);


--
-- Name: idx_health_check_logs_check_name; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_health_check_logs_check_name ON public.health_check_logs USING btree (check_name);


--
-- Name: idx_health_check_logs_checked_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_health_check_logs_checked_at ON public.health_check_logs USING btree (checked_at);


--
-- Name: idx_health_check_logs_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_health_check_logs_status ON public.health_check_logs USING btree (status);


--
-- Name: idx_location_sequence; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_location_sequence ON public.trip_logs USING gin (location_sequence);


--
-- Name: idx_locations_active; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_locations_active ON public.locations USING btree (status) WHERE ((status)::text = 'active'::text);


--
-- Name: idx_locations_active_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_locations_active_type ON public.locations USING btree (type, location_code) WHERE ((status)::text = 'active'::text);


--
-- Name: idx_locations_code; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_locations_code ON public.locations USING btree (location_code);


--
-- Name: idx_locations_is_active; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_locations_is_active ON public.locations USING btree (is_active) WHERE (is_active = true);


--
-- Name: idx_locations_qr_data_gin; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_locations_qr_data_gin ON public.locations USING gin (qr_code_data) WHERE (qr_code_data IS NOT NULL);


--
-- Name: idx_locations_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_locations_type ON public.locations USING btree (type);


--
-- Name: idx_mv_fleet_status_trip_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_mv_fleet_status_trip_status ON public.mv_fleet_status_summary USING btree (current_trip_status, truck_number);


--
-- Name: idx_mv_fleet_status_truck_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_mv_fleet_status_truck_id ON public.mv_fleet_status_summary USING btree (truck_id);


--
-- Name: idx_mv_trip_performance_unique; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX idx_mv_trip_performance_unique ON public.mv_trip_performance_summary USING btree (trip_date, truck_number, COALESCE(driver_name, 'Unknown'::character varying), loading_location, unloading_location);


--
-- Name: idx_scan_logs_timestamp_user; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_scan_logs_timestamp_user ON public.scan_logs USING btree (scan_timestamp DESC, scanner_user_id);


--
-- Name: INDEX idx_scan_logs_timestamp_user; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON INDEX public.idx_scan_logs_timestamp_user IS 'Optimized for scan log queries by timestamp and user';


--
-- Name: idx_scans_location; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_scans_location ON public.scan_logs USING btree (scanned_location_id);


--
-- Name: idx_scans_timestamp; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_scans_timestamp ON public.scan_logs USING btree (scan_timestamp);


--
-- Name: idx_scans_trip; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_scans_trip ON public.scan_logs USING btree (trip_log_id);


--
-- Name: idx_scans_trip_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_scans_trip_type ON public.scan_logs USING btree (trip_log_id, scan_type) WHERE (trip_log_id IS NOT NULL);


--
-- Name: idx_scans_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_scans_type ON public.scan_logs USING btree (scan_type);


--
-- Name: idx_scans_user_valid_timestamp; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_scans_user_valid_timestamp ON public.scan_logs USING btree (scanner_user_id, is_valid, scan_timestamp);


--
-- Name: idx_shift_handovers_time; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_shift_handovers_time ON public.shift_handovers USING btree (handover_time);


--
-- Name: idx_shift_handovers_trip; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_shift_handovers_trip ON public.shift_handovers USING btree (active_trip_id);


--
-- Name: idx_shift_handovers_truck; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_shift_handovers_truck ON public.shift_handovers USING btree (truck_id);


--
-- Name: idx_system_health_logs_checked_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_system_health_logs_checked_at ON public.system_health_logs USING btree (checked_at DESC);


--
-- Name: idx_system_health_logs_module; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_system_health_logs_module ON public.system_health_logs USING btree (module);


--
-- Name: idx_system_health_logs_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_system_health_logs_status ON public.system_health_logs USING btree (status);


--
-- Name: idx_system_logs_created_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_system_logs_created_at ON public.system_logs USING btree (created_at);


--
-- Name: idx_system_logs_log_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_system_logs_log_type ON public.system_logs USING btree (log_type);


--
-- Name: idx_system_logs_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_system_logs_user_id ON public.system_logs USING btree (user_id);


--
-- Name: idx_system_tasks_auto_executable; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_system_tasks_auto_executable ON public.system_tasks USING btree (auto_executable) WHERE (auto_executable = true);


--
-- Name: idx_system_tasks_created_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_system_tasks_created_at ON public.system_tasks USING btree (created_at DESC);


--
-- Name: idx_system_tasks_priority; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_system_tasks_priority ON public.system_tasks USING btree (priority);


--
-- Name: idx_system_tasks_scheduled_for; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_system_tasks_scheduled_for ON public.system_tasks USING btree (scheduled_for) WHERE (scheduled_for IS NOT NULL);


--
-- Name: idx_system_tasks_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_system_tasks_status ON public.system_tasks USING btree (status);


--
-- Name: idx_system_tasks_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_system_tasks_type ON public.system_tasks USING btree (type);


--
-- Name: idx_trip_logs_assignment_status_exception; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trip_logs_assignment_status_exception ON public.trip_logs USING btree (assignment_id, status, is_exception, created_at);


--
-- Name: idx_trip_logs_duration_metrics; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trip_logs_duration_metrics ON public.trip_logs USING btree (total_duration_minutes, loading_duration_minutes) WHERE (total_duration_minutes IS NOT NULL);


--
-- Name: INDEX idx_trip_logs_duration_metrics; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON INDEX public.idx_trip_logs_duration_metrics IS 'Performance index for duration-based analytics';


--
-- Name: idx_trip_logs_notes_gin; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trip_logs_notes_gin ON public.trip_logs USING gin (notes) WHERE (notes IS NOT NULL);


--
-- Name: idx_trip_logs_performed_by_driver; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trip_logs_performed_by_driver ON public.trip_logs USING btree (performed_by_driver_id);


--
-- Name: idx_trip_logs_performed_by_employee; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trip_logs_performed_by_employee ON public.trip_logs USING btree (performed_by_employee_id);


--
-- Name: idx_trip_logs_performed_by_shift; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trip_logs_performed_by_shift ON public.trip_logs USING btree (performed_by_shift_id);


--
-- Name: idx_trip_logs_stopped; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trip_logs_stopped ON public.trip_logs USING btree (status) WHERE (status = 'stopped'::public.trip_status);


--
-- Name: idx_trip_logs_stopped_date; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trip_logs_stopped_date ON public.trip_logs USING btree (stopped_reported_at) WHERE (stopped_reported_at IS NOT NULL);


--
-- Name: idx_trip_logs_stopped_reported_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trip_logs_stopped_reported_at ON public.trip_logs USING btree (stopped_reported_at);


--
-- Name: idx_trip_logs_stopped_resolved_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trip_logs_stopped_resolved_at ON public.trip_logs USING btree (stopped_resolved_at);


--
-- Name: idx_trip_logs_stopped_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trip_logs_stopped_status ON public.trip_logs USING btree (status) WHERE (status = 'stopped'::public.trip_status);


--
-- Name: idx_trips_actual_locations; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trips_actual_locations ON public.trip_logs USING btree (actual_loading_location_id, actual_unloading_location_id);


--
-- Name: idx_trips_assignment; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trips_assignment ON public.trip_logs USING btree (assignment_id);


--
-- Name: idx_trips_assignment_status_date; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trips_assignment_status_date ON public.trip_logs USING btree (assignment_id, status, created_at);


--
-- Name: idx_trips_assignment_status_exception; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trips_assignment_status_exception ON public.trip_logs USING btree (assignment_id, status, is_exception, created_at);


--
-- Name: idx_trips_current_status_time; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trips_current_status_time ON public.trip_logs USING btree (status, created_at DESC) WHERE (status = ANY (ARRAY['loading_start'::public.trip_status, 'loading_end'::public.trip_status, 'unloading_start'::public.trip_status, 'unloading_end'::public.trip_status, 'stopped'::public.trip_status]));


--
-- Name: idx_trips_date; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trips_date ON public.trip_logs USING btree (created_at);


--
-- Name: idx_trips_duration_metrics; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trips_duration_metrics ON public.trip_logs USING btree (total_duration_minutes, loading_duration_minutes) WHERE (total_duration_minutes IS NOT NULL);


--
-- Name: idx_trips_exception; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trips_exception ON public.trip_logs USING btree (is_exception);


--
-- Name: idx_trips_exception_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trips_exception_status ON public.trip_logs USING btree (is_exception, status) WHERE (is_exception = true);


--
-- Name: idx_trips_location_performance; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trips_location_performance ON public.trip_logs USING btree (actual_loading_location_id, actual_unloading_location_id, status, created_at, total_duration_minutes);


--
-- Name: idx_trips_phase_durations; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trips_phase_durations ON public.trip_logs USING btree (status, loading_duration_minutes, travel_duration_minutes, unloading_duration_minutes, created_at) WHERE ((status = 'trip_completed'::public.trip_status) AND (total_duration_minutes IS NOT NULL));


--
-- Name: idx_trips_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trips_status ON public.trip_logs USING btree (status);


--
-- Name: idx_trips_stopped_analytics; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trips_stopped_analytics ON public.trip_logs USING btree (status, stopped_reported_at, stopped_resolved_at, previous_status) WHERE (status = 'stopped'::public.trip_status);


--
-- Name: idx_trips_time_analytics; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trips_time_analytics ON public.trip_logs USING btree (date(created_at), EXTRACT(hour FROM created_at), status);


--
-- Name: idx_trucks_active_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trucks_active_status ON public.dump_trucks USING btree (status, truck_number) WHERE (status = 'active'::public.truck_status);


--
-- Name: idx_trucks_license; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trucks_license ON public.dump_trucks USING btree (license_plate);


--
-- Name: idx_trucks_number; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trucks_number ON public.dump_trucks USING btree (truck_number);


--
-- Name: idx_trucks_qr_data_gin; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trucks_qr_data_gin ON public.dump_trucks USING gin (qr_code_data) WHERE (qr_code_data IS NOT NULL);


--
-- Name: idx_trucks_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trucks_status ON public.dump_trucks USING btree (status);


--
-- Name: idx_users_email; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_users_email ON public.users USING btree (email);


--
-- Name: idx_users_role; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_users_role ON public.users USING btree (role);


--
-- Name: idx_users_username; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_users_username ON public.users USING btree (username);


--
-- Name: idx_workflow_tracking; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_workflow_tracking ON public.trip_logs USING btree (workflow_type, is_extended_trip, cycle_number);


--
-- Name: assignments trg_auto_populate_driver; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER trg_auto_populate_driver BEFORE INSERT ON public.assignments FOR EACH ROW EXECUTE FUNCTION public.auto_populate_driver_from_shift();


--
-- Name: trip_logs trigger_auto_capture_trip_driver; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER trigger_auto_capture_trip_driver BEFORE INSERT OR UPDATE ON public.trip_logs FOR EACH ROW EXECUTE FUNCTION public.auto_capture_trip_driver();


--
-- Name: TRIGGER trigger_auto_capture_trip_driver ON trip_logs; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TRIGGER trigger_auto_capture_trip_driver ON public.trip_logs IS 'Automatically captures active driver information when trip starts';


--
-- Name: trip_logs trigger_calculate_trip_durations; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER trigger_calculate_trip_durations BEFORE INSERT OR UPDATE ON public.trip_logs FOR EACH ROW EXECUTE FUNCTION public.calculate_trip_durations();


--
-- Name: driver_shifts trigger_set_display_type; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER trigger_set_display_type BEFORE INSERT OR UPDATE ON public.driver_shifts FOR EACH ROW EXECUTE FUNCTION public.set_display_type_trigger();


--
-- Name: driver_shifts trigger_sync_shift_date; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER trigger_sync_shift_date BEFORE INSERT OR UPDATE ON public.driver_shifts FOR EACH ROW EXECUTE FUNCTION public.sync_shift_date_with_start_date();


--
-- Name: trip_logs trigger_update_assignment_on_trip_complete; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER trigger_update_assignment_on_trip_complete AFTER UPDATE ON public.trip_logs FOR EACH ROW EXECUTE FUNCTION public.update_assignment_on_trip_complete();


--
-- Name: approvals update_approvals_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_approvals_updated_at BEFORE UPDATE ON public.approvals FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: assignments update_assignments_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_assignments_updated_at BEFORE UPDATE ON public.assignments FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: drivers update_drivers_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_drivers_updated_at BEFORE UPDATE ON public.drivers FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: locations update_locations_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_locations_updated_at BEFORE UPDATE ON public.locations FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: trip_logs update_trip_logs_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_trip_logs_updated_at BEFORE UPDATE ON public.trip_logs FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: dump_trucks update_trucks_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_trucks_updated_at BEFORE UPDATE ON public.dump_trucks FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: users update_users_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: approvals approvals_reported_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.approvals
    ADD CONSTRAINT approvals_reported_by_fkey FOREIGN KEY (reported_by) REFERENCES public.users(id);


--
-- Name: approvals approvals_reviewed_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.approvals
    ADD CONSTRAINT approvals_reviewed_by_fkey FOREIGN KEY (reviewed_by) REFERENCES public.users(id);


--
-- Name: approvals approvals_suggested_assignment_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.approvals
    ADD CONSTRAINT approvals_suggested_assignment_id_fkey FOREIGN KEY (suggested_assignment_id) REFERENCES public.assignments(id);


--
-- Name: approvals approvals_trip_log_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.approvals
    ADD CONSTRAINT approvals_trip_log_id_fkey FOREIGN KEY (trip_log_id) REFERENCES public.trip_logs(id) ON DELETE CASCADE;


--
-- Name: assignments assignments_driver_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.assignments
    ADD CONSTRAINT assignments_driver_id_fkey FOREIGN KEY (driver_id) REFERENCES public.drivers(id) ON DELETE CASCADE;


--
-- Name: assignments assignments_loading_location_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.assignments
    ADD CONSTRAINT assignments_loading_location_id_fkey FOREIGN KEY (loading_location_id) REFERENCES public.locations(id) ON DELETE CASCADE;


--
-- Name: assignments assignments_shift_handover_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.assignments
    ADD CONSTRAINT assignments_shift_handover_id_fkey FOREIGN KEY (shift_handover_id) REFERENCES public.shift_handovers(id);


--
-- Name: assignments assignments_shift_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.assignments
    ADD CONSTRAINT assignments_shift_id_fkey FOREIGN KEY (shift_id) REFERENCES public.driver_shifts(id);


--
-- Name: assignments assignments_truck_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.assignments
    ADD CONSTRAINT assignments_truck_id_fkey FOREIGN KEY (truck_id) REFERENCES public.dump_trucks(id) ON DELETE CASCADE;


--
-- Name: assignments assignments_unloading_location_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.assignments
    ADD CONSTRAINT assignments_unloading_location_id_fkey FOREIGN KEY (unloading_location_id) REFERENCES public.locations(id) ON DELETE CASCADE;


--
-- Name: driver_shifts driver_shifts_assignment_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_shifts
    ADD CONSTRAINT driver_shifts_assignment_id_fkey FOREIGN KEY (assignment_id) REFERENCES public.assignments(id);


--
-- Name: driver_shifts driver_shifts_driver_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_shifts
    ADD CONSTRAINT driver_shifts_driver_id_fkey FOREIGN KEY (driver_id) REFERENCES public.drivers(id) ON DELETE CASCADE;


--
-- Name: driver_shifts driver_shifts_previous_shift_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_shifts
    ADD CONSTRAINT driver_shifts_previous_shift_id_fkey FOREIGN KEY (previous_shift_id) REFERENCES public.driver_shifts(id);


--
-- Name: driver_shifts driver_shifts_truck_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_shifts
    ADD CONSTRAINT driver_shifts_truck_id_fkey FOREIGN KEY (truck_id) REFERENCES public.dump_trucks(id) ON DELETE CASCADE;


--
-- Name: trip_logs fk_baseline_trip; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.trip_logs
    ADD CONSTRAINT fk_baseline_trip FOREIGN KEY (baseline_trip_id) REFERENCES public.trip_logs(id) ON DELETE SET NULL;


--
-- Name: scan_logs scan_logs_scanned_location_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.scan_logs
    ADD CONSTRAINT scan_logs_scanned_location_id_fkey FOREIGN KEY (scanned_location_id) REFERENCES public.locations(id) ON DELETE SET NULL;


--
-- Name: scan_logs scan_logs_scanned_truck_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.scan_logs
    ADD CONSTRAINT scan_logs_scanned_truck_id_fkey FOREIGN KEY (scanned_truck_id) REFERENCES public.dump_trucks(id);


--
-- Name: scan_logs scan_logs_scanner_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.scan_logs
    ADD CONSTRAINT scan_logs_scanner_user_id_fkey FOREIGN KEY (scanner_user_id) REFERENCES public.users(id);


--
-- Name: scan_logs scan_logs_trip_log_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.scan_logs
    ADD CONSTRAINT scan_logs_trip_log_id_fkey FOREIGN KEY (trip_log_id) REFERENCES public.trip_logs(id) ON DELETE SET NULL;


--
-- Name: shift_handovers shift_handovers_active_trip_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shift_handovers
    ADD CONSTRAINT shift_handovers_active_trip_id_fkey FOREIGN KEY (active_trip_id) REFERENCES public.trip_logs(id);


--
-- Name: shift_handovers shift_handovers_approved_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shift_handovers
    ADD CONSTRAINT shift_handovers_approved_by_fkey FOREIGN KEY (approved_by) REFERENCES public.users(id);


--
-- Name: shift_handovers shift_handovers_incoming_shift_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shift_handovers
    ADD CONSTRAINT shift_handovers_incoming_shift_id_fkey FOREIGN KEY (incoming_shift_id) REFERENCES public.driver_shifts(id);


--
-- Name: shift_handovers shift_handovers_location_at_handover_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shift_handovers
    ADD CONSTRAINT shift_handovers_location_at_handover_fkey FOREIGN KEY (location_at_handover) REFERENCES public.locations(id);


--
-- Name: shift_handovers shift_handovers_outgoing_shift_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shift_handovers
    ADD CONSTRAINT shift_handovers_outgoing_shift_id_fkey FOREIGN KEY (outgoing_shift_id) REFERENCES public.driver_shifts(id);


--
-- Name: shift_handovers shift_handovers_truck_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shift_handovers
    ADD CONSTRAINT shift_handovers_truck_id_fkey FOREIGN KEY (truck_id) REFERENCES public.dump_trucks(id) ON DELETE CASCADE;


--
-- Name: system_tasks system_tasks_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.system_tasks
    ADD CONSTRAINT system_tasks_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id);


--
-- Name: trip_logs trip_logs_actual_loading_location_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.trip_logs
    ADD CONSTRAINT trip_logs_actual_loading_location_id_fkey FOREIGN KEY (actual_loading_location_id) REFERENCES public.locations(id);


--
-- Name: trip_logs trip_logs_actual_unloading_location_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.trip_logs
    ADD CONSTRAINT trip_logs_actual_unloading_location_id_fkey FOREIGN KEY (actual_unloading_location_id) REFERENCES public.locations(id);


--
-- Name: trip_logs trip_logs_assignment_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.trip_logs
    ADD CONSTRAINT trip_logs_assignment_id_fkey FOREIGN KEY (assignment_id) REFERENCES public.assignments(id) ON DELETE CASCADE;


--
-- Name: trip_logs trip_logs_exception_approved_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.trip_logs
    ADD CONSTRAINT trip_logs_exception_approved_by_fkey FOREIGN KEY (exception_approved_by) REFERENCES public.users(id);


--
-- Name: trip_logs trip_logs_performed_by_driver_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.trip_logs
    ADD CONSTRAINT trip_logs_performed_by_driver_id_fkey FOREIGN KEY (performed_by_driver_id) REFERENCES public.drivers(id);


--
-- Name: trip_logs trip_logs_performed_by_shift_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.trip_logs
    ADD CONSTRAINT trip_logs_performed_by_shift_id_fkey FOREIGN KEY (performed_by_shift_id) REFERENCES public.driver_shifts(id);


--
-- Name: trip_logs trip_logs_stopped_resolved_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.trip_logs
    ADD CONSTRAINT trip_logs_stopped_resolved_by_fkey FOREIGN KEY (stopped_resolved_by) REFERENCES public.users(id);


--
-- PostgreSQL database dump complete
--

