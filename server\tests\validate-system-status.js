#!/usr/bin/env node

/**
 * System Status Validation Script
 * 
 * This script validates that all components of the shift assignment trip status fix
 * are properly implemented and working in the current system.
 */

const { query, getClient } = require('../config/database');

async function validateSystemStatus() {
  console.log('🔍 Validating Shift Assignment Trip Status Fix Implementation');
  console.log('=' .repeat(80));

  const client = await getClient();
  const validationResults = {
    passed: 0,
    failed: 0,
    warnings: 0
  };

  try {
    // Test 1: Verify database schema has required fields
    console.log('\n📋 Test 1: Database Schema Validation');
    console.log('-'.repeat(50));

    try {
      const schemaCheck = await client.query(`
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns
        WHERE table_name = 'trip_logs' 
        AND column_name IN ('notes', 'location_sequence', 'performed_by_driver_id', 'performed_by_shift_type')
        ORDER BY column_name
      `);

      const requiredFields = ['notes', 'location_sequence', 'performed_by_driver_id', 'performed_by_shift_type'];
      const foundFields = schemaCheck.rows.map(row => row.column_name);

      requiredFields.forEach(field => {
        if (foundFields.includes(field)) {
          console.log(`   ✅ ${field} field exists`);
        } else {
          console.log(`   ❌ ${field} field missing`);
          validationResults.failed++;
        }
      });

      if (foundFields.length === requiredFields.length) {
        console.log('✅ Database schema validation PASSED');
        validationResults.passed++;
      }

    } catch (error) {
      console.log(`❌ Database schema validation FAILED: ${error.message}`);
      validationResults.failed++;
    }

    // Test 2: Verify database functions exist
    console.log('\n📋 Test 2: Database Functions Validation');
    console.log('-'.repeat(50));

    try {
      const functionsCheck = await client.query(`
        SELECT proname, pronargs
        FROM pg_proc 
        WHERE proname IN ('update_all_shift_statuses', 'evaluate_shift_status', 'capture_active_driver_for_trip')
        ORDER BY proname
      `);

      const requiredFunctions = ['update_all_shift_statuses', 'evaluate_shift_status'];
      const foundFunctions = functionsCheck.rows.map(row => row.proname);

      requiredFunctions.forEach(func => {
        if (foundFunctions.includes(func)) {
          console.log(`   ✅ ${func} function exists`);
        } else {
          console.log(`   ❌ ${func} function missing`);
          validationResults.failed++;
        }
      });

      if (foundFunctions.includes('capture_active_driver_for_trip')) {
        console.log('   ✅ capture_active_driver_for_trip function exists (fallback)');
      } else {
        console.log('   ⚠️  capture_active_driver_for_trip function missing (fallback only)');
        validationResults.warnings++;
      }

      console.log('✅ Database functions validation PASSED');
      validationResults.passed++;

    } catch (error) {
      console.log(`❌ Database functions validation FAILED: ${error.message}`);
      validationResults.failed++;
    }

    // Test 3: Test captureActiveDriverInfo function logic
    console.log('\n📋 Test 3: captureActiveDriverInfo Function Logic');
    console.log('-'.repeat(50));

    try {
      // Create test data
      const testTruck = await client.query(`
        INSERT INTO dump_trucks (truck_number, license_plate, status, qr_code_data)
        VALUES ('VALIDATE001', 'VAL-001', 'active', '{"type": "truck", "id": "VALIDATE001"}')
        RETURNING id
      `);
      const truckId = testTruck.rows[0].id;

      const testDriver = await client.query(`
        INSERT INTO drivers (full_name, employee_id, status)
        VALUES ('Validation Driver', 'VAL001', 'active')
        RETURNING id
      `);
      const driverId = testDriver.rows[0].id;

      // Create QR-created active shift
      const testShift = await client.query(`
        INSERT INTO driver_shifts (
          truck_id, driver_id, shift_type, start_date, end_date,
          start_time, end_time, status, auto_created, created_at, updated_at
        ) VALUES ($1, $2, 'day', CURRENT_DATE, NULL, '08:00:00', NULL, 'active', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        RETURNING id
      `, [truckId, driverId]);
      const shiftId = testShift.rows[0].id;

      // Test the captureActiveDriverInfo logic via direct query
      const driverCapture = await client.query(`
        SELECT
          ds.driver_id,
          d.full_name as driver_name,
          d.employee_id,
          ds.id as shift_id,
          ds.shift_type
        FROM driver_shifts ds
        JOIN drivers d ON ds.driver_id = d.id
        WHERE ds.truck_id = $1
          AND ds.status = 'active'
          AND d.status = 'active'
          AND ds.auto_created = true
          AND ds.start_date IS NOT NULL
          AND CURRENT_DATE >= ds.start_date
          AND ds.end_time IS NULL
        ORDER BY ds.created_at DESC
        LIMIT 1
      `, [truckId]);

      if (driverCapture.rows.length > 0) {
        const driver = driverCapture.rows[0];
        console.log(`   ✅ Driver capture works: ${driver.driver_name} (${driver.shift_type})`);
        validationResults.passed++;
      } else {
        console.log('   ❌ Driver capture failed');
        validationResults.failed++;
      }

      // Clean up test data
      await client.query('DELETE FROM driver_shifts WHERE id = $1', [shiftId]);
      await client.query('DELETE FROM drivers WHERE id = $1', [driverId]);
      await client.query('DELETE FROM dump_trucks WHERE id = $1', [truckId]);

    } catch (error) {
      console.log(`❌ captureActiveDriverInfo validation FAILED: ${error.message}`);
      validationResults.failed++;
    }

    // Test 4: Verify automated function protection
    console.log('\n📋 Test 4: Automated Function Protection');
    console.log('-'.repeat(50));

    try {
      // Create test data
      const testTruck = await client.query(`
        INSERT INTO dump_trucks (truck_number, license_plate, status, qr_code_data)
        VALUES ('PROTECT001', 'PROT-001', 'active', '{"type": "truck", "id": "PROTECT001"}')
        RETURNING id
      `);
      const truckId = testTruck.rows[0].id;

      const testDriver = await client.query(`
        INSERT INTO drivers (full_name, employee_id, status)
        VALUES ('Protection Driver', 'PROT001', 'active')
        RETURNING id
      `);
      const driverId = testDriver.rows[0].id;

      // Create QR-created active shift
      const testShift = await client.query(`
        INSERT INTO driver_shifts (
          truck_id, driver_id, shift_type, start_date, end_date,
          start_time, end_time, status, auto_created, created_at, updated_at
        ) VALUES ($1, $2, 'day', CURRENT_DATE, NULL, '08:00:00', NULL, 'active', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        RETURNING id
      `, [truckId, driverId]);
      const shiftId = testShift.rows[0].id;

      // Run automated function
      await client.query('SELECT update_all_shift_statuses(CURRENT_TIMESTAMP)');

      // Check if shift status remained active
      const statusCheck = await client.query(
        'SELECT status, auto_created FROM driver_shifts WHERE id = $1',
        [shiftId]
      );

      if (statusCheck.rows[0].status === 'active' && statusCheck.rows[0].auto_created === true) {
        console.log('   ✅ QR-created shift protected from automated status changes');
        validationResults.passed++;
      } else {
        console.log('   ❌ QR-created shift was modified by automated function');
        validationResults.failed++;
      }

      // Clean up test data
      await client.query('DELETE FROM driver_shifts WHERE id = $1', [shiftId]);
      await client.query('DELETE FROM drivers WHERE id = $1', [driverId]);
      await client.query('DELETE FROM dump_trucks WHERE id = $1', [truckId]);

    } catch (error) {
      console.log(`❌ Automated function protection validation FAILED: ${error.message}`);
      validationResults.failed++;
    }

    // Test 5: Verify location_sequence functionality
    console.log('\n📋 Test 5: Location Sequence Functionality');
    console.log('-'.repeat(50));

    try {
      // Check if updateLocationSequence function exists in scanner.js
      const fs = require('fs');
      const path = require('path');
      const scannerPath = path.join(__dirname, '../routes/scanner.js');
      const scannerContent = fs.readFileSync(scannerPath, 'utf8');

      if (scannerContent.includes('updateLocationSequence')) {
        console.log('   ✅ updateLocationSequence function exists in scanner.js');
        validationResults.passed++;
      } else {
        console.log('   ❌ updateLocationSequence function missing from scanner.js');
        validationResults.failed++;
      }

      // Check if location_sequence field has GIN index
      const indexCheck = await client.query(`
        SELECT indexname, indexdef
        FROM pg_indexes
        WHERE tablename = 'trip_logs' AND indexname = 'idx_location_sequence'
      `);

      if (indexCheck.rows.length > 0) {
        console.log('   ✅ location_sequence GIN index exists');
      } else {
        console.log('   ⚠️  location_sequence GIN index missing (performance impact)');
        validationResults.warnings++;
      }

    } catch (error) {
      console.log(`❌ Location sequence validation FAILED: ${error.message}`);
      validationResults.failed++;
    }

    // Final Results
    console.log('\n' + '='.repeat(80));
    console.log('📊 VALIDATION SUMMARY');
    console.log('='.repeat(80));
    console.log(`Tests Passed: ${validationResults.passed}`);
    console.log(`Tests Failed: ${validationResults.failed}`);
    console.log(`Warnings: ${validationResults.warnings}`);

    if (validationResults.failed === 0) {
      console.log('\n🎉 SYSTEM VALIDATION PASSED!');
      console.log('✅ All components of the shift assignment trip status fix are properly implemented.');
      
      if (validationResults.warnings > 0) {
        console.log(`\n⚠️  ${validationResults.warnings} warning(s) found - system will work but may have minor issues.`);
      }
    } else {
      console.log('\n❌ SYSTEM VALIDATION FAILED!');
      console.log(`${validationResults.failed} critical issue(s) found that need to be addressed.`);
    }

  } catch (error) {
    console.error('💥 Validation script failed:', error.message);
  } finally {
    client.release();
  }
}

// Run validation if called directly
if (require.main === module) {
  validateSystemStatus().catch(console.error);
}

module.exports = { validateSystemStatus };