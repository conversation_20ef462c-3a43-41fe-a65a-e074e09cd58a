const request = require('supertest');
const express = require('express');
const driverRoutes = require('../../../routes/driver');

// Mock dependencies for production testing
jest.mock('../../../config/database', () => ({
  query: jest.fn(),
  getClient: jest.fn()
}));

jest.mock('../../../utils/DriverQRCodeGenerator');
jest.mock('../../../utils/SecurityMonitor');
jest.mock('../../../utils/logger', () => ({
  logError: jest.fn(),
  logInfo: jest.fn()
}));

jest.mock('../../../websocket', () => ({
  notifyDriverConnected: jest.fn(),
  notifyDriverDisconnected: jest.fn(),
  notifyDriverHandover: jest.fn()
}));

const { query, getClient } = require('../../../config/database');
const DriverQRCodeGenerator = require('../../../utils/DriverQRCodeGenerator');
const SecurityMonitor = require('../../../utils/SecurityMonitor');
const { logError, logInfo } = require('../../../utils/logger');
const { notifyDriverConnected, notifyDriverDisconnected, notifyDriverHandover } = require('../../../websocket');

// Create test app with production-like middleware
const app = express();
app.use(express.json({ limit: '10mb' }));
app.use('/api/driver', driverRoutes);

describe('Driver API Production Tests', () => {
  let mockClient;

  beforeAll(() => {
    // Set production environment
    process.env.NODE_ENV = 'production';
  });

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock database client
    mockClient = {
      query: jest.fn(),
      release: jest.fn()
    };
    getClient.mockResolvedValue(mockClient);

    // Mock security monitor
    SecurityMonitor.validateRequestOrigin.mockReturnValue({
      suspicious: false,
      risk_level: 'LOW'
    });
    SecurityMonitor.monitorFailedAuth.mockResolvedValue();
  });

  afterAll(() => {
    // Reset environment
    delete process.env.NODE_ENV;
  });

  describe('Production Success Scenarios', () => {
    const validDriverQR = {
      id: 'DR-001',
      driver_id: 123,
      employee_id: 'DR-001',
      generated_date: '2025-01-01T00:00:00.000Z',
      type: 'driver'
    };

    const validTruckQR = {
      id: 'DT-100',
      type: 'truck',
      truck_number: 'DT-100'
    };

    const mockDriver = {
      id: 123,
      employee_id: 'DR-001',
      full_name: 'John Doe',
      status: 'active'
    };

    const mockTruck = {
      id: 456,
      truck_number: 'DT-100',
      license_plate: 'ABC-123',
      status: 'active',
      qr_code_data: validTruckQR
    };

    test('PROD-001: Check-in success with comprehensive logging', async () => {
      // Mock driver validation
      DriverQRCodeGenerator.validateDriverQR.mockResolvedValue({
        success: true,
        valid: true,
        driver: mockDriver
      });

      // Mock database queries
      mockClient.query
        .mockResolvedValueOnce() // BEGIN
        .mockResolvedValueOnce({ rows: [mockTruck] }) // Truck lookup
        .mockResolvedValueOnce({ rows: [] }) // No active shift
        .mockResolvedValueOnce({ rows: [{ id: 789 }] }) // Insert new shift
        .mockResolvedValueOnce(); // COMMIT

      const response = await request(app)
        .post('/api/driver/connect')
        .set('User-Agent', 'ProductionApp/1.0')
        .set('X-Request-ID', 'prod-test-001')
        .send({
          driver_qr_data: JSON.stringify(validDriverQR),
          truck_qr_data: JSON.stringify(validTruckQR)
        });

      // Verify success response
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.action).toBe('check_in');
      expect(response.body.truck).toBe('DT-100');
      expect(response.body.shift_id).toBe(789);
      expect(response.body.driver.employee_id).toBe('DR-001');
      expect(response.body.check_in_time).toBeDefined();

      // Verify production logging
      expect(logInfo).toHaveBeenCalledWith(
        'DRIVER_CONNECT_SUCCESS',
        expect.stringContaining('Driver DR-001 check_in'),
        expect.objectContaining({
          driver_id: 123,
          employee_id: 'DR-001',
          truck_id: 456,
          truck_number: 'DT-100',
          action: 'check_in',
          is_handover: false,
          shift_id: 789,
          security_level: 'enhanced',
          processing_time_ms: expect.any(Number)
        })
      );

      // Verify WebSocket notification
      expect(notifyDriverConnected).toHaveBeenCalledWith(
        mockDriver,
        mockTruck,
        expect.objectContaining({
          action: 'check_in',
          shift_id: 789
        })
      );

      // Verify database transaction
      expect(mockClient.query).toHaveBeenCalledWith('BEGIN');
      expect(mockClient.query).toHaveBeenCalledWith('COMMIT');
      expect(mockClient.release).toHaveBeenCalled();
    });

    test('PROD-002: Check-out success with duration calculation', async () => {
      const mockActiveShift = {
        id: 789,
        truck_id: 456,
        start_date: '2025-01-01',
        start_time: '08:00:00',
        truck_number: 'DT-100'
      };

      // Mock driver validation
      DriverQRCodeGenerator.validateDriverQR.mockResolvedValue({
        success: true,
        valid: true,
        driver: mockDriver
      });

      // Mock database queries
      mockClient.query
        .mockResolvedValueOnce() // BEGIN
        .mockResolvedValueOnce({ rows: [mockTruck] }) // Truck lookup
        .mockResolvedValueOnce({ rows: [mockActiveShift] }) // Active shift found
        .mockResolvedValueOnce() // Update shift to completed
        .mockResolvedValueOnce(); // COMMIT

      const response = await request(app)
        .post('/api/driver/connect')
        .set('User-Agent', 'ProductionApp/1.0')
        .set('X-Request-ID', 'prod-test-002')
        .send({
          driver_qr_data: JSON.stringify(validDriverQR),
          truck_qr_data: JSON.stringify(validTruckQR)
        });

      // Verify success response
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.action).toBe('check_out');
      expect(response.body.truck).toBe('DT-100');
      expect(response.body.shift_id).toBe(789);
      expect(response.body.duration).toMatch(/^\d+h \d+m$/);
      expect(response.body.check_in_time).toBeDefined();
      expect(response.body.check_out_time).toBeDefined();

      // Verify production logging
      expect(logInfo).toHaveBeenCalledWith(
        'DRIVER_CONNECT_SUCCESS',
        expect.stringContaining('Driver DR-001 check_out'),
        expect.objectContaining({
          action: 'check_out',
          security_level: 'enhanced'
        })
      );

      // Verify WebSocket notification
      expect(notifyDriverDisconnected).toHaveBeenCalledWith(
        mockDriver,
        mockTruck,
        expect.objectContaining({
          action: 'check_out',
          duration: expect.stringMatching(/^\d+h \d+m$/)
        })
      );
    });

    test('PROD-003: Handover success with previous driver tracking', async () => {
      const mockActiveShift = {
        id: 789,
        truck_id: 999, // Different truck
        start_date: '2025-01-01',
        start_time: '08:00:00',
        truck_number: 'DT-200'
      };

      const mockPreviousDriver = {
        id: 999,
        employee_id: 'DR-999',
        full_name: 'Previous Driver'
      };

      // Mock driver validation
      DriverQRCodeGenerator.validateDriverQR.mockResolvedValue({
        success: true,
        valid: true,
        driver: mockDriver
      });

      // Mock database queries
      mockClient.query
        .mockResolvedValueOnce() // BEGIN
        .mockResolvedValueOnce({ rows: [mockTruck] }) // Truck lookup
        .mockResolvedValueOnce({ rows: [mockActiveShift] }) // Active shift on different truck
        .mockResolvedValueOnce({ rows: [{ handover_driver_shift: 890 }] }) // Handover function
        .mockResolvedValueOnce() // COMMIT
        .mockResolvedValueOnce({ rows: [mockPreviousDriver] }); // Previous driver lookup

      const response = await request(app)
        .post('/api/driver/connect')
        .set('User-Agent', 'ProductionApp/1.0')
        .set('X-Request-ID', 'prod-test-003')
        .send({
          driver_qr_data: JSON.stringify(validDriverQR),
          truck_qr_data: JSON.stringify(validTruckQR)
        });

      // Verify success response
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.action).toBe('handover');
      expect(response.body.previous_truck).toBe('DT-200');
      expect(response.body.new_truck).toBe('DT-100');
      expect(response.body.shift_id).toBe(890);
      expect(response.body.previous_shift_id).toBe(789);

      // Verify production logging
      expect(logInfo).toHaveBeenCalledWith(
        'DRIVER_CONNECT_SUCCESS',
        expect.stringContaining('Driver DR-001 check_in (handover)'),
        expect.objectContaining({
          action: 'check_in',
          is_handover: true,
          shift_id: 890,
          previous_shift_id: 789
        })
      );

      // Verify WebSocket handover notification
      expect(notifyDriverHandover).toHaveBeenCalledWith(
        mockPreviousDriver,
        mockDriver,
        mockTruck,
        expect.objectContaining({
          action: 'handover',
          shift_id: 890
        })
      );
    });

    test('PROD-004: Driver status retrieval with active shift', async () => {
      const mockActiveShift = {
        id: 789,
        truck_id: 456,
        start_date: '2025-01-01',
        start_time: '08:00:00',
        truck_number: 'DT-100',
        license_plate: 'ABC-123'
      };

      query
        .mockResolvedValueOnce({ rows: [mockDriver] }) // Driver lookup
        .mockResolvedValueOnce({ rows: [mockActiveShift] }); // Active shift

      const response = await request(app)
        .get('/api/driver/status/DR-001')
        .set('User-Agent', 'ProductionApp/1.0');

      // Verify success response
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.driver.employee_id).toBe('DR-001');
      expect(response.body.driver.full_name).toBe('John Doe');
      expect(response.body.status).toBe('checked_in');
      expect(response.body.status_message).toBe('Currently assigned to DT-100');
      expect(response.body.current_truck).toEqual({
        id: 456,
        truck_number: 'DT-100',
        license_plate: 'ABC-123'
      });
      expect(response.body.shift_info).toEqual({
        shift_id: 789,
        start_date: '2025-01-01',
        start_time: '08:00:00'
      });
    });

    test('PROD-005: Driver status retrieval without active shift', async () => {
      query
        .mockResolvedValueOnce({ rows: [mockDriver] }) // Driver lookup
        .mockResolvedValueOnce({ rows: [] }); // No active shift

      const response = await request(app)
        .get('/api/driver/status/DR-001')
        .set('User-Agent', 'ProductionApp/1.0');

      // Verify success response
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.driver.employee_id).toBe('DR-001');
      expect(response.body.status).toBe('checked_out');
      expect(response.body.status_message).toBe('Ready to check in');
      expect(response.body.current_truck).toBe(null);
      expect(response.body.shift_info).toBe(null);
    });

    test('PROD-006: Health check endpoint', async () => {
      const response = await request(app)
        .get('/api/driver/health')
        .set('User-Agent', 'ProductionApp/1.0');

      // Verify success response
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.service).toBe('Driver Connect API');
      expect(response.body.status).toBe('healthy');
      expect(response.body.timestamp).toBeDefined();
      expect(new Date(response.body.timestamp)).toBeInstanceOf(Date);
    });
  });

  describe('Production Error Handling', () => {
    test('PROD-007: Error logging without stack traces in production', async () => {
      const testError = new Error('Test database error');
      testError.stack = 'Error: Test database error\n    at test.js:123:45';

      // Mock driver validation to succeed
      DriverQRCodeGenerator.validateDriverQR.mockResolvedValue({
        success: true,
        valid: true,
        driver: { id: 123, employee_id: 'DR-001', full_name: 'John Doe' }
      });

      // Mock database to throw error
      mockClient.query
        .mockResolvedValueOnce() // BEGIN
        .mockRejectedValueOnce(testError) // Truck lookup fails
        .mockResolvedValueOnce(); // ROLLBACK

      const response = await request(app)
        .post('/api/driver/connect')
        .set('User-Agent', 'ProductionApp/1.0')
        .set('X-Request-ID', 'prod-test-007')
        .send({
          driver_qr_data: JSON.stringify({ id: 'DR-001', type: 'driver' }),
          truck_qr_data: JSON.stringify({ id: 'DT-100', type: 'truck' })
        });

      // Verify error response
      expect(response.status).toBe(500);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('INTERNAL_ERROR');
      expect(response.body.message).toBe('An error occurred while processing your request. Please try again.');

      // Verify production error logging (stack trace should be redacted)
      expect(logError).toHaveBeenCalledWith(
        'DRIVER_CONNECT_ERROR',
        testError,
        expect.objectContaining({
          error_type: 'Error',
          error_message: 'Test database error',
          stack_trace: 'REDACTED', // Stack trace should be redacted in production
          request_body_hash: expect.any(String)
        })
      );

      // Verify database rollback
      expect(mockClient.query).toHaveBeenCalledWith('ROLLBACK');
      expect(mockClient.release).toHaveBeenCalled();
    });
  });

  describe('Production Security Features', () => {
    test('PROD-008: Rate limiting is enforced in production', async () => {
      // Rate limiting should be active in production (not skipped)
      const response = await request(app)
        .post('/api/driver/connect')
        .set('User-Agent', 'ProductionApp/1.0')
        .send({});

      // Should get validation error, not rate limit skip
      expect(response.status).toBe(400);
      expect(response.body.error).toBe('VALIDATION_ERROR');
    });

    test('PROD-009: Security context is properly logged', async () => {
      // Mock driver validation failure
      DriverQRCodeGenerator.validateDriverQR.mockResolvedValue({
        success: false,
        valid: false,
        error: 'Invalid QR code'
      });

      mockClient.query
        .mockResolvedValueOnce() // BEGIN
        .mockResolvedValueOnce(); // ROLLBACK

      const response = await request(app)
        .post('/api/driver/connect')
        .set('User-Agent', 'ProductionApp/1.0')
        .set('X-Request-ID', 'prod-test-009')
        .set('X-Forwarded-For', '*************')
        .send({
          driver_qr_data: JSON.stringify({ id: 'DR-001', type: 'driver' }),
          truck_qr_data: JSON.stringify({ id: 'DT-100', type: 'truck' })
        });

      // Verify security logging
      expect(logError).toHaveBeenCalledWith(
        'DRIVER_CONNECT_VALIDATION_FAILED',
        'Invalid QR code',
        expect.objectContaining({
          ip_address: expect.any(String),
          user_agent: 'ProductionApp/1.0',
          request_id: 'prod-test-009',
          forwarded_for: '*************',
          timestamp: expect.any(String),
          validation_error: 'Invalid QR code'
        })
      );

      // Verify failed auth monitoring
      expect(SecurityMonitor.monitorFailedAuth).toHaveBeenCalled();
    });
  });

  describe('Production WebSocket Integration', () => {
    test('PROD-010: WebSocket errors do not fail the request', async () => {
      // Mock driver validation
      DriverQRCodeGenerator.validateDriverQR.mockResolvedValue({
        success: true,
        valid: true,
        driver: { id: 123, employee_id: 'DR-001', full_name: 'John Doe' }
      });

      // Mock database queries
      mockClient.query
        .mockResolvedValueOnce() // BEGIN
        .mockResolvedValueOnce({ rows: [{ id: 456, truck_number: 'DT-100', status: 'active', qr_code_data: { id: 'DT-100', type: 'truck' } }] })
        .mockResolvedValueOnce({ rows: [] }) // No active shift
        .mockResolvedValueOnce({ rows: [{ id: 789 }] }) // Insert new shift
        .mockResolvedValueOnce(); // COMMIT

      // Mock WebSocket notification to throw error
      notifyDriverConnected.mockImplementation(() => {
        throw new Error('WebSocket connection failed');
      });

      const response = await request(app)
        .post('/api/driver/connect')
        .set('User-Agent', 'ProductionApp/1.0')
        .send({
          driver_qr_data: JSON.stringify({ id: 'DR-001', type: 'driver' }),
          truck_qr_data: JSON.stringify({ id: 'DT-100', type: 'truck' })
        });

      // Request should still succeed despite WebSocket error
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.action).toBe('check_in');

      // WebSocket error should be logged
      expect(logError).toHaveBeenCalledWith(
        'WEBSOCKET_NOTIFICATION_ERROR',
        expect.any(Error),
        expect.objectContaining({
          driver_id: 123,
          action: 'check_in'
        })
      );
    });
  });
});