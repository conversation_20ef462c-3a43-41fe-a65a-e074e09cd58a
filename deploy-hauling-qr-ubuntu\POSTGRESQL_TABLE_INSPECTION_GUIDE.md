# PostgreSQL Table Inspection Guide for Ubuntu

A comprehensive guide for checking existing tables in the Hauling QR Trip Management System PostgreSQL database on Ubuntu.

## Quick Reference

### Database Connection Details
```bash
Host: localhost
Port: 5432
Database: hauling_qr_system
User: hauling_app
Password: PostgreSQLPassword123  # (from deployment-config.conf)
```

---

## Method 1: Interactive psql Command Line (Recommended)

### Connect to Database
```bash
# Using password authentication
PGPASSWORD="PostgreSQLPassword123" psql -h localhost -p 5432 -U hauling_app -d hauling_qr_system

# Alternative: Set environment variable first
export PGPASSWORD="PostgreSQLPassword123"
psql -h localhost -p 5432 -U hauling_app -d hauling_qr_system
```

### Essential psql Commands
```sql
-- List all tables in current database
\dt

-- List tables with detailed information (sizes, descriptions)
\dt+

-- List all tables across all schemas
\dt *.*

-- List tables matching a pattern
\dt trip*
\dt *log*

-- Show detailed structure of a specific table
\d table_name

-- Show comprehensive table information (indexes, constraints, triggers)
\d+ table_name

-- List all databases
\l

-- List all users/roles
\du

-- Show current database and user
\conninfo

-- Get help for psql commands
\?

-- Exit psql
\q
```

---

## Method 2: SQL Queries for Table Information

### Basic Table Listing
```sql
-- List all tables in public schema
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_name;

-- List tables with column counts
SELECT 
    table_name,
    (SELECT COUNT(*) 
     FROM information_schema.columns 
     WHERE table_name = t.table_name 
       AND table_schema = 'public') as column_count
FROM information_schema.tables t
WHERE table_schema = 'public'
ORDER BY table_name;
```

### Table Statistics and Sizes
```sql
-- Get table sizes
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as "Total Size",
    pg_size_pretty(pg_relation_size(schemaname||'.'||tablename)) as "Table Size",
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename) - pg_relation_size(schemaname||'.'||tablename)) as "Index Size"
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- Get row counts for all tables
SELECT 
    schemaname,
    tablename,
    n_tup_ins as "Total Inserts",
    n_tup_upd as "Total Updates", 
    n_tup_del as "Total Deletes",
    n_live_tup as "Live Rows",
    n_dead_tup as "Dead Rows"
FROM pg_stat_user_tables 
ORDER BY n_live_tup DESC;
```

### Advanced Table Analysis
```sql
-- Get table information with constraints
SELECT 
    t.table_name,
    t.table_type,
    c.column_name,
    c.data_type,
    c.is_nullable,
    c.column_default
FROM information_schema.tables t
JOIN information_schema.columns c ON t.table_name = c.table_name
WHERE t.table_schema = 'public'
ORDER BY t.table_name, c.ordinal_position;

-- Find foreign key relationships
SELECT
    tc.table_name, 
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name 
FROM information_schema.table_constraints AS tc 
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
    AND tc.table_schema = 'public'
ORDER BY tc.table_name;
```

---

## Method 3: One-liner Commands (No Interactive Session)

### Quick Table Checks
```bash
# List all tables
PGPASSWORD="PostgreSQLPassword123" psql -h localhost -p 5432 -U hauling_app -d hauling_qr_system -c "\dt"

# List tables with sizes
PGPASSWORD="PostgreSQLPassword123" psql -h localhost -p 5432 -U hauling_app -d hauling_qr_system -c "\dt+"

# Get table names only (clean output)
PGPASSWORD="PostgreSQLPassword123" psql -h localhost -p 5432 -U hauling_app -d hauling_qr_system -t -c "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' ORDER BY table_name;"

# Count total tables
PGPASSWORD="PostgreSQLPassword123" psql -h localhost -p 5432 -U hauling_app -d hauling_qr_system -t -c "SELECT COUNT(*) as total_tables FROM information_schema.tables WHERE table_schema = 'public';"

# Check specific table exists
PGPASSWORD="PostgreSQLPassword123" psql -h localhost -p 5432 -U hauling_app -d hauling_qr_system -t -c "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'users');"
```

### Database Health Checks
```bash
# Check database connection
PGPASSWORD="PostgreSQLPassword123" psql -h localhost -p 5432 -U hauling_app -d hauling_qr_system -c "SELECT version();"

# Get database size
PGPASSWORD="PostgreSQLPassword123" psql -h localhost -p 5432 -U hauling_app -d hauling_qr_system -c "SELECT pg_size_pretty(pg_database_size('hauling_qr_system'));"

# Check active connections
PGPASSWORD="PostgreSQLPassword123" psql -h localhost -p 5432 -U hauling_app -d hauling_qr_system -c "SELECT count(*) as active_connections FROM pg_stat_activity WHERE datname = 'hauling_qr_system';"
```

---

## Method 4: Using Deployment Script Management Tools

### Built-in Database Management
```bash
# Navigate to application directory
cd /var/www/hauling-qr-system

# Use database management script
./manage-database.sh verify

# Connect to database using management script
./manage-database.sh connect

# Test database connection
./manage-database.sh test

# Create database backup
./manage-database.sh backup
```

### Management Script Commands
```bash
# Check database health
./manage-database.sh verify

# Expected output shows:
# ✅ users - exists
# ✅ drivers - exists  
# ✅ dump_trucks - exists
# ✅ locations - exists
# ✅ assignments - exists
# ✅ trip_logs - exists
# ✅ driver_shifts - exists
```

---

## Method 5: Environment Variables Setup

### Configure Environment for Easier Access
```bash
# Add to ~/.bashrc or ~/.profile
export PGHOST=localhost
export PGPORT=5432
export PGDATABASE=hauling_qr_system
export PGUSER=hauling_app
export PGPASSWORD=PostgreSQLPassword123

# Reload environment
source ~/.bashrc

# Now you can use psql without parameters
psql -c "\dt"
psql -c "\dt+"
psql -c "SELECT COUNT(*) FROM users;"
```

---

## Method 6: Comprehensive Database Overview

### Complete System Analysis
```bash
# Get comprehensive database information
PGPASSWORD="PostgreSQLPassword123" psql -h localhost -p 5432 -U hauling_app -d hauling_qr_system -c "
SELECT 
    'Tables' as object_type,
    COUNT(*) as count
FROM information_schema.tables 
WHERE table_schema = 'public'
UNION ALL
SELECT 
    'Views' as object_type,
    COUNT(*) as count
FROM information_schema.views 
WHERE table_schema = 'public'
UNION ALL
SELECT 
    'Functions' as object_type,
    COUNT(*) as count
FROM information_schema.routines 
WHERE routine_schema = 'public'
UNION ALL
SELECT 
    'Indexes' as object_type,
    COUNT(*) as count
FROM pg_indexes 
WHERE schemaname = 'public';"
```

### Performance Analysis
```bash
# Check table performance statistics
PGPASSWORD="PostgreSQLPassword123" psql -h localhost -p 5432 -U hauling_app -d hauling_qr_system -c "
SELECT 
    schemaname,
    tablename,
    seq_scan as \"Sequential Scans\",
    seq_tup_read as \"Sequential Reads\",
    idx_scan as \"Index Scans\",
    idx_tup_fetch as \"Index Reads\",
    n_tup_ins as \"Inserts\",
    n_tup_upd as \"Updates\",
    n_tup_del as \"Deletes\"
FROM pg_stat_user_tables 
ORDER BY seq_scan + idx_scan DESC;"
```

---

## Expected Tables in Hauling QR System

Based on the database schema, you should see these main tables:

### Core Tables
- **`users`** - User accounts and authentication
- **`drivers`** - Driver information and status
- **`dump_trucks`** - Truck fleet management
- **`locations`** - Loading/unloading locations
- **`assignments`** - Trip assignments and scheduling
- **`trip_logs`** - Trip tracking and status logs
- **`driver_shifts`** - Driver shift management

### Supporting Tables
- **`scan_logs`** - QR code scan records
- **`approvals`** - Approval workflow management
- **`shift_handovers`** - Shift transition records
- **`system_logs`** - System event logging
- **`system_tasks`** - Automated task management
- **`health_check_logs`** - System health monitoring

### Utility Tables
- **`migrations`** - Database migration tracking
- **`migration_log`** - Migration execution history
- **`automated_fix_logs`** - System maintenance logs

### Quick Verification Command
```bash
# Check for all critical tables
PGPASSWORD="PostgreSQLPassword123" psql -h localhost -p 5432 -U hauling_app -d hauling_qr_system -c "
SELECT 
    table_name,
    CASE 
        WHEN table_name IN ('users', 'drivers', 'dump_trucks', 'locations', 'assignments', 'trip_logs', 'driver_shifts') 
        THEN '✅ Critical'
        ELSE '📋 Supporting'
    END as importance,
    (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = t.table_name) as columns
FROM information_schema.tables t 
WHERE table_schema = 'public' 
ORDER BY importance, table_name;"
```

---

## Troubleshooting Connection Issues

### Common Problems and Solutions

#### 1. Connection Refused
```bash
# Check if PostgreSQL is running
sudo systemctl status postgresql

# Start PostgreSQL if stopped
sudo systemctl start postgresql

# Enable auto-start
sudo systemctl enable postgresql
```

#### 2. Authentication Failed
```bash
# Check if user exists
sudo -u postgres psql -c "\du" | grep hauling

# Check if database exists  
sudo -u postgres psql -c "\l" | grep hauling

# Reset user password if needed
sudo -u postgres psql -c "ALTER USER hauling_app WITH PASSWORD 'PostgreSQLPassword123';"
```

#### 3. Database Not Found
```bash
# List all databases
sudo -u postgres psql -c "\l"

# Create database if missing
sudo -u postgres psql -c "CREATE DATABASE hauling_qr_system OWNER hauling_app;"
```

#### 4. Permission Denied
```bash
# Grant database privileges
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE hauling_qr_system TO hauling_app;"

# Connect as postgres user to check
sudo -u postgres psql -d hauling_qr_system -c "\dt"
```

---

## Advanced Inspection Techniques

### 1. Table Relationships Visualization
```sql
-- Get table relationship map
WITH RECURSIVE table_deps AS (
    SELECT 
        tc.table_name as parent_table,
        ccu.table_name as child_table,
        1 as level
    FROM information_schema.table_constraints tc
    JOIN information_schema.constraint_column_usage ccu 
        ON tc.constraint_name = ccu.constraint_name
    WHERE tc.constraint_type = 'FOREIGN KEY'
        AND tc.table_schema = 'public'
    
    UNION ALL
    
    SELECT 
        td.parent_table,
        tc2.table_name,
        td.level + 1
    FROM table_deps td
    JOIN information_schema.table_constraints tc2 
        ON td.child_table = tc2.table_name
    JOIN information_schema.constraint_column_usage ccu2 
        ON tc2.constraint_name = ccu2.constraint_name
    WHERE tc2.constraint_type = 'FOREIGN KEY'
        AND td.level < 5
)
SELECT DISTINCT parent_table, child_table, level
FROM table_deps
ORDER BY level, parent_table;
```

### 2. Data Quality Checks
```sql
-- Check for empty tables
SELECT 
    schemaname,
    tablename,
    n_live_tup as row_count,
    CASE 
        WHEN n_live_tup = 0 THEN '⚠️ Empty'
        WHEN n_live_tup < 10 THEN '📊 Low Data'
        ELSE '✅ Has Data'
    END as status
FROM pg_stat_user_tables 
ORDER BY n_live_tup DESC;
```

### 3. Index Analysis
```sql
-- Check index usage
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan as scans,
    idx_tup_read as reads,
    idx_tup_fetch as fetches
FROM pg_stat_user_indexes 
ORDER BY idx_scan DESC;
```

---

## Quick Commands Cheat Sheet

```bash
# Essential one-liners
alias pgconnect='PGPASSWORD="PostgreSQLPassword123" psql -h localhost -p 5432 -U hauling_app -d hauling_qr_system'
alias pgtables='pgconnect -c "\dt"'
alias pgsize='pgconnect -c "\dt+"'
alias pgcount='pgconnect -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = '\''public'\'';"'

# Usage examples
pgtables          # List all tables
pgsize            # List tables with sizes
pgcount           # Count total tables
pgconnect -c "\d users"  # Describe users table
```

This guide provides comprehensive methods for inspecting PostgreSQL tables in your Hauling QR Trip Management System. Choose the method that best fits your needs and technical comfort level.

---

## Database Removal and Cleanup

### Method 1: Complete Database Removal (Recommended)

#### Step 1: Stop Application Services
```bash
# Stop the application first
cd /var/www/hauling-qr-system
./manage-server.sh stop

# Or stop PM2 processes
pm2 stop all
pm2 delete all
```

#### Step 2: Drop Database and User
```bash
# Connect as postgres superuser
sudo -u postgres psql

# Inside PostgreSQL prompt:
-- Terminate all connections to the database
SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname = 'hauling_qr_system';

-- Drop the database
DROP DATABASE IF EXISTS hauling_qr_system;

-- Drop the user
DROP USER IF EXISTS hauling_app;

-- Exit PostgreSQL
\q
```

#### Step 3: One-liner Database Removal
```bash
# Complete database and user removal (one command)
sudo -u postgres psql -c "SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname = 'hauling_qr_system'; DROP DATABASE IF EXISTS hauling_qr_system; DROP USER IF EXISTS hauling_app;"
```

### Method 2: Drop Database Only (Keep User)

```bash
# Drop database but keep the hauling_app user
sudo -u postgres psql -c "SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname = 'hauling_qr_system'; DROP DATABASE IF EXISTS hauling_qr_system;"
```

### Method 3: Clear All Tables (Keep Database Structure)

```bash
# Connect to database
PGPASSWORD="PostgreSQLPassword123" psql -h localhost -p 5432 -U hauling_app -d hauling_qr_system

# Inside database, drop all tables
DROP SCHEMA public CASCADE;
CREATE SCHEMA public;
GRANT ALL ON SCHEMA public TO hauling_app;
GRANT ALL ON SCHEMA public TO public;

# Exit
\q
```

### Method 4: Selective Table Removal

```bash
# Connect to database
PGPASSWORD="PostgreSQLPassword123" psql -h localhost -p 5432 -U hauling_app -d hauling_qr_system

# Drop specific tables (order matters due to foreign keys)
DROP TABLE IF EXISTS scan_logs CASCADE;
DROP TABLE IF EXISTS trip_logs CASCADE;
DROP TABLE IF EXISTS assignments CASCADE;
DROP TABLE IF EXISTS driver_shifts CASCADE;
DROP TABLE IF EXISTS shift_handovers CASCADE;
DROP TABLE IF EXISTS approvals CASCADE;
DROP TABLE IF EXISTS system_logs CASCADE;
DROP TABLE IF EXISTS system_tasks CASCADE;
DROP TABLE IF EXISTS health_check_logs CASCADE;
DROP TABLE IF EXISTS automated_fix_logs CASCADE;
DROP TABLE IF EXISTS migration_log CASCADE;
DROP TABLE IF EXISTS migrations CASCADE;
DROP TABLE IF EXISTS drivers CASCADE;
DROP TABLE IF EXISTS dump_trucks CASCADE;
DROP TABLE IF EXISTS locations CASCADE;
DROP TABLE IF EXISTS users CASCADE;

# Exit
\q
```

### Method 5: Using Management Script for Cleanup

```bash
# Create a cleanup command in your manage-database.sh
cd /var/www/hauling-qr-system

# Add this to your manage-database.sh or run directly:
./manage-database.sh query "
DO \$\$ 
DECLARE 
    r RECORD;
BEGIN
    -- Drop all tables
    FOR r IN (SELECT tablename FROM pg_tables WHERE schemaname = 'public') 
    LOOP
        EXECUTE 'DROP TABLE IF EXISTS ' || quote_ident(r.tablename) || ' CASCADE';
    END LOOP;
    
    -- Drop all sequences
    FOR r IN (SELECT sequence_name FROM information_schema.sequences WHERE sequence_schema = 'public')
    LOOP
        EXECUTE 'DROP SEQUENCE IF EXISTS ' || quote_ident(r.sequence_name) || ' CASCADE';
    END LOOP;
    
    -- Drop all views
    FOR r IN (SELECT viewname FROM pg_views WHERE schemaname = 'public')
    LOOP
        EXECUTE 'DROP VIEW IF EXISTS ' || quote_ident(r.viewname) || ' CASCADE';
    END LOOP;
    
    -- Drop all functions
    FOR r IN (SELECT routine_name FROM information_schema.routines WHERE routine_schema = 'public')
    LOOP
        EXECUTE 'DROP FUNCTION IF EXISTS ' || quote_ident(r.routine_name) || ' CASCADE';
    END LOOP;
END \$\$;
"
```

---

## Database Recreation After Removal

### Method 1: Using Deployment Script (Recommended)
```bash
# After removing database, re-run deployment
cd /path/to/deployment/script
sudo ./deploy-hauling-qr-ubuntu-fixed.sh --config deployment-config.conf
```

### Method 2: Manual Recreation
```bash
# Create database and user manually
sudo -u postgres psql -c "CREATE USER hauling_app WITH PASSWORD 'PostgreSQLPassword123';"
sudo -u postgres psql -c "CREATE DATABASE hauling_qr_system OWNER hauling_app;"
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE hauling_qr_system TO hauling_app;"

# Initialize with schema
cd /var/www/hauling-qr-system
./manage-database.sh init
```

### Method 3: Quick Recreation Script
```bash
# Create a quick recreation script
cat > recreate-database.sh << 'EOF'
#!/bin/bash

echo "🗑️ Removing existing database..."
sudo -u postgres psql -c "SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname = 'hauling_qr_system'; DROP DATABASE IF EXISTS hauling_qr_system; DROP USER IF EXISTS hauling_app;"

echo "🔧 Creating new database and user..."
sudo -u postgres psql -c "CREATE USER hauling_app WITH PASSWORD 'PostgreSQLPassword123';"
sudo -u postgres psql -c "CREATE DATABASE hauling_qr_system OWNER hauling_app;"
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE hauling_qr_system TO hauling_app;"

echo "📊 Initializing database schema..."
cd /var/www/hauling-qr-system
if [[ -f "database/init.sql" ]]; then
    PGPASSWORD="PostgreSQLPassword123" psql -h localhost -p 5432 -U hauling_app -d hauling_qr_system -f database/init.sql
    echo "✅ Database recreated successfully"
else
    echo "❌ init.sql not found"
fi

echo "🔍 Verifying database..."
./manage-database.sh verify
EOF

chmod +x recreate-database.sh
./recreate-database.sh
```

---

## Troubleshooting Database Removal

### Issue 1: "Database is being accessed by other users"
```bash
# Force terminate all connections
sudo -u postgres psql -c "
SELECT pg_terminate_backend(pid) 
FROM pg_stat_activity 
WHERE datname = 'hauling_qr_system' AND pid <> pg_backend_pid();
"

# Then try dropping again
sudo -u postgres psql -c "DROP DATABASE hauling_qr_system;"
```

### Issue 2: "Permission denied" errors
```bash
# Make sure you're using postgres superuser
sudo -u postgres psql

# Or check if you have the right permissions
sudo -u postgres psql -c "\du" | grep hauling_app
```

### Issue 3: Foreign key constraint errors
```bash
# Drop with CASCADE to remove dependent objects
sudo -u postgres psql -c "DROP DATABASE hauling_qr_system CASCADE;"
```

### Issue 4: Application still connected
```bash
# Stop all application services first
pm2 stop all
pm2 delete all
sudo systemctl stop nginx
sudo pkill -f "node.*server"

# Check for remaining connections
sudo -u postgres psql -c "SELECT * FROM pg_stat_activity WHERE datname = 'hauling_qr_system';"
```

---

## Verification After Removal

### Check Database Removal
```bash
# List all databases (hauling_qr_system should not appear)
sudo -u postgres psql -c "\l" | grep hauling

# Check if user still exists
sudo -u postgres psql -c "\du" | grep hauling_app

# Verify no connections
sudo -u postgres psql -c "SELECT count(*) FROM pg_stat_activity WHERE datname = 'hauling_qr_system';"
```

### Check Application Status
```bash
# Verify application is stopped
pm2 list
ps aux | grep -i hauling
netstat -tlnp | grep :5000

# Check if database files are removed
sudo ls -la /var/lib/postgresql/*/main/base/ | grep hauling
```

---

## Safety Precautions

### ⚠️ Before Removing Database:

1. **Create Backup** (if you want to keep data):
```bash
cd /var/www/hauling-qr-system
./manage-database.sh backup
```

2. **Stop Application Services**:
```bash
./manage-server.sh stop
pm2 stop all
```

3. **Verify What You're Removing**:
```bash
# Check database size and content
./manage-database.sh health
./manage-database.sh count
```

### ✅ Safe Removal Checklist:
- [ ] Application services stopped
- [ ] Backup created (if needed)
- [ ] Confirmed correct database name
- [ ] No other applications using the database
- [ ] Ready to recreate from scratch

---

## Quick Reference Commands

```bash
# Complete removal and recreation
sudo -u postgres psql -c "SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname = 'hauling_qr_system'; DROP DATABASE IF EXISTS hauling_qr_system; DROP USER IF EXISTS hauling_app;"
sudo -u postgres psql -c "CREATE USER hauling_app WITH PASSWORD 'PostgreSQLPassword123'; CREATE DATABASE hauling_qr_system OWNER hauling_app; GRANT ALL PRIVILEGES ON DATABASE hauling_qr_system TO hauling_app;"

# Verify removal
sudo -u postgres psql -c "\l" | grep hauling

# Verify recreation
PGPASSWORD="PostgreSQLPassword123" psql -h localhost -p 5432 -U hauling_app -d hauling_qr_system -c "SELECT version();"
```

This guide provides comprehensive methods for inspecting PostgreSQL tables in your Hauling QR Trip Management System. Choose the method that best fits your needs and technical comfort level.