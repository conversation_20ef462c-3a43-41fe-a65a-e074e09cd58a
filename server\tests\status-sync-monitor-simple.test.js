/**
 * Simple Status Synchronization Monitor Test
 * 
 * Basic functionality test for the monitoring service
 */

const statusSyncMonitor = require('../services/StatusSynchronizationMonitor');

// Mock the dependencies
jest.mock('../services/StatusSynchronizationService', () => ({
  monitorStatusSynchronization: jest.fn().mockResolvedValue({
    overall_status: 'operational',
    sync_issues: [],
    auto_fixes_applied: 0,
    metrics: {
      shifts_checked: 5,
      assignments_checked: 3,
      trips_checked: 10
    },
    monitoring_duration_ms: 150
  }),
  createSyncAlerts: jest.fn().mockResolvedValue([])
}));

describe('StatusSynchronizationMonitor - Basic Test', () => {
  afterEach(() => {
    statusSyncMonitor.stop();
  });

  test('should start and stop monitoring service', () => {
    expect(statusSyncMonitor.getStatus().is_running).toBe(false);
    
    statusSyncMonitor.start(30000);
    expect(statusSyncMonitor.getStatus().is_running).toBe(true);
    expect(statusSyncMonitor.getStatus().check_interval_ms).toBe(30000);
    
    statusSyncMonitor.stop();
    expect(statusSyncMonitor.getStatus().is_running).toBe(false);
  });

  test('should get status information', () => {
    const status = statusSyncMonitor.getStatus();
    
    expect(status).toHaveProperty('is_running');
    expect(status).toHaveProperty('check_interval_ms');
    expect(status).toHaveProperty('metrics');
    expect(status).toHaveProperty('active_alerts');
    expect(status).toHaveProperty('alerts');
    
    expect(typeof status.is_running).toBe('boolean');
    expect(typeof status.check_interval_ms).toBe('number');
    expect(typeof status.metrics).toBe('object');
    expect(typeof status.active_alerts).toBe('number');
    expect(Array.isArray(status.alerts)).toBe(true);
  });

  test('should get alerts information', () => {
    const alerts = statusSyncMonitor.getAlerts();
    
    expect(alerts).toHaveProperty('active_alerts');
    expect(alerts).toHaveProperty('alert_history');
    expect(alerts).toHaveProperty('summary');
    
    expect(Array.isArray(alerts.active_alerts)).toBe(true);
    expect(Array.isArray(alerts.alert_history)).toBe(true);
    expect(typeof alerts.summary).toBe('object');
    expect(typeof alerts.summary.total_active).toBe('number');
    expect(typeof alerts.summary.total_resolved).toBe('number');
  });
});