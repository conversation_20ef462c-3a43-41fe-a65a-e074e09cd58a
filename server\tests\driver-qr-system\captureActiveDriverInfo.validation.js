#!/usr/bin/env node

/**
 * Validation script for captureActiveDriverInfo function
 * Creates real test data and validates all scenarios from Task 8
 */

const { getClient, query } = require('../../config/database');

// Test scenarios configuration
const TEST_SCENARIOS = [
  {
    id: 'qr_active_checkin',
    name: 'QR-created shift with NULL end_time (still checked in)',
    setup: {
      shift_type: 'QR',
      start_date: '2025-07-28',
      end_date: null, // NULL for active shifts
      start_time: '08:00:00',
      end_time: null, // NULL for active shifts
      auto_created: true,
      status: 'active'
    },
    test_timestamp: '2025-07-28T10:00:00Z',
    expected_result: 'FOUND',
    requirements: ['2.1', '2.3', '5.1']
  },
  {
    id: 'qr_completed_checkout',
    name: 'QR-created shift with populated end_time (completed check-out)',
    setup: {
      shift_type: 'QR',
      start_date: '2025-07-28',
      end_date: '2025-07-29',
      start_time: '08:00:00',
      end_time: '08:00:00', // Actual check-out time
      auto_created: true,
      status: 'completed'
    },
    test_timestamp: '2025-07-28T22:00:00Z',
    expected_result: 'NOT_FOUND', // Should not find completed shifts
    requirements: ['2.2', '2.4', '5.1']
  },
  {
    id: 'overnight_same_day',
    name: 'Overnight shift: check-in July 28 08:00 AM, trip scan July 28 22:00 PM',
    setup: {
      shift_type: 'QR',
      start_date: '2025-07-28',
      end_date: null, // NULL for active shifts
      start_time: '08:00:00',
      end_time: null, // NULL for active shifts
      auto_created: true,
      status: 'active'
    },
    test_timestamp: '2025-07-28T22:00:00Z',
    expected_result: 'FOUND',
    requirements: ['2.1', '2.2', '5.1']
  },
  {
    id: 'overnight_next_day',
    name: 'Overnight shift: check-in July 28 22:00 PM, trip scan July 29 02:00 AM',
    setup: {
      shift_type: 'QR',
      start_date: '2025-07-28',
      end_date: null, // NULL for active shifts (will span multiple days)
      start_time: '22:00:00',
      end_time: null, // NULL for active shifts
      auto_created: true,
      status: 'active'
    },
    test_timestamp: '2025-07-29T02:00:00Z',
    expected_result: 'FOUND',
    requirements: ['2.1', '2.2', '2.5']
  },
  {
    id: 'completed_overnight_no_find',
    name: 'Completed overnight: check-in July 28 08:00 AM, check-out July 29 08:00 AM, trip scan July 29 10:00 AM',
    setup: {
      shift_type: 'QR',
      start_date: '2025-07-28',
      end_date: '2025-07-29',
      start_time: '08:00:00',
      end_time: '08:00:00', // Actual check-out time
      auto_created: true,
      status: 'completed' // Completed shift
    },
    test_timestamp: '2025-07-29T10:00:00Z',
    expected_result: 'NOT_FOUND',
    requirements: ['2.2', '2.4', '2.5']
  },
  {
    id: 'fallback_test',
    name: 'Test fallback mechanisms when primary query fails',
    setup: {
      shift_type: 'QR',
      start_date: '2025-07-28',
      end_date: null, // NULL for active shifts
      start_time: '08:00:00',
      end_time: null, // NULL for active shifts
      auto_created: true,
      status: 'active'
    },
    test_timestamp: '2025-07-28T10:00:00Z',
    expected_result: 'FOUND',
    requirements: ['5.2', '5.3', '5.4']
  }
];

class CaptureActiveDriverValidator {
  constructor() {
    this.client = null;
    this.testDriverId = null;
    this.testTruckId = null;
    this.testResults = [];
  }

  async initialize() {
    console.log('🔧 Initializing validation environment...');
    
    this.client = await getClient();
    
    // Create test driver
    const driverResult = await this.client.query(`
      INSERT INTO drivers (employee_id, full_name, license_number, license_expiry, hire_date, status, created_at, updated_at)
      VALUES ('VALIDATE-DR-001', 'Validation Test Driver', 'TEST-LIC-001', '2026-12-31', '2024-01-01', 'active', NOW(), NOW())
      ON CONFLICT (employee_id) DO UPDATE SET
        full_name = EXCLUDED.full_name,
        license_number = EXCLUDED.license_number,
        license_expiry = EXCLUDED.license_expiry,
        hire_date = EXCLUDED.hire_date,
        status = EXCLUDED.status,
        updated_at = NOW()
      RETURNING id
    `);
    this.testDriverId = driverResult.rows[0].id;

    // Create test truck
    const truckResult = await this.client.query(`
      INSERT INTO dump_trucks (truck_number, license_plate, status, qr_code_data, created_at, updated_at)
      VALUES ('VALIDATE-DT-100', 'VALIDATE-ABC-123', 'active', '{"id": "VALIDATE-DT-100", "type": "truck"}', NOW(), NOW())
      ON CONFLICT (truck_number) DO UPDATE SET
        license_plate = EXCLUDED.license_plate,
        status = EXCLUDED.status,
        qr_code_data = EXCLUDED.qr_code_data,
        updated_at = NOW()
      RETURNING id
    `);
    this.testTruckId = truckResult.rows[0].id;

    console.log(`✅ Test environment initialized`);
    console.log(`   Driver ID: ${this.testDriverId}`);
    console.log(`   Truck ID: ${this.testTruckId}`);
  }

  async cleanup() {
    if (this.client) {
      try {
        // Clean up test data
        await this.client.query('DELETE FROM driver_shifts WHERE driver_id = $1', [this.testDriverId]);
        await this.client.query('DELETE FROM drivers WHERE employee_id = $1', ['VALIDATE-DR-001']);
        await this.client.query('DELETE FROM dump_trucks WHERE truck_number = $1', ['VALIDATE-DT-100']);
        
        console.log('🧹 Test data cleaned up');
      } catch (error) {
        console.error('⚠️  Error during cleanup:', error.message);
      } finally {
        this.client.release();
      }
    }
  }

  async createTestShift(scenario) {
    // Clean up any existing shifts first
    await this.client.query('DELETE FROM driver_shifts WHERE truck_id = $1', [this.testTruckId]);

    // For QR shifts, use start_date/end_date pattern with proper NULL handling
    const query_text = `
      INSERT INTO driver_shifts (
        truck_id, driver_id, shift_type, start_time, end_time, status,
        start_date, end_date, auto_created, created_at, updated_at
      )
      VALUES ($1, $2, 'day', $3, $4, $5, $6, $7, $8, NOW(), NOW())
      RETURNING id
    `;
    
    const values = [
      this.testTruckId,
      this.testDriverId,
      scenario.setup.start_time,
      scenario.setup.end_time, // Can be NULL for active shifts
      scenario.setup.status,
      scenario.setup.start_date,
      scenario.setup.end_date, // Can be NULL for active shifts
      scenario.setup.auto_created
    ];

    const result = await this.client.query(query_text, values);
    return result.rows[0].id;
  }

  async testDriverCapture(scenario) {
    const timestamp = new Date(scenario.test_timestamp);
    const timestampDate = timestamp.toISOString().split('T')[0];
    const timestampTime = timestamp.toTimeString().split(' ')[0];

    console.log(`\n🧪 Testing: ${scenario.name}`);
    console.log(`   Timestamp: ${scenario.test_timestamp}`);
    console.log(`   Expected: ${scenario.expected_result}`);

    try {
      // Test the primary query (mimicking captureActiveDriverInfo logic)
      const primaryResult = await this.client.query(`
        SELECT
          ds.driver_id,
          d.full_name as driver_name,
          d.employee_id,
          ds.id as shift_id,
          ds.shift_type,
          ds.start_date,
          ds.end_date,
          ds.start_time,
          ds.end_time,
          ds.shift_date,
          ds.auto_created,
          ds.status
        FROM driver_shifts ds
        JOIN drivers d ON ds.driver_id = d.id
        WHERE ds.truck_id = $1
          AND ds.status = 'active'
          AND d.status = 'active'
          AND (
            -- QR-created active shifts: if end_time is NULL, shift is active from start_date onwards
            (ds.start_date IS NOT NULL AND ds.auto_created = true AND ds.end_time IS NULL AND 
             $2::date >= ds.start_date) OR
            -- QR-created completed shifts: check if timestamp falls within date range
            (ds.start_date IS NOT NULL AND ds.auto_created = true AND ds.end_time IS NOT NULL AND 
             $2::date BETWEEN ds.start_date AND ds.end_date) OR
            -- Legacy manual shifts: check if timestamp matches shift date
            (ds.start_date IS NULL AND ds.shift_date IS NOT NULL AND ds.shift_date = $2::date) OR
            -- Manual shifts with start_date/end_date: check date range
            (ds.start_date IS NOT NULL AND ds.auto_created = false AND 
             $2::date BETWEEN ds.start_date AND COALESCE(ds.end_date, ds.start_date))
          )
        ORDER BY 
          ds.auto_created DESC,  -- Prioritize QR-created shifts
          ds.created_at DESC
        LIMIT 1
      `, [this.testTruckId, timestampDate]);

      let driverFound = false;
      let driverInfo = null;

      if (primaryResult.rows.length > 0) {
        const driver = primaryResult.rows[0];
        
        console.log(`   Primary query found driver:`, {
          driver_id: driver.driver_id,
          shift_id: driver.shift_id,
          auto_created: driver.auto_created,
          start_time: driver.start_time,
          end_time: driver.end_time,
          shift_type: driver.shift_type,
          status: driver.status
        });
        
        // Time validation logic
        let timeMatches = true;
        
        if (driver.auto_created && driver.end_time === null) {
          // QR-created active shifts with no end_time are always active
          timeMatches = true;
          console.log(`   Time validation: QR shift with NULL end_time - always active`);
        } else if (driver.end_time !== null) {
          // Validate time range for shifts with end_time
          const startTime = driver.start_time;
          const endTime = driver.end_time;
          const currentTime = timestampTime;
          
          console.log(`   Time validation check:`, {
            start_time: startTime,
            end_time: endTime,
            current_time: currentTime,
            shift_type: driver.shift_type
          });
          
          if (driver.shift_type === 'day') {
            timeMatches = currentTime >= startTime && currentTime <= endTime;
          } else if (driver.shift_type === 'night') {
            if (endTime < startTime) {
              // Overnight shift
              timeMatches = currentTime >= startTime || currentTime <= endTime;
            } else {
              // Same day night shift
              timeMatches = currentTime >= startTime && currentTime <= endTime;
            }
          }
          
          console.log(`   Time validation result: ${timeMatches}`);
        }
        
        if (timeMatches) {
          driverFound = true;
          driverInfo = {
            driver_id: driver.driver_id,
            driver_name: driver.driver_name,
            employee_id: driver.employee_id,
            shift_id: driver.shift_id,
            shift_type: driver.shift_type
          };
        } else {
          console.log(`   Driver found but time validation failed`);
        }
      } else {
        console.log(`   Primary query found no drivers`);
      }

      // Test fallback 1 if primary didn't find driver
      if (!driverFound) {
        const fallback1Result = await this.client.query(`
          SELECT
            ds.driver_id,
            d.full_name as driver_name,
            d.employee_id,
            ds.id as shift_id,
            ds.shift_type
          FROM driver_shifts ds
          JOIN drivers d ON ds.driver_id = d.id
          WHERE ds.truck_id = $1
            AND ds.status = 'active'
            AND d.status = 'active'
            AND ds.auto_created = true
            AND ds.start_date IS NOT NULL
            AND $2::date >= ds.start_date
            AND ds.end_time IS NULL
          ORDER BY ds.created_at DESC
          LIMIT 1
        `, [this.testTruckId, timestampDate]);

        if (fallback1Result.rows.length > 0) {
          driverFound = true;
          driverInfo = fallback1Result.rows[0];
        }
      }

      // Validate result
      const actualResult = driverFound ? 'FOUND' : 'NOT_FOUND';
      const testPassed = actualResult === scenario.expected_result;

      console.log(`   Result: ${actualResult} ${testPassed ? '✅' : '❌'}`);
      
      if (driverInfo) {
        console.log(`   Driver: ${driverInfo.driver_name} (${driverInfo.employee_id})`);
        console.log(`   Shift: ${driverInfo.shift_id} (${driverInfo.shift_type})`);
      }

      return {
        scenario_id: scenario.id,
        scenario_name: scenario.name,
        expected: scenario.expected_result,
        actual: actualResult,
        passed: testPassed,
        driver_info: driverInfo,
        requirements: scenario.requirements
      };

    } catch (error) {
      console.log(`   Error: ${error.message} ❌`);
      return {
        scenario_id: scenario.id,
        scenario_name: scenario.name,
        expected: scenario.expected_result,
        actual: 'ERROR',
        passed: false,
        error: error.message,
        requirements: scenario.requirements
      };
    }
  }

  async validateAllScenarios() {
    console.log('\n🚀 Starting captureActiveDriverInfo validation...\n');

    for (const scenario of TEST_SCENARIOS) {
      try {
        // Create test shift for this scenario
        await this.createTestShift(scenario);
        
        // Test driver capture
        const result = await this.testDriverCapture(scenario);
        this.testResults.push(result);

      } catch (error) {
        console.error(`💥 Error in scenario ${scenario.id}:`, error.message);
        this.testResults.push({
          scenario_id: scenario.id,
          scenario_name: scenario.name,
          expected: scenario.expected_result,
          actual: 'SETUP_ERROR',
          passed: false,
          error: error.message,
          requirements: scenario.requirements
        });
      }
    }
  }

  generateReport() {
    console.log('\n' + '='.repeat(80));
    console.log('📊 CAPTUREACTIVEDRIVERINFO VALIDATION REPORT');
    console.log('='.repeat(80));

    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;

    console.log(`\n📈 Summary:`);
    console.log(`   Total Scenarios: ${totalTests}`);
    console.log(`   Passed: ${passedTests}`);
    console.log(`   Failed: ${failedTests}`);
    console.log(`   Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

    console.log(`\n📋 Detailed Results:`);
    this.testResults.forEach((result, index) => {
      const status = result.passed ? '✅ PASS' : '❌ FAIL';
      console.log(`\n   ${index + 1}. ${result.scenario_name}`);
      console.log(`      Status: ${status}`);
      console.log(`      Expected: ${result.expected}, Actual: ${result.actual}`);
      console.log(`      Requirements: ${result.requirements.join(', ')}`);
      
      if (result.error) {
        console.log(`      Error: ${result.error}`);
      }
      
      if (result.driver_info) {
        console.log(`      Driver Found: ${result.driver_info.driver_name} (${result.driver_info.employee_id})`);
      }
    });

    // Requirements coverage
    const allRequirements = ['2.1', '2.2', '2.3', '2.4', '2.5', '5.1', '5.2', '5.3', '5.4', '5.5'];
    const testedRequirements = new Set();
    const passedRequirements = new Set();

    this.testResults.forEach(result => {
      result.requirements.forEach(req => {
        testedRequirements.add(req);
        if (result.passed) {
          passedRequirements.add(req);
        }
      });
    });

    console.log(`\n✅ Requirements Coverage:`);
    allRequirements.forEach(req => {
      const tested = testedRequirements.has(req);
      const passed = passedRequirements.has(req);
      const status = !tested ? '⚪ Not Tested' : passed ? '✅ Passed' : '❌ Failed';
      console.log(`   ${req}: ${status}`);
    });

    console.log('\n' + '='.repeat(80));

    if (failedTests === 0) {
      console.log('🎉 ALL VALIDATION TESTS PASSED!');
      console.log('✅ captureActiveDriverInfo function is working correctly for all scenarios.');
      return true;
    } else {
      console.log(`⚠️  ${failedTests} validation test(s) failed.`);
      console.log('❌ Some scenarios may not be working as expected.');
      return false;
    }
  }
}

// Main execution
async function main() {
  const validator = new CaptureActiveDriverValidator();
  
  try {
    await validator.initialize();
    await validator.validateAllScenarios();
    const allPassed = validator.generateReport();
    
    process.exit(allPassed ? 0 : 1);
    
  } catch (error) {
    console.error('💥 Validation failed with error:', error);
    process.exit(1);
  } finally {
    await validator.cleanup();
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { CaptureActiveDriverValidator, TEST_SCENARIOS };