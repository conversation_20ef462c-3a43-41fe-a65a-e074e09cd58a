const TripLogsValidationService = require('../services/TripLogsValidationService');
const { query, getClient } = require('../config/database');

/**
 * Test suite for Trip Logs Validation Service
 * Tests all validation functions to ensure they work correctly
 */

describe('TripLogsValidationService', () => {
  let testTripIds = [];

  // Setup test data before running tests
  beforeAll(async () => {
    // Create test trip_logs entries with various data quality scenarios
    const client = await getClient();
    
    try {
      // Test trip 1: Complete data (should pass all validations)
      const completeTrip = await client.query(`
        INSERT INTO trip_logs (
          assignment_id, trip_number, status,
          performed_by_driver_id, performed_by_driver_name, performed_by_employee_id,
          performed_by_shift_id, performed_by_shift_type,
          notes, location_sequence, workflow_type,
          created_at
        ) VALUES (
          1, 1, 'loading_start',
          1, 'Test Driver', 'EMP001',
          1, 'day',
          '"Loading started at Test Location by Test Driver (EMP001)"'::jsonb, 
          '1'::jsonb, 'standard',
          NOW()
        ) RETURNING id
      `);
      testTripIds.push(completeTrip.rows[0].id);

      // Test trip 2: Missing driver information (should fail driver validation)
      const missingDriverTrip = await client.query(`
        INSERT INTO trip_logs (
          assignment_id, trip_number, status,
          notes, location_sequence, workflow_type,
          created_at
        ) VALUES (
          2, 1, 'loading_start',
          '"Loading started but no driver info captured"'::jsonb,
          '1'::jsonb, 'standard',
          NOW()
        ) RETURNING id
      `);
      testTripIds.push(missingDriverTrip.rows[0].id);

      // Test trip 3: Invalid notes (should fail notes validation)
      const invalidNotesTrip = await client.query(`
        INSERT INTO trip_logs (
          assignment_id, trip_number, status,
          performed_by_driver_id, performed_by_driver_name, performed_by_employee_id,
          performed_by_shift_id, performed_by_shift_type,
          notes, location_sequence, workflow_type,
          created_at
        ) VALUES (
          3, 1, 'loading_start',
          2, 'Test Driver 2', 'EMP002',
          2, 'night',
          '"Bad"'::jsonb,
          '1'::jsonb, 'standard',
          NOW()
        ) RETURNING id
      `);
      testTripIds.push(invalidNotesTrip.rows[0].id);

      // Test trip 4: Missing location sequence (should fail sequence validation)
      const missingSequenceTrip = await client.query(`
        INSERT INTO trip_logs (
          assignment_id, trip_number, status,
          performed_by_driver_id, performed_by_driver_name, performed_by_employee_id,
          performed_by_shift_id, performed_by_shift_type,
          notes, workflow_type,
          created_at
        ) VALUES (
          4, 1, 'loading_start',
          3, 'Test Driver 3', 'EMP003',
          3, 'day',
          '"Loading started but no sequence info"'::jsonb,
          'standard',
          NOW()
        ) RETURNING id
      `);
      testTripIds.push(missingSequenceTrip.rows[0].id);

      // Test trip 5: Extended workflow with correct sequence
      const extendedTrip = await client.query(`
        INSERT INTO trip_logs (
          assignment_id, trip_number, status,
          performed_by_driver_id, performed_by_driver_name, performed_by_employee_id,
          performed_by_shift_id, performed_by_shift_type,
          notes, location_sequence, workflow_type,
          created_at
        ) VALUES (
          5, 1, 'loading_start',
          4, 'Test Driver 4', 'EMP004',
          4, 'day',
          '"Extended workflow trip at location 2"'::jsonb,
          '2'::jsonb, 'extended',
          NOW()
        ) RETURNING id
      `);
      testTripIds.push(extendedTrip.rows[0].id);

    } catch (error) {
      console.error('Error setting up test data:', error);
      throw error;
    } finally {
      client.release();
    }
  });

  // Cleanup test data after tests
  afterAll(async () => {
    if (testTripIds.length > 0) {
      try {
        await query(`DELETE FROM trip_logs WHERE id = ANY($1)`, [testTripIds]);
        console.log(`Cleaned up ${testTripIds.length} test trip_logs entries`);
      } catch (error) {
        console.error('Error cleaning up test data:', error);
      }
    }
  });

  describe('validateDriverInformationCompleteness', () => {
    test('should identify complete and incomplete driver information', async () => {
      const result = await TripLogsValidationService.validateDriverInformationCompleteness(1);
      
      expect(result).toHaveProperty('total_trips');
      expect(result).toHaveProperty('complete_driver_info');
      expect(result).toHaveProperty('missing_driver_info');
      expect(result).toHaveProperty('success_rate');
      expect(result).toHaveProperty('missing_breakdown');
      expect(result).toHaveProperty('incomplete_trips');

      // Should have at least our test data
      expect(result.total_trips).toBeGreaterThanOrEqual(5);
      expect(result.missing_driver_info).toBeGreaterThanOrEqual(1); // At least one missing
      expect(result.complete_driver_info).toBeGreaterThanOrEqual(3); // At least three complete

      // Success rate should be a percentage string
      expect(result.success_rate).toMatch(/^\d+(\.\d+)?%$/);
    });

    test('should provide detailed breakdown of missing fields', async () => {
      const result = await TripLogsValidationService.validateDriverInformationCompleteness(1);
      
      expect(result.missing_breakdown).toBeDefined();
      expect(result.incomplete_trips).toBeDefined();
      expect(Array.isArray(result.incomplete_trips)).toBe(true);
    });
  });

  describe('validateNotesFieldQuality', () => {
    test('should identify valid and invalid notes', async () => {
      const result = await TripLogsValidationService.validateNotesFieldQuality(1);
      
      expect(result).toHaveProperty('total_trips');
      expect(result).toHaveProperty('valid_notes');
      expect(result).toHaveProperty('invalid_notes');
      expect(result).toHaveProperty('success_rate');
      expect(result).toHaveProperty('notes_breakdown');
      expect(result).toHaveProperty('average_notes_length');

      // Should have at least our test data
      expect(result.total_trips).toBeGreaterThanOrEqual(5);
      expect(result.invalid_notes).toBeGreaterThanOrEqual(1); // At least one invalid (too short)
      expect(result.valid_notes).toBeGreaterThanOrEqual(3); // At least three valid

      // Success rate should be a percentage string
      expect(result.success_rate).toMatch(/^\d+(\.\d+)?%$/);
      
      // Average notes length should be a number
      expect(typeof result.average_notes_length).toBe('number');
    });
  });

  describe('validateLocationSequenceAccuracy', () => {
    test('should validate location sequences for different workflow types', async () => {
      const result = await TripLogsValidationService.validateLocationSequenceAccuracy(1);
      
      expect(result).toHaveProperty('total_trips');
      expect(result).toHaveProperty('valid_sequences');
      expect(result).toHaveProperty('invalid_sequences');
      expect(result).toHaveProperty('success_rate');
      expect(result).toHaveProperty('sequence_breakdown');
      expect(result).toHaveProperty('workflow_breakdown');

      // Should have at least our test data
      expect(result.total_trips).toBeGreaterThanOrEqual(5);
      expect(result.invalid_sequences).toBeGreaterThanOrEqual(1); // At least one missing sequence
      expect(result.valid_sequences).toBeGreaterThanOrEqual(3); // At least three valid

      // Should have workflow breakdown
      expect(result.workflow_breakdown).toHaveProperty('standard');
      expect(result.workflow_breakdown).toHaveProperty('extended');
    });
  });

  describe('detectMissingRequiredFields', () => {
    test('should detect trips with missing required fields', async () => {
      const result = await TripLogsValidationService.detectMissingRequiredFields(1);
      
      expect(result).toHaveProperty('total_trips');
      expect(result).toHaveProperty('complete_trips');
      expect(result).toHaveProperty('incomplete_trips');
      expect(result).toHaveProperty('completeness_rate');
      expect(result).toHaveProperty('field_missing_counts');
      expect(result).toHaveProperty('most_problematic_trips');

      // Should have at least our test data
      expect(result.total_trips).toBeGreaterThanOrEqual(5);
      expect(result.incomplete_trips).toBeGreaterThanOrEqual(1); // At least one incomplete
      expect(result.complete_trips).toBeGreaterThanOrEqual(1); // At least one complete

      // Field missing counts should be an object
      expect(typeof result.field_missing_counts).toBe('object');
      
      // Most problematic trips should be an array
      expect(Array.isArray(result.most_problematic_trips)).toBe(true);
    });
  });

  describe('createDataQualityAlerts', () => {
    test('should generate alerts when thresholds are exceeded', async () => {
      // Use very high thresholds to ensure alerts are generated
      const highThresholds = {
        driver_info_success_rate: 99,
        notes_success_rate: 99,
        sequence_success_rate: 99,
        completeness_rate: 99,
        time_range_hours: 1
      };

      const result = await TripLogsValidationService.createDataQualityAlerts(highThresholds);
      
      expect(result).toHaveProperty('alerts_generated');
      expect(result).toHaveProperty('alerts');
      expect(result).toHaveProperty('validation_summary');
      expect(result).toHaveProperty('thresholds_used');

      // Should generate some alerts with high thresholds
      expect(result.alerts_generated).toBeGreaterThanOrEqual(0);
      expect(Array.isArray(result.alerts)).toBe(true);

      // Validation summary should contain all validation results
      expect(result.validation_summary).toHaveProperty('driver_validation');
      expect(result.validation_summary).toHaveProperty('notes_validation');
      expect(result.validation_summary).toHaveProperty('sequence_validation');
      expect(result.validation_summary).toHaveProperty('missing_fields_validation');
    });

    test('should not generate alerts when thresholds are met', async () => {
      // Use very low thresholds to ensure no alerts
      const lowThresholds = {
        driver_info_success_rate: 1,
        notes_success_rate: 1,
        sequence_success_rate: 1,
        completeness_rate: 1,
        time_range_hours: 1
      };

      const result = await TripLogsValidationService.createDataQualityAlerts(lowThresholds);
      
      expect(result.alerts_generated).toBe(0);
      expect(result.alerts).toHaveLength(0);
    });
  });

  describe('runComprehensiveValidation', () => {
    test('should run all validation checks and return comprehensive report', async () => {
      const result = await TripLogsValidationService.runComprehensiveValidation(1);
      
      expect(result).toHaveProperty('validation_timestamp');
      expect(result).toHaveProperty('time_range_hours');
      expect(result).toHaveProperty('execution_time_ms');
      expect(result).toHaveProperty('overall_summary');
      expect(result).toHaveProperty('detailed_results');

      // Overall summary should contain all success rates
      expect(result.overall_summary).toHaveProperty('total_trips_analyzed');
      expect(result.overall_summary).toHaveProperty('driver_info_success_rate');
      expect(result.overall_summary).toHaveProperty('notes_quality_success_rate');
      expect(result.overall_summary).toHaveProperty('location_sequence_success_rate');
      expect(result.overall_summary).toHaveProperty('overall_completeness_rate');

      // Detailed results should contain all validation reports
      expect(result.detailed_results).toHaveProperty('driver_validation');
      expect(result.detailed_results).toHaveProperty('notes_validation');
      expect(result.detailed_results).toHaveProperty('sequence_validation');
      expect(result.detailed_results).toHaveProperty('missing_fields_validation');

      // Execution time should be reasonable (less than 10 seconds)
      expect(result.execution_time_ms).toBeLessThan(10000);
    });
  });

  describe('Error handling', () => {
    test('should handle database errors gracefully', async () => {
      // Mock a database error by using an invalid time range
      await expect(
        TripLogsValidationService.validateDriverInformationCompleteness(-1)
      ).rejects.toThrow();
    });
  });
});

// Integration test for the validation queries
describe('Validation SQL Queries', () => {
  test('should execute validation queries without errors', async () => {
    const queries = [
      // Driver information completeness
      `SELECT COUNT(*) as total_trips,
       COUNT(CASE WHEN performed_by_driver_id IS NOT NULL 
                  AND performed_by_driver_name IS NOT NULL 
                  AND performed_by_employee_id IS NOT NULL 
                  AND performed_by_shift_id IS NOT NULL 
                  AND performed_by_shift_type IS NOT NULL 
             THEN 1 END) as complete_driver_info
       FROM trip_logs 
       WHERE created_at >= NOW() - INTERVAL '1 hour'`,

      // Notes quality check
      `SELECT COUNT(*) as total_trips,
       COUNT(CASE WHEN notes IS NOT NULL 
                  AND jsonb_typeof(notes) IN ('string', 'object')
                  AND LENGTH(notes::text) >= 10
             THEN 1 END) as valid_notes
       FROM trip_logs 
       WHERE created_at >= NOW() - INTERVAL '1 hour'`,

      // Location sequence check
      `SELECT COUNT(*) as total_trips,
       COUNT(CASE WHEN location_sequence IS NOT NULL THEN 1 END) as with_sequence
       FROM trip_logs 
       WHERE created_at >= NOW() - INTERVAL '1 hour'`
    ];

    for (const sql of queries) {
      const result = await query(sql);
      expect(result.rows).toBeDefined();
      expect(result.rows.length).toBeGreaterThan(0);
    }
  });
});