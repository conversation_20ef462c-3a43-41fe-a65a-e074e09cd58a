/**
 * Configuration Validation Test
 * 
 * Simple test to verify mobile test setup is working correctly
 */

describe('Mobile Test Configuration', () => {
  test('should have correct test environment', () => {
    expect(typeof global).toBe('object');
    expect(typeof process).toBe('object');
  });

  test('should have test configuration available', () => {
    expect(global.TEST_CONFIG).toBeDefined();
    expect(global.TEST_CONFIG.baseUrl).toBeDefined();
    expect(global.TEST_CONFIG.mobileTimeout).toBe(15000);
    expect(global.TEST_CONFIG.minTouchTargetSize).toBe(44);
  });

  test('should have helper functions available', () => {
    expect(typeof global.waitForElementWithRetry).toBe('function');
    expect(typeof global.isCI).toBe('function');
  });

  test('should validate minimum touch target size requirement', () => {
    const minSize = global.TEST_CONFIG.minTouchTargetSize;
    expect(minSize).toBeGreaterThanOrEqual(44);
  });

  test('should validate mobile timeout configuration', () => {
    const timeout = global.TEST_CONFIG.mobileTimeout;
    expect(timeout).toBeGreaterThanOrEqual(10000);
    expect(timeout).toBeLessThanOrEqual(30000);
  });
});