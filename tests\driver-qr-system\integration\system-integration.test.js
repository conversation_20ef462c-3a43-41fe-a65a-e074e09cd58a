/**
 * Driver QR System Integration Tests
 * 
 * Comprehensive integration tests to validate the complete driver QR system
 * including all components working together.
 */

const request = require('supertest');
const { query, getClient } = require('../../../server/config/database');
const DriverQRCodeGenerator = require('../../../server/utils/DriverQRCodeGenerator');
const DriverQRService = require('../../../server/services/DriverQRService');

// Mock the server app
const express = require('express');
const app = express();
app.use(express.json());

// Import routes
const driverRoutes = require('../../../server/routes/driver');
app.use('/api/driver', driverRoutes);

describe('Driver QR System Integration Tests', () => {
  let testDriver;
  let testTruck;
  let driverQRData;
  let truckQRData;

  beforeAll(async () => {
    // Set up test data
    await setupTestData();
  });

  afterAll(async () => {
    // Clean up test data
    await cleanupTestData();
  });

  describe('Complete Driver Connect Workflow', () => {
    test('should complete full check-in workflow', async () => {
      // Test the complete workflow: QR generation → validation → check-in
      
      // Step 1: Generate driver QR code
      const qrResult = await DriverQRCodeGenerator.generateDriverQR(
        testDriver.id, 
        testDriver.employee_id
      );
      
      expect(qrResult.success).toBe(true);
      expect(qrResult.qr_data).toBeDefined();
      expect(qrResult.qr_code_image).toBeDefined();
      
      driverQRData = qrResult.qr_data;

      // Step 2: Test driver connect API endpoint
      const connectResponse = await request(app)
        .post('/api/driver/connect')
        .send({
          driver_qr_data: JSON.stringify(driverQRData),
          truck_qr_data: JSON.stringify(truckQRData),
          action: 'check_in'
        });

      expect(connectResponse.status).toBe(200);
      expect(connectResponse.body.success).toBe(true);
      expect(connectResponse.body.action).toBe('check_in');
      expect(connectResponse.body.truck).toBe(testTruck.truck_number);
      expect(connectResponse.body.shift_id).toBeDefined();

      // Step 3: Verify shift was created in database
      const shiftResult = await query(
        'SELECT * FROM driver_shifts WHERE driver_id = $1 AND status = $2',
        [testDriver.id, 'active']
      );

      expect(shiftResult.rows.length).toBe(1);
      expect(shiftResult.rows[0].truck_id).toBe(testTruck.id);
      expect(shiftResult.rows[0].shift_type).toBe('custom');
      expect(shiftResult.rows[0].auto_created).toBe(true);
    });

    test('should complete full check-out workflow', async () => {
      // Test check-out workflow
      const connectResponse = await request(app)
        .post('/api/driver/connect')
        .send({
          driver_qr_data: JSON.stringify(driverQRData),
          truck_qr_data: JSON.stringify(truckQRData),
          action: 'check_out'
        });

      expect(connectResponse.status).toBe(200);
      expect(connectResponse.body.success).toBe(true);
      expect(connectResponse.body.action).toBe('check_out');
      expect(connectResponse.body.duration).toBeDefined();
      expect(connectResponse.body.check_in_time).toBeDefined();
      expect(connectResponse.body.check_out_time).toBeDefined();

      // Verify shift was completed in database
      const shiftResult = await query(
        'SELECT * FROM driver_shifts WHERE driver_id = $1 AND status = $2',
        [testDriver.id, 'completed']
      );

      expect(shiftResult.rows.length).toBeGreaterThan(0);
    });

    test('should handle driver status check', async () => {
      const statusResponse = await request(app)
        .get(`/api/driver/status/${testDriver.employee_id}`);

      expect(statusResponse.status).toBe(200);
      expect(statusResponse.body.success).toBe(true);
      expect(statusResponse.body.driver.employee_id).toBe(testDriver.employee_id);
      expect(statusResponse.body.status).toBeDefined();
    });
  });

  describe('Security and Validation Tests', () => {
    test('should reject invalid driver QR codes', async () => {
      const invalidQR = {
        id: 'INVALID',
        driver_id: 99999,
        employee_id: 'INVALID',
        generated_date: new Date().toISOString(),
        type: 'driver'
      };

      const connectResponse = await request(app)
        .post('/api/driver/connect')
        .send({
          driver_qr_data: JSON.stringify(invalidQR),
          truck_qr_data: JSON.stringify(truckQRData)
        });

      expect(connectResponse.status).toBe(400);
      expect(connectResponse.body.success).toBe(false);
      expect(connectResponse.body.error).toBe('INVALID_DRIVER_QR');
    });

    test('should reject invalid truck QR codes', async () => {
      const invalidTruckQR = {
        id: 'INVALID-TRUCK',
        type: 'truck'
      };

      const connectResponse = await request(app)
        .post('/api/driver/connect')
        .send({
          driver_qr_data: JSON.stringify(driverQRData),
          truck_qr_data: JSON.stringify(invalidTruckQR)
        });

      expect(connectResponse.status).toBe(400);
      expect(connectResponse.body.success).toBe(false);
      expect(connectResponse.body.error).toBe('TRUCK_NOT_FOUND');
    });

    test('should handle rate limiting', async () => {
      // Skip in development mode
      if (process.env.NODE_ENV === 'development') {
        return;
      }

      // Make multiple rapid requests to trigger rate limiting
      const requests = Array(105).fill().map(() => 
        request(app)
          .get(`/api/driver/status/${testDriver.employee_id}`)
      );

      const responses = await Promise.all(requests);
      
      // Some requests should be rate limited
      const rateLimitedResponses = responses.filter(res => res.status === 429);
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });
  });

  describe('Business Logic Tests', () => {
    test('should handle shift handover scenario', async () => {
      // Create another test driver for handover
      const secondDriver = await createTestDriver('DR-HANDOVER', 'Handover Driver');
      
      try {
        // First driver checks in
        await request(app)
          .post('/api/driver/connect')
          .send({
            driver_qr_data: JSON.stringify(driverQRData),
            truck_qr_data: JSON.stringify(truckQRData),
            action: 'check_in'
          });

        // Generate QR for second driver
        const secondQRResult = await DriverQRCodeGenerator.generateDriverQR(
          secondDriver.id, 
          secondDriver.employee_id
        );

        // Second driver checks in (should trigger handover)
        const handoverResponse = await request(app)
          .post('/api/driver/connect')
          .send({
            driver_qr_data: JSON.stringify(secondQRResult.qr_data),
            truck_qr_data: JSON.stringify(truckQRData),
            action: 'check_in'
          });

        expect(handoverResponse.status).toBe(200);
        expect(handoverResponse.body.success).toBe(true);
        expect(handoverResponse.body.action).toBe('handover');
        expect(handoverResponse.body.previous_truck).toBeDefined();
        expect(handoverResponse.body.new_truck).toBe(testTruck.truck_number);

        // Verify first driver's shift was completed
        const firstDriverShifts = await query(
          'SELECT * FROM driver_shifts WHERE driver_id = $1 AND status = $2',
          [testDriver.id, 'completed']
        );
        expect(firstDriverShifts.rows.length).toBeGreaterThan(0);

        // Verify second driver has active shift
        const secondDriverShifts = await query(
          'SELECT * FROM driver_shifts WHERE driver_id = $1 AND status = $2',
          [secondDriver.id, 'active']
        );
        expect(secondDriverShifts.rows.length).toBe(1);

      } finally {
        // Clean up second driver
        await query('DELETE FROM drivers WHERE id = $1', [secondDriver.id]);
      }
    });

    test('should validate driver and truck status', async () => {
      // Test with inactive driver
      await query('UPDATE drivers SET status = $1 WHERE id = $2', ['inactive', testDriver.id]);

      const inactiveDriverResponse = await request(app)
        .post('/api/driver/connect')
        .send({
          driver_qr_data: JSON.stringify(driverQRData),
          truck_qr_data: JSON.stringify(truckQRData)
        });

      expect(inactiveDriverResponse.status).toBe(400);
      expect(inactiveDriverResponse.body.error).toBe('INVALID_DRIVER_QR');

      // Restore driver status
      await query('UPDATE drivers SET status = $1 WHERE id = $2', ['active', testDriver.id]);

      // Test with inactive truck
      await query('UPDATE dump_trucks SET status = $1 WHERE id = $2', ['inactive', testTruck.id]);

      const inactiveTruckResponse = await request(app)
        .post('/api/driver/connect')
        .send({
          driver_qr_data: JSON.stringify(driverQRData),
          truck_qr_data: JSON.stringify(truckQRData)
        });

      expect(inactiveTruckResponse.status).toBe(400);
      expect(inactiveTruckResponse.body.error).toBe('TRUCK_INACTIVE');

      // Restore truck status
      await query('UPDATE dump_trucks SET status = $1 WHERE id = $2', ['active', testTruck.id]);
    });
  });

  describe('Database Integration Tests', () => {
    test('should maintain data integrity during concurrent operations', async () => {
      // Test concurrent check-in attempts
      const concurrentRequests = Array(5).fill().map(() =>
        request(app)
          .post('/api/driver/connect')
          .send({
            driver_qr_data: JSON.stringify(driverQRData),
            truck_qr_data: JSON.stringify(truckQRData),
            action: 'check_in'
          })
      );

      const responses = await Promise.all(concurrentRequests);
      
      // Only one should succeed, others should fail gracefully
      const successfulResponses = responses.filter(res => res.status === 200);
      expect(successfulResponses.length).toBe(1);

      // Verify only one active shift exists
      const activeShifts = await query(
        'SELECT * FROM driver_shifts WHERE driver_id = $1 AND status = $2',
        [testDriver.id, 'active']
      );
      expect(activeShifts.rows.length).toBe(1);
    });

    test('should handle database transaction rollbacks', async () => {
      // This test would require more complex setup to force transaction failures
      // For now, just verify basic transaction handling works
      const client = await getClient();
      
      try {
        await client.query('BEGIN');
        
        // Perform some operations
        const result = await client.query(
          'SELECT COUNT(*) as count FROM driver_shifts WHERE driver_id = $1',
          [testDriver.id]
        );
        
        expect(result.rows[0].count).toBeDefined();
        
        await client.query('COMMIT');
      } catch (error) {
        await client.query('ROLLBACK');
        throw error;
      } finally {
        client.release();
      }
    });
  });

  // Helper functions
  async function setupTestData() {
    // Create test driver
    testDriver = await createTestDriver('DR-TEST-001', 'Test Driver');
    
    // Create test truck
    testTruck = await createTestTruck('DT-TEST-001', 'Test Truck');
    
    // Create truck QR data
    truckQRData = {
      id: testTruck.truck_number,
      type: 'truck',
      generated_date: new Date().toISOString()
    };

    // Update truck with QR data
    await query(
      'UPDATE dump_trucks SET qr_code_data = $1 WHERE id = $2',
      [JSON.stringify(truckQRData), testTruck.id]
    );
  }

  async function createTestDriver(employeeId, fullName) {
    const result = await query(
      `INSERT INTO drivers (employee_id, full_name, license_number, status, created_at, updated_at)
       VALUES ($1, $2, $3, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
       RETURNING *`,
      [employeeId, fullName, `LIC-${employeeId}`]
    );
    return result.rows[0];
  }

  async function createTestTruck(truckNumber, description) {
    const result = await query(
      `INSERT INTO dump_trucks (truck_number, license_plate, status, created_at, updated_at)
       VALUES ($1, $2, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
       RETURNING *`,
      [truckNumber, `${truckNumber}-PLATE`, description]
    );
    return result.rows[0];
  }

  async function cleanupTestData() {
    try {
      // Clean up shifts first (foreign key dependency)
      if (testDriver) {
        await query('DELETE FROM driver_shifts WHERE driver_id = $1', [testDriver.id]);
        await query('DELETE FROM drivers WHERE id = $1', [testDriver.id]);
      }
      
      if (testTruck) {
        await query('DELETE FROM dump_trucks WHERE id = $1', [testTruck.id]);
      }
    } catch (error) {
      console.error('Cleanup error:', error);
    }
  }
});