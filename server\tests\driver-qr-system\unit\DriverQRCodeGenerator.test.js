const DriverQRCodeGenerator = require('../../../utils/DriverQRCodeGenerator');

// Mock dependencies
jest.mock('../../../config/database', () => ({
  query: jest.fn()
}));
jest.mock('../../../utils/logger', () => ({
  logError: jest.fn(),
  logInfo: jest.fn()
}));

const { query } = require('../../../config/database');

describe('DriverQRCodeGenerator', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('generateQRData', () => {
    it('should generate valid QR data structure', () => {
      const driverId = 123;
      const employeeId = 'DR-001';
      
      const qrData = DriverQRCodeGenerator.generateQRData(driverId, employeeId);
      
      expect(qrData).toHaveProperty('id', employeeId);
      expect(qrData).toHaveProperty('driver_id', driverId);
      expect(qrData).toHaveProperty('employee_id', employeeId);
      expect(qrData).toHaveProperty('generated_date');
      expect(qrData).toHaveProperty('type', 'driver');
      
      // Validate date format
      expect(new Date(qrData.generated_date)).toBeInstanceOf(Date);
      expect(new Date(qrData.generated_date).getTime()).not.toBeNaN();
    });

    it('should generate unique timestamps for different calls', async () => {
      const qrData1 = DriverQRCodeGenerator.generateQRData(1, 'DR-001');
      
      // Small delay to ensure different timestamp
      await new Promise(resolve => setTimeout(resolve, 1));
      
      const qrData2 = DriverQRCodeGenerator.generateQRData(2, 'DR-002');
      
      expect(qrData1.generated_date).not.toBe(qrData2.generated_date);
    });
  });

  describe('generateDriverQR', () => {
    it('should generate QR code for valid active driver', async () => {
      const mockDriver = {
        id: 123,
        employee_id: 'DR-001',
        full_name: 'John Doe',
        status: 'active'
      };

      query
        .mockResolvedValueOnce({ rows: [mockDriver] }) // Driver lookup
        .mockResolvedValueOnce({ rows: [] }); // Update query

      const result = await DriverQRCodeGenerator.generateDriverQR(123, 'DR-001');

      expect(result.success).toBe(true);
      expect(result.qr_data).toHaveProperty('driver_id', 123);
      expect(result.qr_data).toHaveProperty('employee_id', 'DR-001');
      expect(result.qr_data).toHaveProperty('type', 'driver');
      expect(result.qr_code_image).toMatch(/^data:image\/png;base64,/);
      expect(result.driver).toEqual({
        id: 123,
        employee_id: 'DR-001',
        full_name: 'John Doe'
      });

      // Verify database update was called
      expect(query).toHaveBeenCalledWith(
        'UPDATE drivers SET driver_qr_code = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
        [expect.any(String), 123]
      );
    });

    it('should throw error for missing driver ID', async () => {
      await expect(
        DriverQRCodeGenerator.generateDriverQR(null, 'DR-001')
      ).rejects.toThrow('Driver ID and Employee ID are required');
    });

    it('should throw error for missing employee ID', async () => {
      await expect(
        DriverQRCodeGenerator.generateDriverQR(123, null)
      ).rejects.toThrow('Driver ID and Employee ID are required');
    });

    it('should throw error for non-existent driver', async () => {
      query.mockResolvedValueOnce({ rows: [] });

      await expect(
        DriverQRCodeGenerator.generateDriverQR(999, 'DR-999')
      ).rejects.toThrow('Driver not found');
    });

    it('should throw error for inactive driver', async () => {
      const mockDriver = {
        id: 123,
        employee_id: 'DR-001',
        full_name: 'John Doe',
        status: 'inactive'
      };

      query.mockResolvedValueOnce({ rows: [mockDriver] });

      await expect(
        DriverQRCodeGenerator.generateDriverQR(123, 'DR-001')
      ).rejects.toThrow('Cannot generate QR code for inactive driver');
    });

    it('should throw error for employee ID mismatch', async () => {
      const mockDriver = {
        id: 123,
        employee_id: 'DR-001',
        full_name: 'John Doe',
        status: 'active'
      };

      query.mockResolvedValueOnce({ rows: [mockDriver] });

      await expect(
        DriverQRCodeGenerator.generateDriverQR(123, 'DR-002')
      ).rejects.toThrow('Employee ID mismatch');
    });
  });

  describe('validateDriverQR', () => {
    const validQRData = {
      id: 'DR-001',
      driver_id: 123,
      employee_id: 'DR-001',
      generated_date: '2025-01-01T00:00:00.000Z',
      type: 'driver'
    };

    const mockDriver = {
      id: 123,
      employee_id: 'DR-001',
      full_name: 'John Doe',
      status: 'active',
      driver_qr_code: validQRData
    };

    it('should validate correct QR data object', async () => {
      query.mockResolvedValueOnce({ rows: [mockDriver] });

      const result = await DriverQRCodeGenerator.validateDriverQR(validQRData);

      expect(result.success).toBe(true);
      expect(result.valid).toBe(true);
      expect(result.driver).toEqual({
        id: 123,
        employee_id: 'DR-001',
        full_name: 'John Doe',
        status: 'active'
      });
      expect(result.qr_data).toEqual(validQRData);
    });

    it('should validate correct QR data JSON string', async () => {
      query.mockResolvedValueOnce({ rows: [mockDriver] });

      const qrDataString = JSON.stringify(validQRData);
      const result = await DriverQRCodeGenerator.validateDriverQR(qrDataString);

      expect(result.success).toBe(true);
      expect(result.valid).toBe(true);
    });

    it('should reject invalid JSON string', async () => {
      const result = await DriverQRCodeGenerator.validateDriverQR('invalid json');

      expect(result.success).toBe(false);
      expect(result.valid).toBe(false);
      expect(result.error).toBe('Invalid QR code format - not valid JSON');
    });

    it('should reject QR data missing required fields', async () => {
      const incompleteData = {
        id: 'DR-001',
        driver_id: 123
        // Missing employee_id, generated_date, type
      };

      const result = await DriverQRCodeGenerator.validateDriverQR(incompleteData);

      expect(result.success).toBe(false);
      expect(result.valid).toBe(false);
      expect(result.error).toBe('Missing required field: employee_id');
    });

    it('should reject invalid driver_id format', async () => {
      const invalidData = {
        ...validQRData,
        driver_id: 'not-a-number'
      };

      const result = await DriverQRCodeGenerator.validateDriverQR(invalidData);

      expect(result.success).toBe(false);
      expect(result.valid).toBe(false);
      expect(result.error).toBe('Invalid driver_id format');
    });

    it('should reject wrong QR code type', async () => {
      const invalidData = {
        ...validQRData,
        type: 'truck'
      };

      const result = await DriverQRCodeGenerator.validateDriverQR(invalidData);

      expect(result.success).toBe(false);
      expect(result.valid).toBe(false);
      expect(result.error).toBe('Invalid QR code type - expected "driver"');
    });

    it('should reject invalid date format', async () => {
      const invalidData = {
        ...validQRData,
        generated_date: 'invalid-date'
      };

      const result = await DriverQRCodeGenerator.validateDriverQR(invalidData);

      expect(result.success).toBe(false);
      expect(result.valid).toBe(false);
      expect(result.error).toBe('Invalid generated_date format');
    });

    it('should reject for non-existent driver', async () => {
      query.mockResolvedValueOnce({ rows: [] });

      const result = await DriverQRCodeGenerator.validateDriverQR(validQRData);

      expect(result.success).toBe(false);
      expect(result.valid).toBe(false);
      expect(result.error).toBe('Driver not found or employee ID mismatch');
    });

    it('should reject for inactive driver', async () => {
      const inactiveDriver = {
        ...mockDriver,
        status: 'inactive'
      };
      query.mockResolvedValueOnce({ rows: [inactiveDriver] });

      const result = await DriverQRCodeGenerator.validateDriverQR(validQRData);

      expect(result.success).toBe(false);
      expect(result.valid).toBe(false);
      expect(result.error).toBe('Driver account is inactive. Please contact your supervisor.');
    });

    it('should accept QR codes with different timestamps (legacy support)', async () => {
      const driverWithOldQR = {
        ...mockDriver,
        driver_qr_code: {
          ...validQRData,
          generated_date: '2024-01-01T00:00:00.000Z' // Different/old date should be accepted
        }
      };
      query.mockResolvedValueOnce({ rows: [driverWithOldQR] });

      const result = await DriverQRCodeGenerator.validateDriverQR(validQRData);

      expect(result.success).toBe(true);
      expect(result.valid).toBe(true);
      expect(result.driver).toBeDefined();
      expect(result.driver.employee_id).toBe(validQRData.employee_id);
    });
  });

  describe('getDriverQRInfo', () => {
    it('should return driver QR info for existing driver', async () => {
      const mockDriver = {
        id: 123,
        employee_id: 'DR-001',
        full_name: 'John Doe',
        status: 'active',
        driver_qr_code: { id: 'DR-001', type: 'driver' },
        updated_at: '2025-01-01T00:00:00.000Z'
      };

      query.mockResolvedValueOnce({ rows: [mockDriver] });

      const result = await DriverQRCodeGenerator.getDriverQRInfo(123);

      expect(result.success).toBe(true);
      expect(result.driver).toEqual({
        id: 123,
        employee_id: 'DR-001',
        full_name: 'John Doe',
        status: 'active'
      });
      expect(result.has_qr_code).toBe(true);
      expect(result.qr_data).toEqual({ id: 'DR-001', type: 'driver' });
      expect(result.last_updated).toBe('2025-01-01T00:00:00.000Z');
    });

    it('should return info for driver without QR code', async () => {
      const mockDriver = {
        id: 123,
        employee_id: 'DR-001',
        full_name: 'John Doe',
        status: 'active',
        driver_qr_code: null,
        updated_at: '2025-01-01T00:00:00.000Z'
      };

      query.mockResolvedValueOnce({ rows: [mockDriver] });

      const result = await DriverQRCodeGenerator.getDriverQRInfo(123);

      expect(result.success).toBe(true);
      expect(result.has_qr_code).toBe(false);
      expect(result.qr_data).toBe(null);
    });

    it('should throw error for non-existent driver', async () => {
      query.mockResolvedValueOnce({ rows: [] });

      await expect(
        DriverQRCodeGenerator.getDriverQRInfo(999)
      ).rejects.toThrow('Driver not found');
    });
  });

  describe('bulkGenerateQR', () => {
    it('should generate QR codes for multiple active drivers', async () => {
      const driverIds = [1, 2, 3];
      
      // Mock driver lookups and updates in sequence
      query
        .mockResolvedValueOnce({ rows: [{ id: 1, employee_id: 'DR-001', status: 'active' }] }) // Driver 1 lookup
        .mockResolvedValueOnce({ rows: [{ id: 1, employee_id: 'DR-001', full_name: 'Driver 1', status: 'active' }] }) // Driver 1 full lookup
        .mockResolvedValueOnce({ rows: [] }) // Driver 1 update
        .mockResolvedValueOnce({ rows: [{ id: 2, employee_id: 'DR-002', status: 'active' }] }) // Driver 2 lookup
        .mockResolvedValueOnce({ rows: [{ id: 2, employee_id: 'DR-002', full_name: 'Driver 2', status: 'active' }] }) // Driver 2 full lookup
        .mockResolvedValueOnce({ rows: [] }) // Driver 2 update
        .mockResolvedValueOnce({ rows: [{ id: 3, employee_id: 'DR-003', status: 'inactive' }] }); // Driver 3 lookup (inactive)

      const result = await DriverQRCodeGenerator.bulkGenerateQR(driverIds);

      expect(result.total).toBe(3);
      expect(result.success).toHaveLength(2);
      expect(result.failed).toHaveLength(1);
      
      expect(result.success[0]).toEqual({
        driver_id: 1,
        employee_id: 'DR-001',
        qr_data: expect.any(Object)
      });
      
      expect(result.failed[0]).toEqual({
        driver_id: 3,
        employee_id: 'DR-003',
        error: 'Driver is not active'
      });
    }, 10000); // 10 second timeout

    it('should handle non-existent drivers', async () => {
      query.mockResolvedValueOnce({ rows: [] });

      const result = await DriverQRCodeGenerator.bulkGenerateQR([999]);

      expect(result.total).toBe(1);
      expect(result.success).toHaveLength(0);
      expect(result.failed).toHaveLength(1);
      expect(result.failed[0]).toEqual({
        driver_id: 999,
        error: 'Driver not found'
      });
    });
  });
});