# Quick Fixes Reference Card

## 🚨 Emergency Commands

### Check System Status
```bash
cd /var/www/hauling-qr-system
./manage-server.sh status
```

### Fix All Services (Emergency Recovery)
```bash
cd /var/www/hauling-qr-system
sudo ./recover-system.sh  # (Created by troubleshooting guide)
```

---

## ⚡ Common Quick Fixes

### 1. Nginx Not Running
```bash
sudo systemctl start nginx
sudo systemctl enable nginx
./manage-server.sh status
```

### 2. PM2/Application Not Running
```bash
cd /var/www/hauling-qr-system
pm2 start server/server.js --name hauling-qr-server
pm2 save
```

### 3. PostgreSQL Not Running
```bash
sudo systemctl start postgresql
sudo systemctl enable postgresql
./manage-database.sh test
```

### 4. Missing Network Tools
```bash
sudo apt-get update
sudo apt-get install -y net-tools
```

### 5. Port Conflicts
```bash
# Check what's using ports
sudo lsof -i :80
sudo lsof -i :443  
sudo lsof -i :5000

# Kill conflicting processes
sudo pkill -f apache2
sudo systemctl stop apache2
```

---

## 🔍 Quick Diagnostics

### Service Status
```bash
sudo systemctl status nginx
sudo systemctl status postgresql
pm2 list
```

### Port Check
```bash
sudo netstat -tlnp | grep -E ':(80|443|5000)'
```

### Log Check
```bash
pm2 logs hauling-qr-server --lines 20
sudo tail -f /var/log/nginx/error.log
```

### Database Check
```bash
./manage-database.sh verify
./manage-database.sh health
```

---

## 📋 Status Indicators

### ✅ Healthy System
```
✅ Application Server (PM2)
✅ Database (PostgreSQL)  
✅ Web Server (Nginx)
✅ Network Ports (80, 443, 5000)
🎉 All services are running correctly!
```

### ❌ Problem Indicators
- `❌ Nginx not running` → Run nginx fixes
- `❌ PM2 not available` → Run PM2 fixes  
- `❌ PostgreSQL status unknown` → Run database fixes
- `❌ No required ports found listening` → Run port fixes

---

## 🆘 When All Else Fails

### Complete System Reset
```bash
# Stop everything
pm2 stop all
sudo systemctl stop nginx

# Start fresh
sudo systemctl start postgresql
cd /var/www/hauling-qr-system
pm2 start server/server.js --name hauling-qr-server
sudo systemctl start nginx

# Check status
./manage-server.sh status
```

### Get Help
```bash
# Generate system report
./manage-server.sh status > system-report.txt
./manage-database.sh health >> system-report.txt
pm2 list >> system-report.txt

# View the report
cat system-report.txt
```

---

## 📚 Full Documentation

- **Complete Troubleshooting**: `POST_DEPLOYMENT_TROUBLESHOOTING.md`
- **Database Management**: `POSTGRESQL_TABLE_INSPECTION_GUIDE.md`
- **Deployment Fixes**: `DEPLOYMENT_FIXES.md`