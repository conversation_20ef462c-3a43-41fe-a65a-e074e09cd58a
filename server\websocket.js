const WebSocket = require('ws');

let wss = null;
const clients = new Map(); // Store client connections with metadata

// Initialize WebSocket server
const initializeWebSocket = (server) => {
  wss = new WebSocket.Server({ server });

  wss.on('connection', (ws, req) => {
    const clientId = generateClientId();
    
    // Store client with metadata
    clients.set(clientId, {
      ws,
      userId: null,
      role: null,
      lastActivity: Date.now()
    });

    // WebSocket client connected

    // Handle authentication message
    ws.on('message', (message) => {
      try {
        const data = JSON.parse(message);
        
        if (data.type === 'auth') {
          const client = clients.get(clientId);
          if (client) {
            client.userId = data.userId;
            client.role = data.role;
            client.lastActivity = Date.now();

            // Send authentication confirmation
            ws.send(JSON.stringify({
              type: 'auth_success',
              message: 'Authenticated successfully'
            }));

            console.log(`Client ${clientId} authenticated as user ${data.userId} with role ${data.role}`);
          }
        } else if (data.type === 'ping') {
          // CRITICAL FIX: Handle heartbeat ping messages to keep connection alive
          const client = clients.get(clientId);
          if (client) {
            client.lastActivity = Date.now();

            // Send pong response
            ws.send(JSON.stringify({
              type: 'pong',
              timestamp: new Date().toISOString()
            }));
          }
        }
      } catch (error) {
        console.error('WebSocket message parse error:', error);
      }
    });

    // Handle client disconnect
    ws.on('close', () => {
      clients.delete(clientId);
      // WebSocket client disconnected
    });

    // Handle connection errors
    ws.on('error', (error) => {
      console.error('WebSocket connection error:', error.message);
      clients.delete(clientId);
    });

    // Send initial welcome message
    ws.send(JSON.stringify({
      type: 'welcome',
      clientId,
      message: 'Connected to Hauling System WebSocket'
    }));
  });

  // Cleanup inactive connections every 30 seconds
  setInterval(() => {
    const now = Date.now();
    const INACTIVE_THRESHOLD = 5 * 60 * 1000; // 5 minutes

    for (const [clientId, client] of clients.entries()) {
      if (now - client.lastActivity > INACTIVE_THRESHOLD) {
        console.log(`Removing inactive client: ${clientId}`);
        client.ws.terminate();
        clients.delete(clientId);
      }
    }
  }, 30000);

  console.log('WebSocket server initialized');
  return wss;
};

// Generate unique client ID
const generateClientId = () => {
  return Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
};

// Broadcast to all authenticated clients
const broadcast = (message) => {
  if (!wss) return;

  const payload = JSON.stringify(message);
  
  clients.forEach((client, clientId) => {
    if (client.ws.readyState === WebSocket.OPEN && client.userId) {
      try {
        client.ws.send(payload);
        client.lastActivity = Date.now();
      } catch (error) {
        console.error(`Failed to send message to client ${clientId}:`, error);
        clients.delete(clientId);
      }
    }
  });
};

// Send to specific users by role
const sendToRole = (role, message) => {
  if (!wss) return;

  const payload = JSON.stringify(message);
  
  clients.forEach((client, clientId) => {
    if (client.ws.readyState === WebSocket.OPEN && client.role === role) {
      try {
        client.ws.send(payload);
        client.lastActivity = Date.now();
      } catch (error) {
        console.error(`Failed to send message to client ${clientId}:`, error);
        clients.delete(clientId);
      }
    }
  });
};

// Send to specific user
const sendToUser = (userId, message) => {
  if (!wss) return;

  const payload = JSON.stringify(message);
  
  clients.forEach((client, clientId) => {
    if (client.ws.readyState === WebSocket.OPEN && client.userId === userId) {
      try {
        client.ws.send(payload);
        client.lastActivity = Date.now();
      } catch (error) {
        console.error(`Failed to send message to client ${clientId}:`, error);
        clients.delete(clientId);
      }
    }
  });
};

// Notification functions for specific events
const notifyExceptionCreated = (exception) => {
  // Enhanced notification for hybrid exceptions
  const isAdaptive = exception.is_adaptive || false;
  const hasInsights = exception.adaptation_insights || false;

  let title = 'New Exception Requires Approval';
  let message = `Route deviation detected: ${exception.description || exception.exception_description}`;

  if (isAdaptive && hasInsights) {
    title = 'Smart Exception with AI Insights';
    message = `Route deviation detected with pattern analysis: ${exception.description || exception.exception_description}`;
  }

  const notificationData = {
    type: 'exception_created',
    title,
    message,
    data: {
      ...exception,
      is_adaptive: isAdaptive,
      has_insights: hasInsights
    },
    timestamp: new Date().toISOString(),
    priority: exception.severity || 'medium'
  };

  // Notify all admins and supervisors
  sendToRole('admin', notificationData);
  sendToRole('supervisor', notificationData);
};

const notifyExceptionUpdated = (exception, decision) => {
  // Enhanced notification for hybrid exception updates
  const description = exception.exception_description || exception.description || 'route deviation';
  const isAdaptive = exception.is_adaptive || false;
  const autoApproved = exception.auto_approved || false;

  let title = `Exception ${decision}`;
  let message = decision === 'approved'
    ? `Route deviation ${decision}: ${description}`
    : `Exception ${decision}: ${description}`;

  // Special handling for auto-approved exceptions
  if (decision === 'auto_approved' || autoApproved) {
    title = 'Exception Auto-Approved';
    message = `Smart system auto-approved: ${description}`;
  } else if (isAdaptive && decision === 'approved') {
    title = 'Smart Exception Approved';
    message = `AI-assisted exception approved: ${description}`;
  }

  broadcast({
    type: 'exception_updated',
    title,
    message,
    data: {
      ...exception,
      is_adaptive: isAdaptive,
      auto_approved: autoApproved
    },
    decision: autoApproved ? 'auto_approved' : decision,
    timestamp: new Date().toISOString()
  });
};

const notifyTripStatusChanged = (trip) => {
  // Notify relevant users about trip status changes
  broadcast({
    type: 'trip_status_changed',
    title: 'Trip Status Updated',
    message: `Trip ${trip.trip_number} status: ${trip.status}`,
    data: trip,
    timestamp: new Date().toISOString()
  });
};

// Route Discovery Notifications
const notifyRouteDiscoveryStarted = (assignment, location) => {
  // Notify when dynamic route discovery begins
  broadcast({
    type: 'route_discovery_started',
    title: 'Route Discovery Started',
    message: `Dynamic route discovery initiated for ${assignment.assignment_code} at ${location.name}`,
    data: {
      assignment_id: assignment.id,
      assignment_code: assignment.assignment_code,
      truck_number: assignment.truck_number,
      trigger_location: {
        id: location.id,
        name: location.name,
        type: location.type
      },
      discovery_mode: 'progressive'
    },
    timestamp: new Date().toISOString(),
    priority: 'low'
  });
};

const notifyRouteLocationConfirmed = (assignment, location, locationType) => {
  // Notify when a route location is confirmed through QR scan
  const isLoading = locationType === 'loading';
  const icon = isLoading ? '📍' : '🎯';
  const action = isLoading ? 'Loading location confirmed' : 'Unloading location confirmed';

  broadcast({
    type: 'route_location_confirmed',
    title: `${action}`,
    message: `${assignment.assignment_code}: ${location.name} confirmed via QR scan`,
    data: {
      assignment_id: assignment.id,
      assignment_code: assignment.assignment_code,
      truck_number: assignment.truck_number,
      confirmed_location: {
        id: location.id,
        name: location.name,
        type: location.type,
        role: locationType
      },
      discovery_progress: isLoading ? 'loading_confirmed' : 'unloading_confirmed'
    },
    icon,
    timestamp: new Date().toISOString(),
    priority: 'medium'
  });
};

// Driver Connect/Disconnect Notifications
const notifyDriverConnected = (driverData, truckData, shiftData) => {
  // Notify when a driver checks in to a truck
  broadcast({
    type: 'driver_connected',
    title: 'Driver Checked In',
    message: `${driverData.full_name} (${driverData.employee_id}) checked in to ${truckData.truck_number}`,
    data: {
      driver: {
        id: driverData.id,
        employee_id: driverData.employee_id,
        full_name: driverData.full_name
      },
      truck: {
        id: truckData.id,
        truck_number: truckData.truck_number,
        license_plate: truckData.license_plate
      },
      shift: {
        id: shiftData.shift_id,
        start_time: shiftData.check_in_time,
        shift_type: 'custom'
      },
      action: 'check_in'
    },
    icon: '✅',
    timestamp: new Date().toISOString(),
    priority: 'medium'
  });
};

const notifyDriverDisconnected = (driverData, truckData, shiftData) => {
  // Notify when a driver checks out from a truck
  broadcast({
    type: 'driver_disconnected',
    title: 'Driver Checked Out',
    message: `${driverData.full_name} (${driverData.employee_id}) checked out from ${truckData.truck_number} - Duration: ${shiftData.duration}`,
    data: {
      driver: {
        id: driverData.id,
        employee_id: driverData.employee_id,
        full_name: driverData.full_name
      },
      truck: {
        id: truckData.id,
        truck_number: truckData.truck_number,
        license_plate: truckData.license_plate
      },
      shift: {
        id: shiftData.shift_id,
        start_time: shiftData.check_in_time,
        end_time: shiftData.check_out_time,
        duration: shiftData.duration,
        duration_ms: shiftData.duration_ms
      },
      action: 'check_out'
    },
    icon: '⏰',
    timestamp: new Date().toISOString(),
    priority: 'medium'
  });
};

const notifyDriverHandover = (previousDriverData, newDriverData, truckData, handoverData) => {
  // Notify when a driver handover occurs
  sendToRole('admin', {
    type: 'driver_handover',
    title: 'Driver Handover',
    message: `${newDriverData.full_name} took over ${truckData.truck_number} from ${previousDriverData.full_name}`,
    data: {
      previous_driver: {
        id: previousDriverData.id,
        employee_id: previousDriverData.employee_id,
        full_name: previousDriverData.full_name
      },
      new_driver: {
        id: newDriverData.id,
        employee_id: newDriverData.employee_id,
        full_name: newDriverData.full_name
      },
      truck: {
        id: truckData.id,
        truck_number: truckData.truck_number,
        license_plate: truckData.license_plate
      },
      handover: {
        previous_shift_id: handoverData.previous_shift_id,
        new_shift_id: handoverData.shift_id,
        handover_time: handoverData.check_in_time,
        previous_truck: handoverData.previous_truck,
        new_truck: handoverData.new_truck
      },
      action: 'handover'
    },
    icon: '🔄',
    timestamp: new Date().toISOString(),
    priority: 'high'
  });

  // Also send to supervisors
  sendToRole('supervisor', {
    type: 'driver_handover',
    title: 'Driver Handover',
    message: `${newDriverData.full_name} took over ${truckData.truck_number} from ${previousDriverData.full_name}`,
    data: {
      previous_driver: {
        id: previousDriverData.id,
        employee_id: previousDriverData.employee_id,
        full_name: previousDriverData.full_name
      },
      new_driver: {
        id: newDriverData.id,
        employee_id: newDriverData.employee_id,
        full_name: newDriverData.full_name
      },
      truck: {
        id: truckData.id,
        truck_number: truckData.truck_number,
        license_plate: truckData.license_plate
      },
      handover: {
        previous_shift_id: handoverData.previous_shift_id,
        new_shift_id: handoverData.shift_id,
        handover_time: handoverData.check_in_time
      },
      action: 'handover'
    },
    icon: '🔄',
    timestamp: new Date().toISOString(),
    priority: 'high'
  });
};

const notifyDriverManualCheckout = (driverData, truckData, checkoutData, supervisorData) => {
  // Notify when a supervisor manually checks out a driver
  broadcast({
    type: 'driver_manual_checkout',
    title: 'Manual Driver Checkout',
    message: `${supervisorData.username} manually checked out ${driverData.full_name} from ${truckData.truck_number}`,
    data: {
      driver: {
        id: driverData.id,
        employee_id: driverData.employee_id,
        full_name: driverData.full_name
      },
      truck: {
        id: truckData.id,
        truck_number: truckData.truck_number
      },
      checkout: {
        shift_id: checkoutData.shift_id,
        reason: checkoutData.reason,
        notes: checkoutData.notes,
        checkout_time: checkoutData.checkout_time,
        duration: checkoutData.duration
      },
      supervisor: {
        id: supervisorData.id,
        username: supervisorData.username
      },
      action: 'manual_checkout'
    },
    icon: '⚠️',
    timestamp: new Date().toISOString(),
    priority: 'high'
  });
};

const notifyRouteUpdated = (assignment, previousLocation, newLocation, locationType) => {
  // Notify when route is updated due to different location than predicted
  const isLoading = locationType === 'loading';
  const locationRole = isLoading ? 'loading' : 'unloading';

  broadcast({
    type: 'route_updated',
    title: 'Route Updated',
    message: `${assignment.assignment_code}: ${locationRole} location changed from ${previousLocation.name} to ${newLocation.name}`,
    data: {
      assignment_id: assignment.id,
      assignment_code: assignment.assignment_code,
      truck_number: assignment.truck_number,
      location_change: {
        type: locationType,
        previous: {
          id: previousLocation.id,
          name: previousLocation.name
        },
        new: {
          id: newLocation.id,
          name: newLocation.name
        }
      },
      discovery_type: 'route_correction'
    },
    timestamp: new Date().toISOString(),
    priority: 'high'
  });
};

const notifyRouteDiscoveryCompleted = (assignment, finalRoute) => {
  // Notify when route discovery is completed (both locations confirmed)
  broadcast({
    type: 'route_discovery_completed',
    title: 'Route Discovery Completed',
    message: `${assignment.assignment_code}: Complete route confirmed - ${finalRoute.loading_location} → ${finalRoute.unloading_location}`,
    data: {
      assignment_id: assignment.id,
      assignment_code: assignment.assignment_code,
      truck_number: assignment.truck_number,
      final_route: {
        loading_location: {
          id: finalRoute.loading_location_id,
          name: finalRoute.loading_location
        },
        unloading_location: {
          id: finalRoute.unloading_location_id,
          name: finalRoute.unloading_location
        }
      },
      discovery_status: 'completed'
    },
    timestamp: new Date().toISOString(),
    priority: 'medium'
  });
};

// Multi-location workflow notifications
const notifyTripExtended = (trip, newLocation) => {
  broadcast({
    type: 'trip_extended',
    title: 'Trip Extended',
    message: `${trip.truck_number}: Trip extended to ${newLocation.name}`,
    data: {
      trip_id: trip.id,
      truck_number: trip.truck_number,
      location_name: newLocation.name,
      workflow_type: 'extended',
      baseline_trip_id: trip.baseline_trip_id
    },
    timestamp: new Date().toISOString(),
    priority: 'medium'
  });
};

const notifyCycleStarted = (trip, cycleNumber) => {
  broadcast({
    type: 'cycle_started',
    title: 'Cycle Trip Started',
    message: `${trip.truck_number}: Cycle #${cycleNumber} started`,
    data: {
      trip_id: trip.id,
      truck_number: trip.truck_number,
      cycle_number: cycleNumber,
      workflow_type: 'cycle',
      baseline_trip_id: trip.baseline_trip_id
    },
    timestamp: new Date().toISOString(),
    priority: 'medium'
  });
};

const notifyDynamicRouteDiscovered = (trip, newLocation) => {
  broadcast({
    type: 'dynamic_route',
    title: 'Dynamic Route Discovered',
    message: `${trip.truck_number}: New route discovered to ${newLocation.name}`,
    data: {
      trip_id: trip.id,
      truck_number: trip.truck_number,
      location_name: newLocation.name,
      workflow_type: 'dynamic'
    },
    timestamp: new Date().toISOString(),
    priority: 'medium'
  });
};

const notifyWorkflowCompleted = (trip, workflowType) => {
  broadcast({
    type: 'workflow_completed',
    title: 'Workflow Completed',
    message: `${trip.truck_number}: ${workflowType} workflow completed`,
    data: {
      trip_id: trip.id,
      truck_number: trip.truck_number,
      workflow_type: workflowType,
      cycle_number: trip.cycle_number
    },
    timestamp: new Date().toISOString(),
    priority: 'low'
  });
};

// Get connection statistics
const getStats = () => {
  const totalClients = clients.size;
  const authenticatedClients = Array.from(clients.values()).filter(c => c.userId).length;
  const roles = {};

  clients.forEach(client => {
    if (client.role) {
      roles[client.role] = (roles[client.role] || 0) + 1;
    }
  });

  return {
    totalClients,
    authenticatedClients,
    roles,
    uptime: wss ? Date.now() - wss.startTime : 0
  };
};

// ============================================================================
// ANALYTICS & REPORTS WEBSOCKET NOTIFICATIONS
// ============================================================================

// Analytics data update notification
const notifyAnalyticsUpdate = (updateType, data) => {
  broadcast({
    type: 'analytics_update',
    title: 'Analytics Data Updated',
    message: `${updateType} analytics data has been refreshed`,
    data: {
      updateType: updateType, // 'fleet_overview', 'trip_performance', 'live_operations'
      timestamp: new Date().toISOString(),
      ...data
    },
    timestamp: new Date().toISOString(),
    priority: 'low'
  });
};

// Fleet status change notification
const notifyFleetStatusChanged = (fleetData) => {
  broadcast({
    type: 'fleet_status_changed',
    title: 'Fleet Status Updated',
    message: `Fleet status updated: ${fleetData.activeTrucks} trucks active, ${fleetData.trucksInOperation} in operation`,
    data: {
      activeTrucks: fleetData.activeTrucks,
      trucksInOperation: fleetData.trucksInOperation,
      trucksInBreakdown: fleetData.trucksInBreakdown,
      fleetUtilization: fleetData.fleetUtilization,
      lastUpdated: new Date().toISOString()
    },
    timestamp: new Date().toISOString(),
    priority: 'medium'
  });
};

// Performance alert notification
const notifyPerformanceAlert = (alertType, alertData) => {
  broadcast({
    type: 'performance_alert',
    title: 'Performance Alert',
    message: `${alertType}: ${alertData.message}`,
    data: {
      alertType: alertType, // 'overdue_trip', 'breakdown_threshold', 'efficiency_drop'
      severity: alertData.severity, // 'low', 'medium', 'high', 'critical'
      truckNumber: alertData.truckNumber,
      details: alertData.details,
      actionRequired: alertData.actionRequired || false,
      timestamp: new Date().toISOString()
    },
    timestamp: new Date().toISOString(),
    priority: alertData.severity === 'critical' ? 'high' : 'medium'
  });
};

// Live operations update notification
const notifyLiveOperationsUpdate = (operationsData) => {
  broadcast({
    type: 'live_operations_update',
    title: 'Live Operations Updated',
    message: `Operations status updated for ${operationsData.totalActive} active trucks`,
    data: {
      totalActive: operationsData.totalActive,
      byPhase: operationsData.byPhase,
      alerts: operationsData.alerts,
      dynamicAssignments: operationsData.dynamicAssignments,
      lastUpdated: new Date().toISOString()
    },
    timestamp: new Date().toISOString(),
    priority: 'low'
  });
};

// Stopped analytics update notification
const notifyStoppedAnalyticsUpdate = (stoppedData) => {
  broadcast({
    type: 'stopped_analytics_update',
    title: 'Stopped Analytics Updated',
    message: `Stopped analytics refreshed: ${stoppedData.totalStopped} total stopped trips analyzed`,
    data: {
      totalStopped: stoppedData.totalStopped,
      avgResolutionTime: stoppedData.avgResolutionTime,
      mostCommonReason: stoppedData.mostCommonReason,
      trendsUpdated: true,
      lastUpdated: new Date().toISOString()
    },
    timestamp: new Date().toISOString(),
    priority: 'low'
  });
};

// Trip performance metrics update notification
const notifyTripPerformanceUpdate = (performanceData) => {
  broadcast({
    type: 'trip_performance_update',
    title: 'Trip Performance Updated',
    message: `Performance metrics updated: ${performanceData.totalTrips} trips analyzed`,
    data: {
      totalTrips: performanceData.totalTrips,
      completionRate: performanceData.completionRate,
      avgDuration: performanceData.avgDuration,
      efficiencyTrends: performanceData.efficiencyTrends,
      lastUpdated: new Date().toISOString()
    },
    timestamp: new Date().toISOString(),
    priority: 'low'
  });
};

// Shift management notifications
const notifyShiftStatusChanged = (shiftData) => {
  broadcast({
    type: 'shift_status_changed',
    title: 'Shift Status Updated',
    message: `Shift ${shiftData.shift_type} for Truck ${shiftData.truck_id} changed from ${shiftData.previous_status} to ${shiftData.new_status}`,
    data: shiftData,
    timestamp: new Date().toISOString(),
    priority: 'medium'
  });
};

const notifyShiftDeleted = (shiftData) => {
  broadcast({
    type: 'shift_deleted',
    title: 'Shift Deleted',
    message: `Shift ${shiftData.shift_type} for Truck ${shiftData.truck_id} on ${shiftData.shift_date} has been deleted`,
    data: shiftData,
    timestamp: new Date().toISOString(),
    priority: 'medium'
  });
};

const notifyBulkShiftsCreated = (bulkData) => {
  broadcast({
    type: 'bulk_shifts_created',
    title: 'Bulk Shifts Created',
    message: `${bulkData.count} ${bulkData.shift_type} shifts created for Truck ${bulkData.truck_id}`,
    data: bulkData,
    timestamp: new Date().toISOString(),
    priority: 'medium'
  });
};

module.exports = {
  initializeWebSocket,
  broadcast,
  sendToRole,
  sendToUser,
  notifyExceptionCreated,
  notifyExceptionUpdated,
  notifyTripStatusChanged,
  notifyRouteDiscoveryStarted,
  notifyRouteLocationConfirmed,
  notifyRouteUpdated,
  notifyRouteDiscoveryCompleted,
  notifyTripExtended,
  notifyCycleStarted,
  notifyDynamicRouteDiscovered,
  notifyWorkflowCompleted,
  // Analytics & Reports WebSocket notifications
  notifyAnalyticsUpdate,
  notifyFleetStatusChanged,
  notifyPerformanceAlert,
  notifyLiveOperationsUpdate,
  notifyStoppedAnalyticsUpdate,
  notifyTripPerformanceUpdate,
  // Shift management notifications
  notifyShiftStatusChanged,
  notifyShiftDeleted,
  notifyBulkShiftsCreated,
  // Driver QR system notifications
  notifyDriverConnected,
  notifyDriverDisconnected,
  notifyDriverHandover,
  notifyDriverManualCheckout,
  getStats
};
