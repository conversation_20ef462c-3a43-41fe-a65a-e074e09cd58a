// Hauling QR Trip Management System - Offline Database Schema
// Comprehensive IndexedDB management for PWA offline functionality

const DB_NAME = 'HaulingQROffline';
const DB_VERSION = 1;

// Database schema configuration
const SCHEMA_CONFIG = {
  // Trip Scanner offline data
  scanQueue: {
    keyPath: 'id',
    autoIncrement: true,
    indexes: [
      { name: 'timestamp', keyPath: 'timestamp', unique: false },
      { name: 'status', keyPath: 'status', unique: false },
      { name: 'scanType', keyPath: 'scanType', unique: false },
      { name: 'truckId', keyPath: 'truckId', unique: false },
      { name: 'locationId', keyPath: 'locationId', unique: false },
      { name: 'priority', keyPath: 'priority', unique: false }
    ]
  },
  
  // Driver Connect offline data
  connectionQueue: {
    keyPath: 'id',
    autoIncrement: true,
    indexes: [
      { name: 'timestamp', keyPath: 'timestamp', unique: false },
      { name: 'status', keyPath: 'status', unique: false },
      { name: 'employeeId', keyPath: 'employeeId', unique: false },
      { name: 'truckId', keyPath: 'truckId', unique: false },
      { name: 'action', keyPath: 'action', unique: false },
      { name: 'priority', keyPath: 'priority', unique: false }
    ]
  },
  
  // Conflict resolution and validation data
  conflictResolution: {
    keyPath: 'id',
    autoIncrement: true,
    indexes: [
      { name: 'timestamp', keyPath: 'timestamp', unique: false },
      { name: 'type', keyPath: 'type', unique: false },
      { name: 'status', keyPath: 'status', unique: false },
      { name: 'originalId', keyPath: 'originalId', unique: false }
    ]
  },
  
  // Cached reference data for offline validation
  referenceData: {
    keyPath: 'type',
    autoIncrement: false,
    indexes: [
      { name: 'lastUpdated', keyPath: 'lastUpdated', unique: false },
      { name: 'version', keyPath: 'version', unique: false }
    ]
  },
  
  // Sync metadata and tracking
  syncMetadata: {
    keyPath: 'id',
    autoIncrement: true,
    indexes: [
      { name: 'syncType', keyPath: 'syncType', unique: false },
      { name: 'lastSync', keyPath: 'lastSync', unique: false },
      { name: 'status', keyPath: 'status', unique: false }
    ]
  }
};

// Status constants
export const SYNC_STATUS = {
  PENDING: 'pending',
  SYNCING: 'syncing',
  SYNCED: 'synced',
  FAILED: 'failed',
  CONFLICT: 'conflict'
};

export const PRIORITY = {
  LOW: 1,
  NORMAL: 2,
  HIGH: 3,
  CRITICAL: 4
};

// Database initialization and management
class OfflineDatabase {
  constructor() {
    this.db = null;
    this.isInitialized = false;
  }

  // Initialize database with schema
  async initialize() {
    if (this.isInitialized) return this.db;

    return new Promise((resolve, reject) => {
      const request = indexedDB.open(DB_NAME, DB_VERSION);

      request.onerror = () => {
        console.error('[OfflineDB] Failed to open database:', request.error);
        reject(request.error);
      };

      request.onsuccess = () => {
        this.db = request.result;
        this.isInitialized = true;
        console.log('[OfflineDB] Database initialized successfully');
        resolve(this.db);
      };

      request.onupgradeneeded = (event) => {
        const db = event.target.result;
        console.log('[OfflineDB] Upgrading database schema...');

        // Create object stores and indexes
        Object.entries(SCHEMA_CONFIG).forEach(([storeName, config]) => {
          // Delete existing store if it exists (for schema updates)
          if (db.objectStoreNames.contains(storeName)) {
            db.deleteObjectStore(storeName);
          }

          // Create object store
          const store = db.createObjectStore(storeName, {
            keyPath: config.keyPath,
            autoIncrement: config.autoIncrement
          });

          // Create indexes
          config.indexes.forEach(index => {
            store.createIndex(index.name, index.keyPath, { unique: index.unique });
          });

          console.log(`[OfflineDB] Created store: ${storeName} with ${config.indexes.length} indexes`);
        });

        console.log('[OfflineDB] Database schema upgrade completed');
      };
    });
  }

  // Generic method to add data to any store
  async addData(storeName, data) {
    await this.initialize();
    
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      
      // Add metadata
      const enrichedData = {
        ...data,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        version: 1
      };
      
      const request = store.add(enrichedData);
      
      request.onsuccess = () => {
        console.log(`[OfflineDB] Added data to ${storeName}:`, request.result);
        resolve(request.result);
      };
      
      request.onerror = () => {
        console.error(`[OfflineDB] Failed to add data to ${storeName}:`, request.error);
        reject(request.error);
      };
    });
  }

  // Generic method to get data by index
  async getDataByIndex(storeName, indexName, value) {
    await this.initialize();
    
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      const index = store.index(indexName);
      
      const request = index.getAll(value);
      
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  // Generic method to update data
  async updateData(storeName, data) {
    await this.initialize();
    
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      
      // Update metadata
      const updatedData = {
        ...data,
        updatedAt: new Date().toISOString(),
        version: (data.version || 1) + 1
      };
      
      const request = store.put(updatedData);
      
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  // Generic method to delete data
  async deleteData(storeName, id) {
    await this.initialize();
    
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      
      const request = store.delete(id);
      
      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  // Get all pending items for sync
  async getAllPending(storeName) {
    return this.getDataByIndex(storeName, 'status', SYNC_STATUS.PENDING);
  }

  // Get database statistics
  async getStats() {
    await this.initialize();
    
    const stats = {};
    
    for (const storeName of Object.keys(SCHEMA_CONFIG)) {
      try {
        const transaction = this.db.transaction([storeName], 'readonly');
        const store = transaction.objectStore(storeName);
        
        const countRequest = store.count();
        const count = await new Promise((resolve, reject) => {
          countRequest.onsuccess = () => resolve(countRequest.result);
          countRequest.onerror = () => reject(countRequest.error);
        });
        
        const pendingCount = (await this.getAllPending(storeName)).length;
        
        stats[storeName] = {
          total: count,
          pending: pendingCount,
          synced: count - pendingCount
        };
      } catch (error) {
        console.error(`[OfflineDB] Failed to get stats for ${storeName}:`, error);
        stats[storeName] = { total: 0, pending: 0, synced: 0, error: error.message };
      }
    }
    
    return stats;
  }

  // Clear all data (for testing/reset)
  async clearAllData() {
    await this.initialize();
    
    const storeNames = Object.keys(SCHEMA_CONFIG);
    
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction(storeNames, 'readwrite');
      
      let completed = 0;
      const total = storeNames.length;
      
      storeNames.forEach(storeName => {
        const store = transaction.objectStore(storeName);
        const request = store.clear();
        
        request.onsuccess = () => {
          completed++;
          if (completed === total) {
            console.log('[OfflineDB] All data cleared successfully');
            resolve();
          }
        };
        
        request.onerror = () => reject(request.error);
      });
    });
  }
}

// Create singleton instance
export const offlineDB = new OfflineDatabase();

// Conflict Resolution Service
export class ConflictResolutionService {
  constructor() {
    this.storeName = 'conflictResolution';
  }

  // Detect and log conflicts during sync
  async detectConflict(originalData, serverData, conflictType) {
    const conflict = {
      type: conflictType, // 'timestamp', 'data_mismatch', 'duplicate', 'validation'
      status: 'pending',
      originalData,
      serverData,
      timestamp: new Date().toISOString(),
      resolution: null,
      resolutionStrategy: 'manual', // 'manual', 'server_wins', 'client_wins', 'merge'
      metadata: {
        detectedBy: 'offline_sync',
        severity: this.calculateSeverity(conflictType),
        autoResolvable: this.isAutoResolvable(conflictType)
      }
    };

    const id = await offlineDB.addData(this.storeName, conflict);
    console.log(`[ConflictResolution] Conflict detected and logged: ${id}`);

    return { id, conflict };
  }

  // Calculate conflict severity
  calculateSeverity(conflictType) {
    const severityMap = {
      'timestamp': 'low',
      'data_mismatch': 'medium',
      'duplicate': 'medium',
      'validation': 'high'
    };
    return severityMap[conflictType] || 'medium';
  }

  // Check if conflict can be auto-resolved
  isAutoResolvable(conflictType) {
    const autoResolvable = ['timestamp', 'duplicate'];
    return autoResolvable.includes(conflictType);
  }

  // Auto-resolve conflicts where possible
  async autoResolveConflict(conflictId) {
    try {
      const transaction = offlineDB.db.transaction([this.storeName], 'readonly');
      const store = transaction.objectStore(this.storeName);
      const conflict = await new Promise((resolve, reject) => {
        const request = store.get(conflictId);
        request.onsuccess = () => resolve(request.result);
        request.onerror = () => reject(request.error);
      });

      if (!conflict || !conflict.metadata.autoResolvable) {
        return null;
      }

      let resolution = null;

      switch (conflict.type) {
        case 'timestamp':
          // Server timestamp wins for consistency
          resolution = {
            strategy: 'server_wins',
            resolvedData: conflict.serverData,
            reason: 'Server timestamp is authoritative'
          };
          break;

        case 'duplicate':
          // Skip duplicate, keep server version
          resolution = {
            strategy: 'server_wins',
            resolvedData: conflict.serverData,
            reason: 'Duplicate entry, server version retained'
          };
          break;

        default:
          // Cannot auto-resolve unknown conflict types
          console.log(`[ConflictResolution] Cannot auto-resolve conflict type: ${conflict.type}`);
          return null;
      }

      if (resolution) {
        await this.markConflictResolved(conflictId, resolution);
        return resolution;
      }

      return null;
    } catch (error) {
      console.error('[ConflictResolution] Auto-resolve failed:', error);
      return null;
    }
  }

  // Mark conflict as resolved
  async markConflictResolved(conflictId, resolution) {
    try {
      const transaction = offlineDB.db.transaction([this.storeName], 'readonly');
      const store = transaction.objectStore(this.storeName);
      const conflict = await new Promise((resolve, reject) => {
        const request = store.get(conflictId);
        request.onsuccess = () => resolve(request.result);
        request.onerror = () => reject(request.error);
      });

      const resolvedConflict = {
        ...conflict,
        status: 'resolved',
        resolution,
        resolvedAt: new Date().toISOString()
      };

      await offlineDB.updateData(this.storeName, resolvedConflict);
      console.log(`[ConflictResolution] Conflict ${conflictId} resolved`);
    } catch (error) {
      console.error('[ConflictResolution] Failed to mark conflict resolved:', error);
      throw error;
    }
  }

  // Get pending conflicts for manual resolution
  async getPendingConflicts() {
    return offlineDB.getDataByIndex(this.storeName, 'status', 'pending');
  }
}

// Reference Data Cache Service
export class ReferenceDataService {
  constructor() {
    this.storeName = 'referenceData';
  }

  // Cache reference data for offline validation
  async cacheReferenceData(type, data, version = 1) {
    const referenceEntry = {
      type, // 'locations', 'trucks', 'drivers', 'assignments'
      data,
      version,
      lastUpdated: new Date().toISOString(),
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours
    };

    await offlineDB.updateData(this.storeName, referenceEntry);
    console.log(`[ReferenceData] Cached ${type} data, version ${version}`);
  }

  // Get cached reference data
  async getReferenceData(type) {
    try {
      const transaction = offlineDB.db.transaction([this.storeName], 'readonly');
      const store = transaction.objectStore(this.storeName);
      const entry = await new Promise((resolve, reject) => {
        const request = store.get(type);
        request.onsuccess = () => resolve(request.result);
        request.onerror = () => reject(request.error);
      });

      if (!entry) return null;

      // Check if data has expired
      if (new Date() > new Date(entry.expiresAt)) {
        console.log(`[ReferenceData] ${type} data expired, needs refresh`);
        return null;
      }

      return entry.data;
    } catch (error) {
      console.error(`[ReferenceData] Failed to get ${type} data:`, error);
      return null;
    }
  }
}

// Create service instances
export const conflictResolution = new ConflictResolutionService();
export const referenceData = new ReferenceDataService();

// Export database instance and utilities
export default offlineDB;
