import { useState, useEffect, useCallback } from 'react';
import { backgroundSync } from '../services/backgroundSync';
import { tripScannerOffline } from '../services/tripScannerOffline';
import { driverConnectOffline } from '../services/driverConnectOffline';

/**
 * Custom hook for PWA status monitoring and sync management
 * Provides real-time status updates for offline functionality
 */
export const usePWAStatus = () => {
  // Network and sync states
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [syncStatus, setSyncStatus] = useState('synced'); // 'synced', 'pending', 'syncing', 'error'
  const [queuedScans, setQueuedScans] = useState(0);
  const [queuedConnections, setQueuedConnections] = useState(0);
  const [lastSyncTime, setLastSyncTime] = useState(null);
  const [syncError, setSyncError] = useState(null);

  // PWA installation state
  const [installPrompt, setInstallPrompt] = useState(null);
  const [isInstalled, setIsInstalled] = useState(false);



  // Trigger manual sync
  const triggerSync = useCallback(async () => {
    if (!navigator.onLine) {
      console.log('Cannot sync while offline');
      return { success: false, message: 'Cannot sync while offline' };
    }

    try {
      setSyncStatus('syncing');
      setSyncError(null);

      const results = await backgroundSync.syncAll();

      if (results.success) {
        setSyncStatus('synced');
        setLastSyncTime(new Date().toISOString());

        // Update queue counts directly after sync
        try {
          const [scanCount, connectionCount] = await Promise.all([
            tripScannerOffline.getPendingCount(),
            driverConnectOffline.getPendingCount()
          ]);
          setQueuedScans(scanCount);
          setQueuedConnections(connectionCount);
        } catch (countError) {
          console.error('Failed to update queue counts after sync:', countError);
        }

        return {
          success: true,
          message: `Synced ${results.totalSynced} items successfully`,
          results
        };
      } else {
        setSyncStatus('error');
        setSyncError(results.error || 'Sync failed');

        return {
          success: false,
          message: results.error || 'Sync failed',
          results
        };
      }
    } catch (error) {
      console.error('Manual sync failed:', error);
      setSyncStatus('error');
      setSyncError(error.message);

      return {
        success: false,
        message: error.message,
        error
      };
    }
  }, []); // Remove updateQueueCounts dependency to prevent infinite loop

  // Handle PWA installation
  const installPWA = useCallback(async () => {
    if (!installPrompt) {
      return { success: false, message: 'Installation not available' };
    }

    try {
      await installPrompt.prompt();
      const choiceResult = await installPrompt.userChoice;

      if (choiceResult.outcome === 'accepted') {
        setIsInstalled(true);
        setInstallPrompt(null);
        return { success: true, message: 'PWA installed successfully' };
      } else {
        return { success: false, message: 'Installation cancelled by user' };
      }
    } catch (error) {
      console.error('PWA installation failed:', error);
      return { success: false, message: error.message };
    }
  }, [installPrompt]);

  // Network status monitoring
  useEffect(() => {
    const handleOnline = async () => {
      setIsOnline(true);
      setSyncError(null);

      // Auto-trigger sync when coming back online
      // Check queue counts and trigger sync directly to avoid circular dependencies
      try {
        const [scanCount, connectionCount] = await Promise.all([
          tripScannerOffline.getPendingCount(),
          driverConnectOffline.getPendingCount()
        ]);

        if (scanCount > 0 || connectionCount > 0) {
          // Trigger sync directly without using the triggerSync function to avoid circular dependencies
          setTimeout(async () => {
            try {
              setSyncStatus('syncing');
              const results = await backgroundSync.syncAll();

              if (results.success) {
                setSyncStatus('synced');
                setLastSyncTime(new Date().toISOString());

                // Update queue counts directly after auto-sync
                try {
                  const [scanCount, connectionCount] = await Promise.all([
                    tripScannerOffline.getPendingCount(),
                    driverConnectOffline.getPendingCount()
                  ]);
                  setQueuedScans(scanCount);
                  setQueuedConnections(connectionCount);
                } catch (countError) {
                  console.error('Failed to update queue counts after auto-sync:', countError);
                }
              } else {
                setSyncStatus('error');
                setSyncError(results.error || 'Auto-sync failed');
              }
            } catch (error) {
              console.error('Auto-sync failed:', error);
              setSyncStatus('error');
              setSyncError(error.message);
            }
          }, 1000); // Small delay to ensure connection is stable
        }
      } catch (error) {
        console.error('Failed to check queue counts on reconnect:', error);
      }
    };

    const handleOffline = () => {
      setIsOnline(false);
      setSyncStatus('pending');
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []); // Remove updateQueueCounts dependency to prevent infinite loop

  // PWA installation prompt handling
  useEffect(() => {
    const handleBeforeInstallPrompt = (e) => {
      e.preventDefault();
      setInstallPrompt(e);
    };

    const handleAppInstalled = () => {
      setIsInstalled(true);
      setInstallPrompt(null);
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', handleAppInstalled);

    // Check if already installed
    if (window.matchMedia && window.matchMedia('(display-mode: standalone)').matches) {
      setIsInstalled(true);
    }

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
    };
  }, []);

  // Periodic queue count updates - only when offline or when there might be pending items
  useEffect(() => {
    const updateCounts = async () => {
      // Only check queue counts when offline or when we might have pending items
      if (!isOnline || syncStatus === 'pending' || syncStatus === 'syncing') {
        try {
          const [scanCount, connectionCount] = await Promise.all([
            tripScannerOffline.getPendingCount(),
            driverConnectOffline.getPendingCount()
          ]);

          setQueuedScans(scanCount);
          setQueuedConnections(connectionCount);

          // Update overall sync status based on queue counts only
          const totalQueued = scanCount + connectionCount;
          setSyncStatus(prevStatus => {
            if (totalQueued > 0 && prevStatus === 'synced') {
              return 'pending';
            } else if (totalQueued === 0 && prevStatus === 'pending') {
              return 'synced';
            }
            return prevStatus;
          });
        } catch (error) {
          console.error('Failed to update queue counts:', error);
        }
      } else {
        // When online and synced, ensure queue counts are zero
        setQueuedScans(0);
        setQueuedConnections(0);
      }
    };

    // Initial update
    updateCounts();

    // Set up interval for periodic updates
    const interval = setInterval(updateCounts, 5000); // Update every 5 seconds

    return () => clearInterval(interval);
  }, [isOnline, syncStatus]); // Include isOnline and syncStatus as dependencies

  // Service worker sync event handling
  useEffect(() => {
    if ('serviceWorker' in navigator) {
      const handleServiceWorkerMessage = (event) => {
        if (event.data && event.data.type === 'SYNC_STATUS_UPDATE') {
          const { status, queuedScans: scans, queuedConnections: connections } = event.data;

          setSyncStatus(status);
          setQueuedScans(scans || 0);
          setQueuedConnections(connections || 0);

          if (status === 'synced') {
            setLastSyncTime(new Date().toISOString());
            setSyncError(null);
          } else if (status === 'error') {
            setSyncError(event.data.error || 'Sync failed');
          }
        } else if (event.data && event.data.type === 'TRIGGER_SYNC') {
          // Service worker is requesting a sync - trigger it immediately
          console.log('[PWAStatus] Service worker requested sync:', event.data.syncType);
          triggerSync();
        }
      };

      navigator.serviceWorker.addEventListener('message', handleServiceWorkerMessage);

      // Cleanup listener on unmount
      return () => {
        navigator.serviceWorker.removeEventListener('message', handleServiceWorkerMessage);
      };
    }
  }, [triggerSync]);

  return {
    // Network status
    isOnline,
    
    // Sync status
    syncStatus,
    queuedScans,
    queuedConnections,
    lastSyncTime,
    syncError,
    
    // PWA installation
    installPrompt,
    isInstalled,
    
    // Actions
    triggerSync,
    installPWA,
    
    // Computed values
    totalQueued: queuedScans + queuedConnections,
    canSync: isOnline && (queuedScans > 0 || queuedConnections > 0),
    canInstall: !!installPrompt && !isInstalled
  };
};

export default usePWAStatus;
