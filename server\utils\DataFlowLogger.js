/**
 * Data Flow Logger
 * 
 * Provides specialized logging for tracking data flow between:
 * - Shift Management
 * - Assignment Management  
 * - Trip Monitoring
 * 
 * This logger helps debug issues in the complete data flow chain.
 */

const { logInfo, logError, logDebug } = require('./logger');

class DataFlowLogger {
  /**
   * Log shift management events that affect the data flow
   * @param {string} event Event type
   * @param {string} message Log message
   * @param {Object} data Event data
   */
  static logShiftEvent(event, message, data = {}) {
    logInfo(`SHIFT_MANAGEMENT.${event}`, message, {
      system: 'shift_management',
      event_type: event,
      timestamp: new Date().toISOString(),
      ...data
    });
  }

  /**
   * Log assignment management events that affect the data flow
   * @param {string} event Event type
   * @param {string} message Log message
   * @param {Object} data Event data
   */
  static logAssignmentEvent(event, message, data = {}) {
    logInfo(`ASSIGNMENT_MANAGEMENT.${event}`, message, {
      system: 'assignment_management',
      event_type: event,
      timestamp: new Date().toISOString(),
      ...data
    });
  }

  /**
   * Log trip monitoring events that affect the data flow
   * @param {string} event Event type
   * @param {string} message Log message
   * @param {Object} data Event data
   */
  static logTripEvent(event, message, data = {}) {
    logInfo(`TRIP_MONITORING.${event}`, message, {
      system: 'trip_monitoring',
      event_type: event,
      timestamp: new Date().toISOString(),
      ...data
    });
  }

  /**
   * Log data flow synchronization events
   * @param {string} fromSystem Source system
   * @param {string} toSystem Target system
   * @param {string} message Log message
   * @param {Object} data Sync data
   */
  static logDataFlowSync(fromSystem, toSystem, message, data = {}) {
    logInfo(`DATA_FLOW_SYNC.${fromSystem.toUpperCase()}_TO_${toSystem.toUpperCase()}`, message, {
      from_system: fromSystem,
      to_system: toSystem,
      sync_type: 'data_flow',
      timestamp: new Date().toISOString(),
      ...data
    });
  }

  /**
   * Log driver QR check-in events and their impact on the data flow
   * @param {string} action Check-in or check-out
   * @param {Object} driverData Driver information
   * @param {Object} shiftData Shift information
   * @param {Object} truckData Truck information
   */
  static logDriverQREvent(action, driverData, shiftData, truckData) {
    const eventData = {
      action,
      driver_id: driverData.id,
      employee_id: driverData.employee_id,
      driver_name: driverData.full_name,
      truck_id: truckData.id,
      truck_number: truckData.truck_number,
      shift_id: shiftData.id,
      shift_type: shiftData.shift_type,
      shift_status: shiftData.status,
      auto_created: shiftData.auto_created,
      timestamp: new Date().toISOString()
    };

    this.logShiftEvent('DRIVER_QR_ACTION', `Driver ${action}: ${driverData.employee_id} - ${truckData.truck_number}`, eventData);

    // Log the expected impact on assignment management
    this.logAssignmentEvent('EXPECTED_SYNC', `Assignment should sync with ${action} for ${truckData.truck_number}`, {
      expected_driver_id: action === 'check_in' ? driverData.id : null,
      expected_driver_name: action === 'check_in' ? driverData.full_name : null,
      truck_id: truckData.id,
      truck_number: truckData.truck_number,
      trigger_event: 'driver_qr_action',
      ...eventData
    });
  }

  /**
   * Log assignment updates and their impact on trip monitoring
   * @param {Object} assignmentData Assignment information
   * @param {Object} driverData Driver information
   * @param {string} updateReason Reason for the update
   */
  static logAssignmentUpdate(assignmentData, driverData, updateReason) {
    const eventData = {
      assignment_id: assignmentData.id,
      assignment_code: assignmentData.assignment_code,
      truck_id: assignmentData.truck_id,
      previous_driver_id: assignmentData.previous_driver_id,
      new_driver_id: driverData?.id,
      new_driver_name: driverData?.full_name,
      update_reason: updateReason,
      timestamp: new Date().toISOString()
    };

    this.logAssignmentEvent('DRIVER_UPDATE', `Assignment driver updated: ${assignmentData.assignment_code}`, eventData);

    // Log the expected impact on trip monitoring
    this.logTripEvent('EXPECTED_DRIVER_CHANGE', `Trip monitoring should reflect new driver for assignment ${assignmentData.assignment_code}`, {
      assignment_id: assignmentData.id,
      expected_driver_id: driverData?.id,
      expected_driver_name: driverData?.full_name,
      trigger_event: 'assignment_update',
      ...eventData
    });
  }

  /**
   * Log trip creation and driver capture results
   * @param {Object} tripData Trip information
   * @param {Object} capturedDriverData Captured driver information
   * @param {Object} assignmentData Assignment information
   * @param {boolean} captureSuccess Whether driver capture was successful
   */
  static logTripDriverCapture(tripData, capturedDriverData, assignmentData, captureSuccess) {
    const eventData = {
      trip_id: tripData.id,
      assignment_id: assignmentData.id,
      truck_id: assignmentData.truck_id,
      capture_success: captureSuccess,
      captured_driver_id: capturedDriverData?.driver_id,
      captured_driver_name: capturedDriverData?.driver_name,
      captured_employee_id: capturedDriverData?.employee_id,
      captured_shift_id: capturedDriverData?.shift_id,
      captured_shift_type: capturedDriverData?.shift_type,
      assignment_driver_id: assignmentData.driver_id,
      timestamp: new Date().toISOString()
    };

    if (captureSuccess) {
      this.logTripEvent('DRIVER_CAPTURE_SUCCESS', `Driver captured for trip ${tripData.id}: ${capturedDriverData.driver_name}`, eventData);
      
      // Validate data flow consistency
      if (capturedDriverData.driver_id === assignmentData.driver_id) {
        this.logDataFlowSync('assignment_management', 'trip_monitoring', 'Driver data consistent across systems', {
          consistency_check: 'passed',
          driver_id: capturedDriverData.driver_id,
          ...eventData
        });
      } else {
        this.logDataFlowSync('assignment_management', 'trip_monitoring', 'Driver data inconsistency detected', {
          consistency_check: 'failed',
          expected_driver_id: assignmentData.driver_id,
          actual_driver_id: capturedDriverData.driver_id,
          ...eventData
        });
      }
    } else {
      this.logTripEvent('DRIVER_CAPTURE_FAILURE', `Failed to capture driver for trip ${tripData.id}`, eventData);
      
      this.logDataFlowSync('assignment_management', 'trip_monitoring', 'Data flow broken - no driver captured', {
        consistency_check: 'broken',
        assignment_has_driver: assignmentData.driver_id !== null,
        ...eventData
      });
    }
  }

  /**
   * Log data flow validation results
   * @param {Object} validationResults Results from DataFlowValidationService
   */
  static logDataFlowValidation(validationResults) {
    const summary = {
      overall_status: validationResults.overall_status,
      total_issues: validationResults.issues.length,
      critical_issues: validationResults.issues.filter(i => i.severity === 'critical').length,
      warning_issues: validationResults.issues.filter(i => i.severity === 'warning').length,
      validation_timestamp: validationResults.timestamp
    };

    if (validationResults.overall_status === 'operational') {
      logInfo('DATA_FLOW_VALIDATION.PASSED', 'Data flow validation passed - all systems synchronized', summary);
    } else if (validationResults.overall_status === 'warning') {
      logInfo('DATA_FLOW_VALIDATION.WARNING', 'Data flow validation found minor issues', summary);
    } else {
      logError('DATA_FLOW_VALIDATION.CRITICAL', 'Data flow validation found critical issues', summary);
    }

    // Log detailed issues
    validationResults.issues.forEach((issue, index) => {
      const issueData = {
        issue_id: issue.id,
        issue_type: issue.type,
        severity: issue.severity,
        description: issue.description,
        affected_records_count: issue.affected_records.length,
        auto_fixable: issue.auto_fixable,
        fix_suggestion: issue.fix_suggestion
      };

      if (issue.severity === 'critical') {
        logError(`DATA_FLOW_VALIDATION.ISSUE_${index + 1}`, issue.description, issueData);
      } else {
        logInfo(`DATA_FLOW_VALIDATION.ISSUE_${index + 1}`, issue.description, issueData);
      }
    });
  }

  /**
   * Log end-to-end data flow test results
   * @param {Object} testData Test scenario data
   * @param {Object} results Test results
   */
  static logEndToEndTest(testData, results) {
    const eventData = {
      test_scenario: testData.scenario,
      driver_id: testData.driver_id,
      truck_id: testData.truck_id,
      test_timestamp: testData.timestamp,
      results: {
        shift_created: results.shift_created,
        assignment_updated: results.assignment_updated,
        trip_driver_captured: results.trip_driver_captured,
        data_consistency: results.data_consistency
      },
      success: results.overall_success,
      duration_ms: results.duration_ms
    };

    if (results.overall_success) {
      logInfo('END_TO_END_TEST.SUCCESS', `End-to-end test passed: ${testData.scenario}`, eventData);
    } else {
      logError('END_TO_END_TEST.FAILURE', `End-to-end test failed: ${testData.scenario}`, eventData);
    }

    // Log each step result
    if (results.shift_created) {
      this.logShiftEvent('TEST_SHIFT_CREATED', 'Test shift created successfully', eventData);
    }
    
    if (results.assignment_updated) {
      this.logAssignmentEvent('TEST_ASSIGNMENT_UPDATED', 'Test assignment updated successfully', eventData);
    }
    
    if (results.trip_driver_captured) {
      this.logTripEvent('TEST_DRIVER_CAPTURED', 'Test trip driver captured successfully', eventData);
    }
  }

  /**
   * Log status synchronization monitoring results
   * @param {Object} syncResults Synchronization monitoring results
   */
  static logStatusSyncMonitoring(syncResults) {
    const eventData = {
      shifts_checked: syncResults.metrics?.shifts_checked || 0,
      assignments_checked: syncResults.metrics?.assignments_checked || 0,
      trips_checked: syncResults.metrics?.trips_checked || 0,
      sync_issues_found: syncResults.sync_issues?.length || 0,
      auto_fixes_applied: syncResults.auto_fixes_applied || 0,
      monitoring_duration_ms: syncResults.monitoring_duration_ms || 0,
      overall_status: syncResults.overall_status,
      correlation_id: syncResults.correlation_id,
      timestamp: new Date().toISOString()
    };

    if (syncResults.sync_issues?.length === 0) {
      logInfo('STATUS_SYNC_MONITORING.CLEAN', 'Status synchronization monitoring - no issues found', eventData);
    } else {
      logInfo('STATUS_SYNC_MONITORING.ISSUES_FOUND', `Status synchronization monitoring found ${syncResults.sync_issues?.length || 0} issues`, eventData);
      
      // Log each sync issue
      syncResults.sync_issues?.forEach((issue, index) => {
        const logLevel = issue.severity === 'critical' ? logError : logInfo;
        logLevel(`STATUS_SYNC_MONITORING.ISSUE_${index + 1}`, issue.description, {
          issue_type: issue.type,
          severity: issue.severity,
          affected_system: issue.system,
          auto_fixed: issue.auto_fixed,
          affected_records: issue.affected_records?.length || 0,
          ...eventData
        });
      });
    }
  }

  /**
   * Log status synchronization alert events
   * @param {string} action Alert action (created, resolved, updated)
   * @param {Object} alert Alert information
   * @param {Object} context Additional context
   */
  static logStatusSyncAlert(action, alert, context = {}) {
    const eventData = {
      alert_id: alert.id,
      alert_type: alert.type,
      priority: alert.priority,
      title: alert.title,
      affected_systems: alert.affected_systems,
      action: action,
      correlation_id: context.correlation_id,
      timestamp: new Date().toISOString(),
      ...context
    };

    const logLevel = alert.priority === 'high' ? logError : logInfo;
    logLevel(`STATUS_SYNC_ALERT.${action.toUpperCase()}`, `Alert ${action}: ${alert.title}`, eventData);
  }

  /**
   * Log monitoring service lifecycle events
   * @param {string} event Event type (started, stopped, error, recovery)
   * @param {string} message Log message
   * @param {Object} data Event data
   */
  static logMonitoringService(event, message, data = {}) {
    logInfo(`MONITORING_SERVICE.${event.toUpperCase()}`, message, {
      service: 'status_synchronization_monitor',
      event_type: event,
      timestamp: new Date().toISOString(),
      ...data
    });
  }

  /**
   * Create a correlation ID for tracking related events across systems
   * @param {string} prefix Prefix for the correlation ID
   * @returns {string} Correlation ID
   */
  static createCorrelationId(prefix = 'dataflow') {
    return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Log with correlation ID for tracking related events
   * @param {string} correlationId Correlation ID
   * @param {string} system System name
   * @param {string} event Event type
   * @param {string} message Log message
   * @param {Object} data Event data
   */
  static logWithCorrelation(correlationId, system, event, message, data = {}) {
    logInfo(`${system.toUpperCase()}.${event}`, message, {
      correlation_id: correlationId,
      system,
      event_type: event,
      timestamp: new Date().toISOString(),
      ...data
    });
  }
}

module.exports = DataFlowLogger;