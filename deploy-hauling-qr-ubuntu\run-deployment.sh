#!/bin/bash
#
# Complete Deployment Runner Script
# This script handles all the setup and runs the deployment
#

echo "Hauling QR Trip Management System - Deployment Runner"
echo "====================================================="
echo

# Show current directory and available files
echo "Current directory: $(pwd)"
echo "Available deployment files:"
ls -la *.sh *.conf 2>/dev/null || echo "No deployment files found in current directory"
echo

# Step 1: Fix deployment configuration file line endings
echo "Step 1: Fixing deployment configuration file line endings..."
if [[ -f "deployment-config.conf" ]]; then
    # Remove Windows line endings
    sed -i 's/\r$//' deployment-config.conf 2>/dev/null || true
    chmod 600 deployment-config.conf
    echo "✅ Deployment configuration file fixed"
else
    echo "❌ deployment-config.conf not found"
    exit 1
fi

# Step 2: Test GitHub access
echo
echo "Step 2: Testing GitHub repository access..."
TOKEN="*********************************************************************************************"
REPO="mightybadz18/hauling-qr-trip-management"

if curl -s -H "Authorization: token $TOKEN" \
   "https://api.github.com/repos/$REPO" | grep -q '"name": "hauling-qr-trip-management"'; then
    echo "✅ GitHub repository access confirmed"
else
    echo "❌ GitHub repository access failed"
    echo "Please check your Personal Access Token"
    exit 1
fi

# Step 3: Fix script permissions and verify files
echo
echo "Step 3: Fixing script permissions and verifying files..."
echo "Fix all script permissions..."
chmod +x *.sh 2>/dev/null || true

# Verify main deployment script exists and is executable
if [[ -f "deploy-hauling-qr-ubuntu-fixed.sh" ]]; then
    if [[ -x "deploy-hauling-qr-ubuntu-fixed.sh" ]]; then
        echo "✅ Main deployment script is ready and executable"
        ls -la deploy-hauling-qr-ubuntu-fixed.sh
    else
        echo "❌ Main deployment script is not executable"
        chmod +x deploy-hauling-qr-ubuntu-fixed.sh
        echo "✅ Fixed permissions for main deployment script"
    fi
else
    echo "❌ deploy-hauling-qr-ubuntu-fixed.sh not found"
    echo "Available files:"
    ls -la *.sh 2>/dev/null || echo "No shell scripts found"
    exit 1
fi

# Step 4: Check if running as root
echo
echo "Step 4: Checking root permissions..."
if [[ $EUID -ne 0 ]]; then
    echo "❌ This script must be run as root (use sudo)"
    echo "Run: sudo ./run-deployment.sh"
    echo ""
    echo "IMPORTANT: You need root privileges to:"
    echo "- Install system packages (nginx, postgresql, nodejs, npm)"
    echo "- Create system users and directories"
    echo "- Configure system services"
    echo "- Set up SSL certificates"
    exit 1
else
    echo "✅ Running as root"
fi

# Step 5: Run deployment
echo
echo "Step 5: Choose deployment mode:"
echo "1. Dry run (test configuration only)"
echo "2. Full deployment"
echo "3. Exit"
echo

read -p "Enter your choice (1-3): " choice

case $choice in
    1)
        echo
        echo "Running dry run deployment..."
        echo "=============================="
        if ./deploy-hauling-qr-ubuntu-fixed.sh --config deployment-config.conf --dry-run; then
            echo "✅ Dry run completed successfully"
            DEPLOYMENT_SUCCESS=true
        else
            echo "❌ Dry run failed"
            DEPLOYMENT_SUCCESS=false
        fi
        ;;
    2)
        echo
        echo "⚠️  WARNING: This will perform a full deployment to your system!"
        echo "This will install packages, modify system configuration, and set up the application."
        echo
        read -p "Are you sure you want to continue? (yes/no): " confirm
        
        if [[ "$confirm" == "yes" ]]; then
            echo
            echo "Running full deployment..."
            echo "=========================="
            if ./deploy-hauling-qr-ubuntu-fixed.sh --config deployment-config.conf; then
                echo "✅ Full deployment completed successfully"
                DEPLOYMENT_SUCCESS=true
            else
                echo "❌ Full deployment failed"
                DEPLOYMENT_SUCCESS=false
            fi
        else
            echo "Deployment cancelled."
            exit 0
        fi
        ;;
    3)
        echo "Exiting..."
        exit 0
        ;;
    *)
        echo "Invalid choice. Exiting..."
        exit 1
        ;;
esac

echo
echo "Deployment runner completed!"

# Show results and next steps
if [[ "$DEPLOYMENT_SUCCESS" == "true" && $choice -eq 2 ]]; then
    echo
    echo "🎉 Deployment completed!"
    echo
    echo "=== ACCESS INFORMATION ==="
    echo "1. Visit https://truckhaul.top to access your application"
    echo "2. Login with admin credentials:"
    echo "   - Username: admin"
    echo "   - Password: admin12345"
    echo "3. Change the default password after first login"
    echo "4. Configure your Cloudflare SSL settings"
    echo
    echo "=== FILE LOCATIONS ==="
    echo "Application directory: /var/www/hauling-qr-system"
    echo "- Server code: /var/www/hauling-qr-system/server"
    echo "- Client code: /var/www/hauling-qr-system/client"
    echo "- Database scripts: /var/www/hauling-qr-system/database"
    echo "- Configuration: /var/www/hauling-qr-system/.env"
    echo
    echo "=== LOGS ==="
    echo "Deployment log: /var/log/hauling-deployment/deployment.log"
    echo "Application logs: /var/www/hauling-qr-system/server/logs/"
    echo
    echo "=== MANAGEMENT COMMANDS ==="
    echo "Check application status: pm2 list"
    echo "View application logs: pm2 logs hauling-qr-server"
    echo "Restart application: pm2 restart hauling-qr-server"
elif [[ "$DEPLOYMENT_SUCCESS" == "false" ]]; then
    echo
    echo "❌ Deployment failed!"
    echo
    echo "=== TROUBLESHOOTING ==="
    echo "1. Check deployment logs:"
    echo "   tail -f /var/log/hauling-deployment/deployment.log"
    echo
    echo "2. Check script permissions:"
    echo "   ls -la deploy-hauling-qr-ubuntu-fixed.sh"
    echo
    echo "3. Try running with debug logging:"
    echo "   sudo ./deploy-hauling-qr-ubuntu-fixed.sh --config deployment-config.conf --log-level debug"
    echo
    echo "4. Check system requirements:"
    echo "   - Ubuntu 18.04+ required"
    echo "   - Internet connectivity needed"
    echo "   - At least 512MB RAM available"
    echo "   - At least 2GB disk space available"
    echo
    echo "5. Common fixes:"
    echo "   - Run 'apt update' if package installation fails"
    echo "   - Check GitHub token permissions if repository clone fails"
    echo "   - Ensure running with sudo/root privileges"
elif [[ "$DEPLOYMENT_SUCCESS" == "true" && $choice -eq 1 ]]; then
    echo
    echo "✅ Dry run successful!"
    echo
    echo "The configuration is valid and ready for deployment."
    echo "To run the full deployment, run this script again and choose option 2."
fi