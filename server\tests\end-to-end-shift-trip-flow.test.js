const { query, getClient } = require('../config/database');
const request = require('supertest');
const app = require('../server');

describe('End-to-End Shift-Trip Flow Integration Tests', () => {
  let client;
  let testTruckId;
  let testDriverId;
  let testLocationId;
  let testAssignmentId;

  beforeAll(async () => {
    client = await getClient();
    
    // Create test truck
    const truckResult = await client.query(`
      INSERT INTO dump_trucks (truck_number, license_plate, status, qr_code_data)
      VALUES ('E2E001', 'E2E-001', 'active', '{"type": "truck", "id": "E2E001"}')
      RETURNING id
    `);
    testTruckId = truckResult.rows[0].id;

    // Create test driver
    const driverResult = await client.query(`
      INSERT INTO drivers (full_name, employee_id, status, qr_code_data)
      VALUES ('E2E Test Driver', 'E2E001', 'active', '{"type": "driver", "id": "E2E001"}')
      RETURNING id
    `);
    testDriverId = driverResult.rows[0].id;

    // Create test locations
    const locationResult = await client.query(`
      INSERT INTO locations (location_code, name, type, status, qr_code_data)
      VALUES ('E2ELOC1', 'E2E Test Location', 'loading', 'active', '{"type": "location", "id": "E2ELOC1"}')
      RETURNING id
    `);
    testLocationId = locationResult.rows[0].id;

    // Create test assignment
    const assignmentResult = await client.query(`
      INSERT INTO assignments (
        assignment_code, truck_id, loading_location_id, unloading_location_id,
        loading_location_name, unloading_location_name, status
      ) VALUES ('E2EASG1', $1, $2, $2, 'E2E Loading', 'E2E Unloading', 'active')
      RETURNING id
    `, [testTruckId, testLocationId]);
    testAssignmentId = assignmentResult.rows[0].id;
  });

  afterAll(async () => {
    // Clean up test data
    await client.query('DELETE FROM trip_logs WHERE assignment_id = $1', [testAssignmentId]);
    await client.query('DELETE FROM driver_shifts WHERE driver_id = $1', [testDriverId]);
    await client.query('DELETE FROM assignments WHERE id = $1', [testAssignmentId]);
    await client.query('DELETE FROM locations WHERE id = $1', [testLocationId]);
    await client.query('DELETE FROM drivers WHERE id = $1', [testDriverId]);
    await client.query('DELETE FROM dump_trucks WHERE id = $1', [testTruckId]);
    
    client.release();
  });

  beforeEach(async () => {
    // Clean up any existing shifts and trips before each test
    await client.query('DELETE FROM trip_logs WHERE assignment_id = $1', [testAssignmentId]);
    await client.query('DELETE FROM driver_shifts WHERE driver_id = $1', [testDriverId]);
  });

  describe('Complete Day Shift Workflow', () => {
    test('Day shift: Check-in → Trip Creation → Check-out', async () => {
      // Step 1: Driver QR check-in at 8:00 AM
      const checkInTime = new Date('2024-07-28T08:00:00');
      const checkInData = {
        driver_qr_data: JSON.stringify({ type: 'driver', id: 'E2E001' }),
        truck_qr_data: JSON.stringify({ type: 'truck', id: 'E2E001' }),
        action: 'check_in'
      };

      const checkInResponse = await request(app)
        .post('/api/driver/connect')
        .send(checkInData)
        .expect(200);

      expect(checkInResponse.body.success).toBe(true);
      expect(checkInResponse.body.action).toBe('check_in');

      // Verify shift was created
      const shiftCheck = await client.query(`
        SELECT id, shift_type, status, auto_created, start_date, end_date
        FROM driver_shifts 
        WHERE driver_id = $1 AND truck_id = $2 AND status = 'active'
        ORDER BY created_at DESC
        LIMIT 1
      `, [testDriverId, testTruckId]);

      expect(shiftCheck.rows.length).toBe(1);
      const shift = shiftCheck.rows[0];
      expect(shift.shift_type).toBe('day'); // 8:00 AM should be day shift
      expect(shift.status).toBe('active');
      expect(shift.auto_created).toBe(true);
      expect(shift.end_date).toBeNull(); // Still active

      // Step 2: Trip creation at 10:00 AM (same day)
      const tripScanData = {
        scan_type: 'truck',
        scanned_data: JSON.stringify({ type: 'truck', id: 'E2E001' }),
        location_scan_data: { type: 'location', id: 'E2ELOC1' }
      };

      const tripResponse = await request(app)
        .post('/api/scanner/public-scan')
        .send(tripScanData)
        .expect(200);

      expect(tripResponse.body.success).toBe(true);

      // Verify trip was created with complete driver information
      const tripCheck = await client.query(`
        SELECT 
          id, status, performed_by_driver_id, performed_by_driver_name,
          performed_by_employee_id, performed_by_shift_id, performed_by_shift_type,
          notes, location_sequence
        FROM trip_logs 
        WHERE assignment_id = $1
        ORDER BY created_at DESC
        LIMIT 1
      `, [testAssignmentId]);

      expect(tripCheck.rows.length).toBe(1);
      const trip = tripCheck.rows[0];
      
      expect(trip.performed_by_driver_id).toBe(testDriverId);
      expect(trip.performed_by_driver_name).toBe('E2E Test Driver');
      expect(trip.performed_by_employee_id).toBe('E2E001');
      expect(trip.performed_by_shift_id).toBe(shift.id);
      expect(trip.performed_by_shift_type).toBe('day');
      
      // Verify notes are populated
      expect(trip.notes).toBeDefined();
      const notes = JSON.parse(trip.notes);
      expect(notes.driver.name).toBe('E2E Test Driver');
      expect(notes.driver.shift_type).toBe('day');
      
      // Verify location_sequence is populated
      expect(trip.location_sequence).toBeDefined();

      // Step 3: Driver check-out at 8:00 AM next day
      const checkOutData = {
        driver_qr_data: JSON.stringify({ type: 'driver', id: 'E2E001' }),
        truck_qr_data: JSON.stringify({ type: 'truck', id: 'E2E001' }),
        action: 'check_out'
      };

      const checkOutResponse = await request(app)
        .post('/api/driver/connect')
        .send(checkOutData)
        .expect(200);

      expect(checkOutResponse.body.success).toBe(true);
      expect(checkOutResponse.body.action).toBe('check_out');

      // Verify shift was completed
      const completedShiftCheck = await client.query(`
        SELECT status, end_date, end_time
        FROM driver_shifts 
        WHERE id = $1
      `, [shift.id]);

      expect(completedShiftCheck.rows[0].status).toBe('completed');
      expect(completedShiftCheck.rows[0].end_date).not.toBeNull();
      expect(completedShiftCheck.rows[0].end_time).not.toBeNull();
    });
  });

  describe('Complete Night Shift Workflow', () => {
    test('Night shift: Check-in → Overnight Trip Creation → Check-out', async () => {
      // Step 1: Driver QR check-in at 10:00 PM
      const checkInData = {
        driver_qr_data: JSON.stringify({ type: 'driver', id: 'E2E001' }),
        truck_qr_data: JSON.stringify({ type: 'truck', id: 'E2E001' }),
        action: 'check_in'
      };

      const checkInResponse = await request(app)
        .post('/api/driver/connect')
        .send(checkInData)
        .expect(200);

      expect(checkInResponse.body.success).toBe(true);

      // Verify night shift was created
      const shiftCheck = await client.query(`
        SELECT id, shift_type, status, auto_created
        FROM driver_shifts 
        WHERE driver_id = $1 AND truck_id = $2 AND status = 'active'
        ORDER BY created_at DESC
        LIMIT 1
      `, [testDriverId, testTruckId]);

      expect(shiftCheck.rows.length).toBe(1);
      const shift = shiftCheck.rows[0];
      expect(shift.shift_type).toBe('night'); // 10:00 PM should be night shift
      expect(shift.status).toBe('active');

      // Step 2: Trip creation at 2:00 AM next day (overnight scenario)
      const tripScanData = {
        scan_type: 'truck',
        scanned_data: JSON.stringify({ type: 'truck', id: 'E2E001' }),
        location_scan_data: { type: 'location', id: 'E2ELOC1' }
      };

      const tripResponse = await request(app)
        .post('/api/scanner/public-scan')
        .send(tripScanData)
        .expect(200);

      expect(tripResponse.body.success).toBe(true);

      // Verify trip was created with night shift driver information
      const tripCheck = await client.query(`
        SELECT 
          performed_by_driver_id, performed_by_shift_type, notes
        FROM trip_logs 
        WHERE assignment_id = $1
        ORDER BY created_at DESC
        LIMIT 1
      `, [testAssignmentId]);

      expect(tripCheck.rows.length).toBe(1);
      const trip = tripCheck.rows[0];
      
      expect(trip.performed_by_driver_id).toBe(testDriverId);
      expect(trip.performed_by_shift_type).toBe('night');
      
      const notes = JSON.parse(trip.notes);
      expect(notes.driver.shift_type).toBe('night');

      // Step 3: Verify shift remains active during automated function calls
      await client.query('SELECT update_all_shift_statuses(CURRENT_TIMESTAMP)');

      const statusCheck = await client.query(`
        SELECT status FROM driver_shifts WHERE id = $1
      `, [shift.id]);

      expect(statusCheck.rows[0].status).toBe('active'); // Should remain active

      // Step 4: Driver check-out
      const checkOutData = {
        driver_qr_data: JSON.stringify({ type: 'driver', id: 'E2E001' }),
        truck_qr_data: JSON.stringify({ type: 'truck', id: 'E2E001' }),
        action: 'check_out'
      };

      const checkOutResponse = await request(app)
        .post('/api/driver/connect')
        .send(checkOutData)
        .expect(200);

      expect(checkOutResponse.body.success).toBe(true);

      // Verify shift was completed
      const completedShiftCheck = await client.query(`
        SELECT status FROM driver_shifts WHERE id = $1
      `, [shift.id]);

      expect(completedShiftCheck.rows[0].status).toBe('completed');
    });
  });

  describe('Status Protection During Automated Functions', () => {
    test('QR-created shifts should be protected from automated status changes', async () => {
      // Create QR-created active shift
      const checkInData = {
        driver_qr_data: JSON.stringify({ type: 'driver', id: 'E2E001' }),
        truck_qr_data: JSON.stringify({ type: 'truck', id: 'E2E001' }),
        action: 'check_in'
      };

      await request(app)
        .post('/api/driver/connect')
        .send(checkInData)
        .expect(200);

      const shiftCheck = await client.query(`
        SELECT id FROM driver_shifts 
        WHERE driver_id = $1 AND status = 'active'
        ORDER BY created_at DESC
        LIMIT 1
      `, [testDriverId]);

      const shiftId = shiftCheck.rows[0].id;

      // Run automated functions multiple times at different times
      const testTimes = [
        new Date('2024-07-28T15:00:00'), // 3:00 PM same day
        new Date('2024-07-28T23:00:00'), // 11:00 PM same day
        new Date('2024-07-29T02:00:00'), // 2:00 AM next day
        new Date('2024-07-29T06:00:00')  // 6:00 AM next day
      ];

      for (const testTime of testTimes) {
        await client.query('SELECT update_all_shift_statuses($1)', [testTime]);
        
        const statusCheck = await client.query(`
          SELECT status, auto_created FROM driver_shifts WHERE id = $1
        `, [shiftId]);

        expect(statusCheck.rows[0].status).toBe('active');
        expect(statusCheck.rows[0].auto_created).toBe(true);

        // Verify driver can still be captured for trips
        const tripScanData = {
          scan_type: 'truck',
          scanned_data: JSON.stringify({ type: 'truck', id: 'E2E001' }),
          location_scan_data: { type: 'location', id: 'E2ELOC1' }
        };

        const tripResponse = await request(app)
          .post('/api/scanner/public-scan')
          .send(tripScanData)
          .expect(200);

        expect(tripResponse.body.success).toBe(true);
        expect(tripResponse.body.shift_info.driver_name).toBe('E2E Test Driver');

        // Clean up trip for next iteration
        await client.query('DELETE FROM trip_logs WHERE assignment_id = $1', [testAssignmentId]);
      }
    });
  });

  describe('Data Flow Validation', () => {
    test('Complete data flow: Shift Management → Assignment Management → Trip Monitoring', async () => {
      // Step 1: Shift Management - Create active shift
      const checkInData = {
        driver_qr_data: JSON.stringify({ type: 'driver', id: 'E2E001' }),
        truck_qr_data: JSON.stringify({ type: 'truck', id: 'E2E001' }),
        action: 'check_in'
      };

      const checkInResponse = await request(app)
        .post('/api/driver/connect')
        .send(checkInData)
        .expect(200);

      // Verify shift data is available
      const shiftData = await client.query(`
        SELECT ds.id, ds.shift_type, ds.status, d.full_name, d.employee_id
        FROM driver_shifts ds
        JOIN drivers d ON ds.driver_id = d.id
        WHERE ds.driver_id = $1 AND ds.status = 'active'
      `, [testDriverId]);

      expect(shiftData.rows.length).toBe(1);
      const shift = shiftData.rows[0];

      // Step 2: Assignment Management - Verify assignment can access shift data
      const assignmentData = await client.query(`
        SELECT a.*, ds.shift_type, d.full_name as driver_name
        FROM assignments a
        LEFT JOIN driver_shifts ds ON ds.truck_id = a.truck_id AND ds.status = 'active'
        LEFT JOIN drivers d ON ds.driver_id = d.id
        WHERE a.id = $1
      `, [testAssignmentId]);

      expect(assignmentData.rows.length).toBe(1);
      const assignment = assignmentData.rows[0];
      expect(assignment.shift_type).toBe(shift.shift_type);
      expect(assignment.driver_name).toBe(shift.full_name);

      // Step 3: Trip Monitoring - Create trip and verify complete data flow
      const tripScanData = {
        scan_type: 'truck',
        scanned_data: JSON.stringify({ type: 'truck', id: 'E2E001' }),
        location_scan_data: { type: 'location', id: 'E2ELOC1' }
      };

      const tripResponse = await request(app)
        .post('/api/scanner/public-scan')
        .send(tripScanData)
        .expect(200);

      // Verify complete data flow in trip_logs
      const tripData = await client.query(`
        SELECT 
          tl.*,
          ds.shift_type as source_shift_type,
          d.full_name as source_driver_name,
          a.assignment_code
        FROM trip_logs tl
        JOIN assignments a ON tl.assignment_id = a.id
        LEFT JOIN driver_shifts ds ON tl.performed_by_shift_id = ds.id
        LEFT JOIN drivers d ON tl.performed_by_driver_id = d.id
        WHERE tl.assignment_id = $1
      `, [testAssignmentId]);

      expect(tripData.rows.length).toBe(1);
      const trip = tripData.rows[0];

      // Verify data consistency across all systems
      expect(trip.performed_by_driver_id).toBe(testDriverId);
      expect(trip.performed_by_driver_name).toBe(shift.full_name);
      expect(trip.performed_by_employee_id).toBe(shift.employee_id);
      expect(trip.performed_by_shift_type).toBe(shift.shift_type);
      expect(trip.source_shift_type).toBe(shift.shift_type);
      expect(trip.source_driver_name).toBe(shift.full_name);

      // Verify notes contain complete context
      const notes = JSON.parse(trip.notes);
      expect(notes.driver.name).toBe(shift.full_name);
      expect(notes.driver.employee_id).toBe(shift.employee_id);
      expect(notes.driver.shift_type).toBe(shift.shift_type);
      expect(notes.assignment_code).toBe('E2EASG1');
    });
  });

  describe('Error Handling and Edge Cases', () => {
    test('Trip creation without active driver should handle gracefully', async () => {
      // Don't create any active shift
      
      const tripScanData = {
        scan_type: 'truck',
        scanned_data: JSON.stringify({ type: 'truck', id: 'E2E001' }),
        location_scan_data: { type: 'location', id: 'E2ELOC1' }
      };

      const tripResponse = await request(app)
        .post('/api/scanner/public-scan')
        .send(tripScanData)
        .expect(200);

      expect(tripResponse.body.success).toBe(true);
      expect(tripResponse.body.shift_info.driver_name).toBeNull();
      expect(tripResponse.body.shift_info.shift_type).toBe('No Active Shift');

      // Verify trip was still created but with null driver fields
      const tripCheck = await client.query(`
        SELECT performed_by_driver_id, performed_by_driver_name, notes
        FROM trip_logs 
        WHERE assignment_id = $1
        ORDER BY created_at DESC
        LIMIT 1
      `, [testAssignmentId]);

      if (tripCheck.rows.length > 0) {
        const trip = tripCheck.rows[0];
        expect(trip.performed_by_driver_id).toBeNull();
        expect(trip.performed_by_driver_name).toBeNull();
        
        const notes = JSON.parse(trip.notes);
        expect(notes.driver).toBeNull();
      }
    });

    test('Multiple rapid trip creations should maintain data consistency', async () => {
      // Create active shift
      const checkInData = {
        driver_qr_data: JSON.stringify({ type: 'driver', id: 'E2E001' }),
        truck_qr_data: JSON.stringify({ type: 'truck', id: 'E2E001' }),
        action: 'check_in'
      };

      await request(app)
        .post('/api/driver/connect')
        .send(checkInData)
        .expect(200);

      // Create multiple trips rapidly
      const tripScanData = {
        scan_type: 'truck',
        scanned_data: JSON.stringify({ type: 'truck', id: 'E2E001' }),
        location_scan_data: { type: 'location', id: 'E2ELOC1' }
      };

      const promises = [];
      for (let i = 0; i < 3; i++) {
        promises.push(
          request(app)
            .post('/api/scanner/public-scan')
            .send(tripScanData)
        );
      }

      const responses = await Promise.all(promises);

      // All should succeed
      responses.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
      });

      // Verify all trips have consistent driver information
      const tripCheck = await client.query(`
        SELECT DISTINCT performed_by_driver_id, performed_by_driver_name, performed_by_shift_type
        FROM trip_logs 
        WHERE assignment_id = $1
      `, [testAssignmentId]);

      // Should have only one unique set of driver information
      expect(tripCheck.rows.length).toBe(1);
      expect(tripCheck.rows[0].performed_by_driver_id).toBe(testDriverId);
      expect(tripCheck.rows[0].performed_by_driver_name).toBe('E2E Test Driver');
    });
  });
});