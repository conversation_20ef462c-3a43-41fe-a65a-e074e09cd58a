#!/bin/bash
#
# Quick fix for database management script issues
#

echo "🔧 Fixing database management script issues..."

# Load deployment configuration
if [[ -f "deployment-config.conf" ]]; then
    source deployment-config.conf
    echo "✅ Loaded deployment configuration"
else
    echo "❌ deployment-config.conf not found, using defaults"
    DB_HOST="localhost"
    DB_PORT="5432"
    DB_NAME="hauling_qr_system"
    DB_USER="hauling_app"
    DB_PASSWORD="PostgreSQLPassword123"
fi

echo "📊 Database Configuration:"
echo "  Host: $DB_HOST"
echo "  Port: $DB_PORT"
echo "  Database: $DB_NAME"
echo "  User: $DB_USER"

# Step 1: Fix database and user
echo "🗄️ Step 1: Fixing database and user..."
sudo -u postgres psql -c "SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname = '$DB_NAME';" 2>/dev/null || true
sudo -u postgres psql -c "DROP DATABASE IF EXISTS $DB_NAME;" 2>/dev/null || true
sudo -u postgres psql -c "DROP USER IF EXISTS $DB_USER;" 2>/dev/null || true

sudo -u postgres psql -c "CREATE USER $DB_USER WITH PASSWORD '$DB_PASSWORD' CREATEDB;"
sudo -u postgres psql -c "CREATE DATABASE $DB_NAME OWNER $DB_USER;"
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE $DB_NAME TO $DB_USER;"
sudo -u postgres psql -c "GRANT ALL ON SCHEMA public TO $DB_USER;" -d "$DB_NAME" 2>/dev/null || true

echo "✅ Database and user recreated"

# Step 2: Fix .env file
echo "📝 Step 2: Fixing .env file..."
cd /var/www/hauling-qr-system
if [[ -f ".env" ]]; then
    sed -i "s|^DB_HOST=.*|DB_HOST=$DB_HOST|" .env
    sed -i "s|^DB_PORT=.*|DB_PORT=$DB_PORT|" .env
    sed -i "s|^DB_NAME=.*|DB_NAME=$DB_NAME|" .env
    sed -i "s|^DB_USER=.*|DB_USER=$DB_USER|" .env
    sed -i "s|^DB_PASSWORD=.*|DB_PASSWORD=$DB_PASSWORD|" .env
    echo "✅ .env file updated"
else
    echo "❌ .env file not found"
fi

# Step 3: Create corrected manage-database.sh script
echo "🔧 Step 3: Creating corrected manage-database.sh script..."
cat > /var/www/hauling-qr-system/manage-database.sh << 'EOF'
#!/bin/bash
#
# Database Management Script
#

# Load environment variables from .env file
if [[ -f "/var/www/hauling-qr-system/.env" ]]; then
    source /var/www/hauling-qr-system/.env
elif [[ -f ".env" ]]; then
    source .env
fi

# Set defaults if not provided
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-5432}"
DB_NAME="${DB_NAME:-hauling_qr_system}"
DB_USER="${DB_USER:-hauling_app}"
DB_PASSWORD="${DB_PASSWORD:-PostgreSQLPassword123}"

case "$1" in
    connect)
        echo "Connecting to database..."
        PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME"
        ;;
    backup)
        echo "Creating database backup..."
        backup_file="backup_$(date +%Y%m%d_%H%M%S).sql"
        PGPASSWORD="$DB_PASSWORD" pg_dump -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" "$DB_NAME" > "$backup_file"
        echo "✅ Backup created: $backup_file"
        ;;
    test)
        echo "Testing database connection..."
        if PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT version();" 2>/dev/null >/dev/null; then
            echo "✅ Database connection successful"
        else
            echo "❌ Database connection failed"
            exit 1
        fi
        ;;
    verify)
        echo "Verifying database schema..."
        echo "Current tables:"
        PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "\dt"
        echo
        echo "Checking critical tables..."
        
        critical_tables=("users" "drivers" "dump_trucks" "locations" "assignments" "trip_logs" "driver_shifts")
        missing_tables=()
        
        for table in "${critical_tables[@]}"; do
            exists=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = '$table');" 2>/dev/null | tr -d ' ')
            if [[ "$exists" == "t" ]]; then
                echo "✅ $table - exists"
            else
                echo "❌ $table - missing"
                missing_tables+=("$table")
            fi
        done
        
        if [[ ${#missing_tables[@]} -eq 0 ]]; then
            echo
            echo "✅ All critical tables are present"
        else
            echo
            echo "❌ Missing tables: ${missing_tables[*]}"
            echo "Run database initialization:"
            echo "PGPASSWORD=\"$DB_PASSWORD\" psql -h \"$DB_HOST\" -p \"$DB_PORT\" -U \"$DB_USER\" -d \"$DB_NAME\" -f /var/www/hauling-qr-system/database/init.sql"
        fi
        ;;
    init)
        echo "Initializing database schema..."
        if [[ -f "/var/www/hauling-qr-system/database/init.sql" ]]; then
            PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f /var/www/hauling-qr-system/database/init.sql
            echo "✅ Database initialization completed"
            echo "Running verification..."
            $0 verify
        elif [[ -f "database/init.sql" ]]; then
            PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f database/init.sql
            echo "✅ Database initialization completed"
            echo "Running verification..."
            $0 verify
        else
            echo "❌ init.sql file not found"
            echo "Expected locations:"
            echo "  - /var/www/hauling-qr-system/database/init.sql"
            echo "  - database/init.sql"
            exit 1
        fi
        ;;
    tables)
        echo "=== Database Tables ==="
        PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "\dt+"
        ;;
    health)
        echo "=== Database Health Check ==="
        echo "Connection test:"
        if PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT 'Database OK' as status;" 2>/dev/null; then
            echo "✅ Database connection: OK"
        else
            echo "❌ Database connection: FAILED"
        fi
        ;;
    query)
        if [[ -z "$2" ]]; then
            echo "Usage: $0 query \"SQL_QUERY\""
            echo "Example: $0 query \"SELECT COUNT(*) FROM users;\""
            exit 1
        fi
        echo "Executing query: $2"
        PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "$2"
        ;;
    *)
        echo "Database Management Script for Hauling QR System"
        echo "================================================"
        echo
        echo "Usage: $0 {command}"
        echo
        echo "Commands:"
        echo "  connect   - Connect to database interactively"
        echo "  test      - Test database connection"
        echo "  verify    - Verify database schema and critical tables"
        echo "  init      - Initialize database with schema"
        echo "  backup    - Create database backup"
        echo "  tables    - List all tables with details"
        echo "  health    - Run comprehensive health check"
        echo "  query     - Execute custom SQL query"
        echo
        echo "Database Configuration:"
        echo "  Host: $DB_HOST"
        echo "  Port: $DB_PORT"
        echo "  Database: $DB_NAME"
        echo "  User: $DB_USER"
        echo
        exit 1
        ;;
esac
EOF

chmod +x /var/www/hauling-qr-system/manage-database.sh
echo "✅ manage-database.sh script updated"

# Step 4: Initialize database
echo "📊 Step 4: Initializing database..."
if [[ -f "/var/www/hauling-qr-system/database/init.sql" ]]; then
    PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f /var/www/hauling-qr-system/database/init.sql
    echo "✅ Database initialized"
else
    echo "❌ init.sql not found"
fi

# Step 5: Test everything
echo "🔍 Step 5: Testing the fix..."
cd /var/www/hauling-qr-system

echo "Testing database connection..."
./manage-database.sh test

echo "Verifying database schema..."
./manage-database.sh verify

echo "✅ Database fix completed!"
echo "🎉 You should now be able to use all manage-database.sh commands"