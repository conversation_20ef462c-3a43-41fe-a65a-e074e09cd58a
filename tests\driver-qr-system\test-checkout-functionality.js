const { query } = require('../../server/config/database');

/**
 * Test the checkout functionality with current database state
 * This simulates the exact logic that was causing the "Invalid time value" error
 */

async function testCheckoutFunctionality() {
  try {
    console.log('🔍 Testing checkout functionality with current database state...');
    
    // Get an active shift to test with (same query as in driver.js)
    const activeShifts = await query(`
      SELECT ds.id, ds.truck_id, ds.start_date, ds.start_time, dt.truck_number
      FROM driver_shifts ds
      JOIN dump_trucks dt ON ds.truck_id = dt.id
      WHERE ds.driver_id = $1 AND ds.status = 'active'
        AND ds.start_date IS NOT NULL AND ds.start_time IS NOT NULL
      ORDER BY ds.created_at DESC
      LIMIT 1
    `, [1]); // Using driver_id = 1 for testing
    
    if (activeShifts.rows.length === 0) {
      console.log('❌ No active shifts found for driver_id = 1');
      console.log('   Creating a test shift...');
      
      // Create a test shift for testing
      const currentTimestamp = new Date();
      const currentDate = currentTimestamp.toISOString().split('T')[0];
      const currentTime = currentTimestamp.toTimeString().split(' ')[0];
      
      await query(`
        INSERT INTO driver_shifts (
          truck_id, driver_id, shift_type, start_date, end_date, 
          start_time, end_time, status, auto_created, created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      `, [
        1, 1, 'custom', currentDate, currentDate,
        currentTime, '23:59:59', 'active', true, currentTimestamp, currentTimestamp
      ]);
      
      console.log('✅ Test shift created');
      return;
    }
    
    const currentShift = activeShifts.rows[0];
    console.log('📋 Testing with shift:', {
      id: currentShift.id,
      truck_id: currentShift.truck_id,
      start_date: currentShift.start_date,
      start_time: currentShift.start_time,
      truck_number: currentShift.truck_number
    });
    
    // Simulate the exact checkout logic from driver.js
    const currentTimestamp = new Date();
    let startDateTime;
    let durationHours = 0;
    let durationMinutes = 0;
    
    try {
      // Validate that we have valid date and time values
      if (!currentShift.start_date || !currentShift.start_time) {
        throw new Error('Missing start_date or start_time');
      }
      
      // Ensure start_date is in proper format (handle Date objects or strings)
      let formattedStartDate;
      if (currentShift.start_date instanceof Date) {
        formattedStartDate = currentShift.start_date.toISOString().split('T')[0];
        console.log(`📅 Start date is Date object, formatted to: ${formattedStartDate}`);
      } else {
        formattedStartDate = currentShift.start_date;
        console.log(`📅 Start date is string: ${formattedStartDate}`);
      }
      
      startDateTime = new Date(`${formattedStartDate}T${currentShift.start_time}`);
      
      // Check if the created date is valid
      if (isNaN(startDateTime.getTime())) {
        throw new Error('Invalid date/time combination');
      }
      
      const endDateTime = currentTimestamp;
      const durationMs = endDateTime - startDateTime;
      durationHours = Math.floor(durationMs / (1000 * 60 * 60));
      durationMinutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));
      
      // Ensure non-negative duration
      if (durationMs < 0) {
        durationHours = 0;
        durationMinutes = 0;
      }
      
      console.log(`✅ Date parsing successful: ${startDateTime.toISOString()}`);
      
    } catch (dateError) {
      console.log(`⚠️  Date parsing error: ${dateError.message}`);
      console.log('   Using fallback values...');
      startDateTime = null;
      durationHours = 0;
      durationMinutes = 0;
    }
    
    // Test the result object creation (this is where the error was happening)
    let checkInTime;
    try {
      checkInTime = startDateTime && !isNaN(startDateTime.getTime()) 
        ? startDateTime.toISOString() 
        : currentTimestamp.toISOString();
      console.log(`✅ Check-in time formatted successfully: ${checkInTime}`);
    } catch (error) {
      checkInTime = currentTimestamp.toISOString();
      console.log(`⚠️  Fallback to current time: ${checkInTime}`);
    }
    
    // Create the result object (this was the line causing the crash)
    const result = {
      action: 'check_out',
      message: `Checked out from ${currentShift.truck_number}`,
      truck: currentShift.truck_number,
      shift_id: currentShift.id,
      check_in_time: checkInTime,
      check_out_time: currentTimestamp.toISOString(),
      duration: `${durationHours}h ${durationMinutes}m`,
      driver: {
        employee_id: 'TEST_EMP',
        full_name: 'Test Driver'
      }
    };
    
    console.log('✅ Result object created successfully:');
    console.log(JSON.stringify(result, null, 2));
    
    console.log('\n🎉 Checkout functionality test PASSED - No crashes!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  }
}

// Run the test
if (require.main === module) {
  testCheckoutFunctionality()
    .then(() => {
      console.log('\n✅ Test completed successfully.');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Test failed:', error);
      process.exit(1);
    });
}

module.exports = { testCheckoutFunctionality };