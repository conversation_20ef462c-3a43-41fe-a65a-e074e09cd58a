---
type: "agent_requested"
description: "Analyzes all JavaScript files in the scripts/* directory to identify and remove unused functions while preserving route handlers, middleware, database functions, and core system functionality for the Hauling QR Trip System"
---
Analyze the JavaScript files in the scripts/* directory to identify unused functions. Preserve all route handlers, middleware functions, database operations, and core system functionality. Generate a report of unused functions that can be safely removed and create a cleanup plan that maintains the integrity of the Hauling QR Trip System. Focus on utility scripts, test files, and development helpers while being extremely careful not to remove any functions that are part of the core trip workflow, QR code processing, or system startup processes.