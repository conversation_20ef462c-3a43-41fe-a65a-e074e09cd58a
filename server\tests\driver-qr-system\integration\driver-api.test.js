const request = require('supertest');
const express = require('express');
const driverRoutes = require('../../../routes/driver');

// Mock dependencies
jest.mock('../../../config/database', () => ({
  query: jest.fn(),
  getClient: jest.fn()
}));

jest.mock('../../../utils/DriverQRCodeGenerator');
jest.mock('../../../utils/logger', () => ({
  logError: jest.fn(),
  logInfo: jest.fn()
}));

const { query, getClient } = require('../../../config/database');
const DriverQRCodeGenerator = require('../../../utils/DriverQRCodeGenerator');

// Create test app
const app = express();
app.use(express.json());
app.use('/api/driver', driverRoutes);

describe('Driver API Integration Tests', () => {
  let mockClient;

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock database client
    mockClient = {
      query: jest.fn(),
      release: jest.fn()
    };
    getClient.mockResolvedValue(mockClient);
  });

  describe('POST /api/driver/connect', () => {
    const validDriverQR = {
      id: 'DR-001',
      driver_id: 123,
      employee_id: 'DR-001',
      generated_date: '2025-01-01T00:00:00.000Z',
      type: 'driver'
    };

    const validTruckQR = {
      id: 'DT-100',
      type: 'truck',
      truck_number: 'DT-100'
    };

    const mockDriver = {
      id: 123,
      employee_id: 'DR-001',
      full_name: 'John Doe',
      status: 'active'
    };

    const mockTruck = {
      id: 456,
      truck_number: 'DT-100',
      license_plate: 'ABC-123',
      status: 'active',
      qr_code_data: validTruckQR
    };

    it('should successfully check in driver with no active shift', async () => {
      // Mock driver validation
      DriverQRCodeGenerator.validateDriverQR.mockResolvedValue({
        success: true,
        valid: true,
        driver: mockDriver
      });

      // Mock database queries
      mockClient.query
        .mockResolvedValueOnce() // BEGIN
        .mockResolvedValueOnce({ rows: [mockTruck] }) // Truck lookup
        .mockResolvedValueOnce({ rows: [] }) // No active shift
        .mockResolvedValueOnce({ rows: [{ id: 789 }] }) // Insert new shift
        .mockResolvedValueOnce(); // COMMIT

      const response = await request(app)
        .post('/api/driver/connect')
        .send({
          driver_qr_data: JSON.stringify(validDriverQR),
          truck_qr_data: JSON.stringify(validTruckQR)
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.action).toBe('check_in');
      expect(response.body.truck).toBe('DT-100');
      expect(response.body.shift_id).toBe(789);
      expect(response.body.driver.employee_id).toBe('DR-001');
    });

    it('should successfully check out driver with active shift on same truck', async () => {
      const mockActiveShift = {
        id: 789,
        truck_id: 456,
        start_date: '2025-01-01',
        start_time: '08:00:00',
        truck_number: 'DT-100'
      };

      // Mock driver validation
      DriverQRCodeGenerator.validateDriverQR.mockResolvedValue({
        success: true,
        valid: true,
        driver: mockDriver
      });

      // Mock database queries
      mockClient.query
        .mockResolvedValueOnce() // BEGIN
        .mockResolvedValueOnce({ rows: [mockTruck] }) // Truck lookup
        .mockResolvedValueOnce({ rows: [mockActiveShift] }) // Active shift found
        .mockResolvedValueOnce() // Update shift to completed
        .mockResolvedValueOnce(); // COMMIT

      const response = await request(app)
        .post('/api/driver/connect')
        .send({
          driver_qr_data: JSON.stringify(validDriverQR),
          truck_qr_data: JSON.stringify(validTruckQR)
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.action).toBe('check_out');
      expect(response.body.truck).toBe('DT-100');
      expect(response.body.shift_id).toBe(789);
      expect(response.body).toHaveProperty('duration');
    });

    it('should handle handover when driver scans different truck', async () => {
      const mockActiveShift = {
        id: 789,
        truck_id: 999, // Different truck
        start_date: '2025-01-01',
        start_time: '08:00:00',
        truck_number: 'DT-200'
      };

      // Mock driver validation
      DriverQRCodeGenerator.validateDriverQR.mockResolvedValue({
        success: true,
        valid: true,
        driver: mockDriver
      });

      // Mock database queries
      mockClient.query
        .mockResolvedValueOnce() // BEGIN
        .mockResolvedValueOnce({ rows: [mockTruck] }) // Truck lookup
        .mockResolvedValueOnce({ rows: [mockActiveShift] }) // Active shift on different truck
        .mockResolvedValueOnce({ rows: [{ handover_driver_shift: 890 }] }) // Handover function
        .mockResolvedValueOnce(); // COMMIT

      const response = await request(app)
        .post('/api/driver/connect')
        .send({
          driver_qr_data: JSON.stringify(validDriverQR),
          truck_qr_data: JSON.stringify(validTruckQR)
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.action).toBe('handover');
      expect(response.body.previous_truck).toBe('DT-200');
      expect(response.body.new_truck).toBe('DT-100');
      expect(response.body.shift_id).toBe(890);
    });

    it('should reject invalid driver QR code', async () => {
      // Mock driver validation failure
      DriverQRCodeGenerator.validateDriverQR.mockResolvedValue({
        success: false,
        valid: false,
        error: 'Driver account is inactive. Please contact your supervisor.'
      });

      mockClient.query.mockResolvedValueOnce(); // BEGIN
      mockClient.query.mockResolvedValueOnce(); // ROLLBACK

      const response = await request(app)
        .post('/api/driver/connect')
        .send({
          driver_qr_data: JSON.stringify(validDriverQR),
          truck_qr_data: JSON.stringify(validTruckQR)
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('INVALID_DRIVER_QR');
      expect(response.body.message).toBe('Driver account is inactive. Please contact your supervisor.');
    });

    it('should reject invalid truck QR code format', async () => {
      // Mock driver validation success
      DriverQRCodeGenerator.validateDriverQR.mockResolvedValue({
        success: true,
        valid: true,
        driver: mockDriver
      });

      mockClient.query.mockResolvedValueOnce(); // BEGIN
      mockClient.query.mockResolvedValueOnce(); // ROLLBACK

      const response = await request(app)
        .post('/api/driver/connect')
        .send({
          driver_qr_data: JSON.stringify(validDriverQR),
          truck_qr_data: 'invalid json'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('INVALID_TRUCK_QR');
      expect(response.body.message).toBe('Truck QR code is not valid JSON format');
    });

    it('should reject inactive truck', async () => {
      const inactiveTruck = {
        ...mockTruck,
        status: 'maintenance'
      };

      // Mock driver validation success
      DriverQRCodeGenerator.validateDriverQR.mockResolvedValue({
        success: true,
        valid: true,
        driver: mockDriver
      });

      // Mock database queries
      mockClient.query
        .mockResolvedValueOnce() // BEGIN
        .mockResolvedValueOnce({ rows: [inactiveTruck] }) // Truck lookup (inactive)
        .mockResolvedValueOnce(); // ROLLBACK

      const response = await request(app)
        .post('/api/driver/connect')
        .send({
          driver_qr_data: JSON.stringify(validDriverQR),
          truck_qr_data: JSON.stringify(validTruckQR)
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('TRUCK_INACTIVE');
      expect(response.body.message).toBe('Truck is not available for assignment. Please contact maintenance or supervisor.');
    });

    it('should reject missing required fields', async () => {
      const response = await request(app)
        .post('/api/driver/connect')
        .send({
          driver_qr_data: JSON.stringify(validDriverQR)
          // Missing truck_qr_data
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('VALIDATION_ERROR');
    });

    it('should handle truck mismatch on checkout', async () => {
      const mockActiveShift = {
        id: 789,
        truck_id: 999, // Different truck ID
        start_date: '2025-01-01',
        start_time: '08:00:00',
        truck_number: 'DT-200'
      };

      // Mock driver validation
      DriverQRCodeGenerator.validateDriverQR.mockResolvedValue({
        success: true,
        valid: true,
        driver: mockDriver
      });

      // Mock database queries
      mockClient.query
        .mockResolvedValueOnce() // BEGIN
        .mockResolvedValueOnce({ rows: [mockTruck] }) // Truck lookup
        .mockResolvedValueOnce({ rows: [mockActiveShift] }) // Active shift on different truck
        .mockResolvedValueOnce(); // ROLLBACK

      const response = await request(app)
        .post('/api/driver/connect')
        .send({
          driver_qr_data: JSON.stringify(validDriverQR),
          truck_qr_data: JSON.stringify(validTruckQR),
          action: 'check_out' // Explicitly requesting checkout
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('TRUCK_MISMATCH');
      expect(response.body.message).toContain('DT-200');
    });
  });

  describe('GET /api/driver/status/:employeeId', () => {
    const mockDriver = {
      id: 123,
      employee_id: 'DR-001',
      full_name: 'John Doe',
      status: 'active'
    };

    it('should return driver status with no active shift', async () => {
      query
        .mockResolvedValueOnce({ rows: [mockDriver] }) // Driver lookup
        .mockResolvedValueOnce({ rows: [] }); // No active shift

      const response = await request(app)
        .get('/api/driver/status/DR-001');

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.driver.employee_id).toBe('DR-001');
      expect(response.body.status).toBe('checked_out');
      expect(response.body.status_message).toBe('Ready to check in');
      expect(response.body.current_truck).toBe(null);
    });

    it('should return driver status with active shift', async () => {
      const mockActiveShift = {
        id: 789,
        truck_id: 456,
        start_date: '2025-01-01',
        start_time: '08:00:00',
        truck_number: 'DT-100',
        license_plate: 'ABC-123'
      };

      query
        .mockResolvedValueOnce({ rows: [mockDriver] }) // Driver lookup
        .mockResolvedValueOnce({ rows: [mockActiveShift] }); // Active shift

      const response = await request(app)
        .get('/api/driver/status/DR-001');

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.driver.employee_id).toBe('DR-001');
      expect(response.body.status).toBe('checked_in');
      expect(response.body.status_message).toBe('Currently assigned to DT-100');
      expect(response.body.current_truck).toEqual({
        id: 456,
        truck_number: 'DT-100',
        license_plate: 'ABC-123'
      });
      expect(response.body.shift_info.shift_id).toBe(789);
    });

    it('should return 404 for non-existent driver', async () => {
      query.mockResolvedValueOnce({ rows: [] }); // No driver found

      const response = await request(app)
        .get('/api/driver/status/DR-999');

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('DRIVER_NOT_FOUND');
    });

    it('should reject inactive driver', async () => {
      const inactiveDriver = {
        ...mockDriver,
        status: 'inactive'
      };

      query.mockResolvedValueOnce({ rows: [inactiveDriver] });

      const response = await request(app)
        .get('/api/driver/status/DR-001');

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('DRIVER_INACTIVE');
      expect(response.body.message).toBe('Driver account is inactive. Please contact your supervisor.');
    });
  });

  describe('GET /api/driver/health', () => {
    it('should return health status', async () => {
      const response = await request(app)
        .get('/api/driver/health');

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.service).toBe('Driver Connect API');
      expect(response.body.status).toBe('healthy');
      expect(response.body).toHaveProperty('timestamp');
    });
  });

  describe('Rate Limiting', () => {
    it('should apply rate limiting to connect endpoint', async () => {
      // This test would need to be run in a real environment to test rate limiting
      // For now, we just verify the endpoint exists and responds
      const response = await request(app)
        .post('/api/driver/connect')
        .send({});

      // Should get validation error, not rate limit error (since we're not hitting the limit)
      expect(response.status).toBe(400);
      expect(response.body.error).toBe('VALIDATION_ERROR');
    });
  });
});