const { logError, logInfo } = require('./logger');
const { query } = require('../config/database');

/**
 * Security Monitor Utility
 * Tracks and analyzes security patterns and potential abuse
 */
class SecurityMonitor {
  
  /**
   * Track suspicious activity patterns
   * @param {string} activityType - Type of activity
   * @param {Object} context - Activity context
   */
  static async trackSuspiciousActivity(activityType, context) {
    try {
      const { ip_address, user_agent, endpoint, details } = context;
      
      // Log the suspicious activity
      logError('SUSPICIOUS_ACTIVITY_DETECTED', `${activityType} detected`, {
        activity_type: activityType,
        ip_address,
        user_agent,
        endpoint,
        timestamp: new Date().toISOString(),
        details
      });

      // Store in database for pattern analysis (if security_logs table exists)
      try {
        await query(
          `INSERT INTO security_logs (activity_type, ip_address, user_agent, endpoint, details, created_at)
           VALUES ($1, $2, $3, $4, $5, $6)`,
          [activityType, ip_address, user_agent, endpoint, JSON.stringify(details), new Date()]
        );
      } catch (dbError) {
        // Don't fail if security_logs table doesn't exist
        logError('SECURITY_LOG_DB_ERROR', dbError, { activity_type: activityType });
      }

    } catch (error) {
      logError('SECURITY_MONITOR_ERROR', error, { activity_type: activityType });
    }
  }

  /**
   * Analyze QR code scanning patterns for abuse
   * @param {string} ip_address - Client IP address
   * @param {Object} qrData - QR code data
   * @returns {Object} Analysis result
   */
  static analyzeQRScanPattern(ip_address, qrData) {
    const suspiciousIndicators = [];
    
    try {
      // Check for rapid scanning patterns (would need Redis or in-memory store in production)
      // For now, just analyze the QR data structure
      
      const dataString = JSON.stringify(qrData);
      
      // Check for unusual data patterns
      if (dataString.length > 500) {
        suspiciousIndicators.push('OVERSIZED_QR_DATA');
      }
      
      // Check for potential injection attempts
      const dangerousPatterns = ['<script', 'javascript:', 'data:', 'vbscript:', 'onload='];
      for (const pattern of dangerousPatterns) {
        if (dataString.toLowerCase().includes(pattern)) {
          suspiciousIndicators.push(`DANGEROUS_PATTERN_${pattern.toUpperCase()}`);
        }
      }
      
      // Check for excessive special characters
      const specialCharCount = (dataString.match(/[<>'"&;]/g) || []).length;
      if (specialCharCount > 10) {
        suspiciousIndicators.push('EXCESSIVE_SPECIAL_CHARS');
      }

      return {
        suspicious: suspiciousIndicators.length > 0,
        indicators: suspiciousIndicators,
        risk_level: this._calculateRiskLevel(suspiciousIndicators)
      };

    } catch (error) {
      return {
        suspicious: true,
        indicators: ['ANALYSIS_ERROR'],
        risk_level: 'HIGH'
      };
    }
  }

  /**
   * Monitor failed authentication attempts
   * @param {string} ip_address - Client IP address
   * @param {string} employee_id - Employee ID attempted
   * @param {string} failure_reason - Reason for failure
   */
  static async monitorFailedAuth(ip_address, employee_id, failure_reason) {
    try {
      // Track failed attempts (in production, use Redis for rate limiting)
      const context = {
        ip_address,
        employee_id,
        failure_reason,
        endpoint: '/api/driver/connect',
        details: {
          failure_type: 'AUTHENTICATION_FAILED',
          attempted_employee_id: employee_id,
          failure_reason
        }
      };

      await this.trackSuspiciousActivity('FAILED_AUTHENTICATION', context);

      // Check if this IP has too many failures (simplified check)
      // In production, implement proper rate limiting with Redis
      logInfo('AUTH_FAILURE_MONITORED', `Failed auth attempt for ${employee_id}`, {
        ip_address,
        employee_id,
        failure_reason,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logError('FAILED_AUTH_MONITOR_ERROR', error, { ip_address, employee_id });
    }
  }

  /**
   * Validate request origin and headers
   * @param {Object} req - Express request object
   * @returns {Object} Validation result
   */
  static validateRequestOrigin(req) {
    const suspiciousHeaders = [];
    
    // Check for missing or suspicious User-Agent
    const userAgent = req.get('User-Agent');
    if (!userAgent || userAgent.length < 10) {
      suspiciousHeaders.push('MISSING_OR_SHORT_USER_AGENT');
    }
    
    // Check for suspicious User-Agent patterns
    if (userAgent && (userAgent.includes('bot') || userAgent.includes('crawler') || userAgent.includes('spider'))) {
      suspiciousHeaders.push('BOT_USER_AGENT');
    }
    
    // Check for missing Accept header
    if (!req.get('Accept')) {
      suspiciousHeaders.push('MISSING_ACCEPT_HEADER');
    }
    
    // Check for suspicious X-Forwarded-For chains
    const forwardedFor = req.get('X-Forwarded-For');
    if (forwardedFor && forwardedFor.split(',').length > 5) {
      suspiciousHeaders.push('EXCESSIVE_PROXY_CHAIN');
    }

    return {
      suspicious: suspiciousHeaders.length > 0,
      indicators: suspiciousHeaders,
      risk_level: this._calculateRiskLevel(suspiciousHeaders)
    };
  }

  /**
   * Generate security audit report
   * @param {Object} options - Report options
   * @returns {Promise<Object>} Security audit report
   */
  static async generateSecurityAuditReport(options = {}) {
    try {
      const { date_from, date_to, limit = 100 } = options;
      
      // This would query security_logs table in production
      // For now, return a basic structure
      
      return {
        success: true,
        report: {
          period: {
            from: date_from || new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
            to: date_to || new Date().toISOString()
          },
          summary: {
            total_suspicious_activities: 0,
            failed_authentications: 0,
            rate_limit_violations: 0,
            tamper_attempts: 0
          },
          top_risk_ips: [],
          security_recommendations: [
            'Monitor failed authentication patterns',
            'Implement IP-based rate limiting',
            'Enable QR code tamper detection',
            'Regular security audit reviews'
          ]
        }
      };

    } catch (error) {
      logError('SECURITY_AUDIT_REPORT_ERROR', error, options);
      throw error;
    }
  }

  /**
   * Calculate risk level based on indicators
   * @param {Array} indicators - Security indicators
   * @returns {string} Risk level
   * @private
   */
  static _calculateRiskLevel(indicators) {
    if (indicators.length === 0) return 'LOW';
    if (indicators.length <= 2) return 'MEDIUM';
    if (indicators.length <= 4) return 'HIGH';
    return 'CRITICAL';
  }

  /**
   * Check if IP address should be blocked
   * @param {string} ip_address - IP address to check
   * @returns {Promise<boolean>} Whether IP should be blocked
   */
  static async shouldBlockIP(ip_address) {
    try {
      // In production, this would check against a blocklist
      // For now, just return false
      return false;
    } catch (error) {
      logError('IP_BLOCK_CHECK_ERROR', error, { ip_address });
      return false;
    }
  }
}

module.exports = SecurityMonitor;