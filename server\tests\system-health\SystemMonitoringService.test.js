/**
 * Unit tests for SystemMonitoringService
 * 
 * Tests the core functionality of the system health monitoring service
 * including shift, assignment, trip, and database health monitoring.
 */

const SystemMonitoringService = require('../../services/SystemMonitoringService');
const db = require('../../config/database');

// Mock dependencies
jest.mock('../../config/database', () => ({
  query: jest.fn(),
  pool: {
    query: jest.fn()
  }
}));

// Mock removed - module doesn't exist

describe('SystemMonitoringService', () => {
  let systemMonitoringService;
  
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Create instance of service
    systemMonitoringService = new SystemMonitoringService();
  });
  
  describe('getSystemHealth', () => {
    it('should return health status for all modules', async () => {
      // Mock implementation for individual health checks
      systemMonitoringService.getShiftManagementHealth = jest.fn().mockResolvedValue({
        status: 'operational',
        issues: [],
        lastCheck: new Date(),
        metrics: { totalShifts: 4, activeShifts: 2 }
      });
      
      systemMonitoringService.getAssignmentManagementHealth = jest.fn().mockResolvedValue({
        status: 'warning',
        issues: [{ id: 'assign-001', type: 'sync_issue', severity: 'medium' }],
        lastCheck: new Date(),
        metrics: { totalAssignments: 10, problemAssignments: 1 }
      });
      
      systemMonitoringService.getTripMonitoringHealth = jest.fn().mockResolvedValue({
        status: 'operational',
        issues: [],
        lastCheck: new Date(),
        metrics: { totalTrips: 20, activeTrips: 5 }
      });
      
      systemMonitoringService.getDatabaseHealth = jest.fn().mockResolvedValue({
        status: 'operational',
        issues: [],
        lastCheck: new Date(),
        metrics: { connectionPoolUsage: 25, avgQueryTime: 12 }
      });
      
      // Call the method
      const result = await systemMonitoringService.getSystemHealth();
      
      // Assertions
      expect(result).toHaveProperty('shifts');
      expect(result).toHaveProperty('assignments');
      expect(result).toHaveProperty('trips');
      expect(result).toHaveProperty('database');
      expect(result).toHaveProperty('overall');
      
      // Verify overall status is derived from module with worst status
      expect(result.overall.status).toBe('warning');
      
      // Verify all methods were called
      expect(systemMonitoringService.getShiftManagementHealth).toHaveBeenCalled();
      expect(systemMonitoringService.getAssignmentManagementHealth).toHaveBeenCalled();
      expect(systemMonitoringService.getTripMonitoringHealth).toHaveBeenCalled();
      expect(systemMonitoringService.getDatabaseHealth).toHaveBeenCalled();
    });
    
    it('should handle errors in individual module checks', async () => {
      // Mock implementation with one failing module
      systemMonitoringService.getShiftManagementHealth = jest.fn().mockResolvedValue({
        status: 'operational',
        issues: [],
        lastCheck: new Date()
      });
      
      systemMonitoringService.getAssignmentManagementHealth = jest.fn().mockRejectedValue(
        new Error('Database connection failed')
      );
      
      systemMonitoringService.getTripMonitoringHealth = jest.fn().mockResolvedValue({
        status: 'operational',
        issues: [],
        lastCheck: new Date()
      });
      
      systemMonitoringService.getDatabaseHealth = jest.fn().mockResolvedValue({
        status: 'operational',
        issues: [],
        lastCheck: new Date()
      });
      
      // Call the method
      const result = await systemMonitoringService.getSystemHealth();
      
      // Assertions
      expect(result.assignments.status).toBe('critical');
      expect(result.assignments.issues[0].type).toBe('service_error');
      expect(result.overall.status).toBe('critical');
    });
  });
  
  describe('getShiftManagementHealth', () => {
    it('should return operational status when no issues detected', async () => {
      // Mock the monitor-shift-status script response
      const monitorShiftStatus = require('../../scripts/monitor-shift-status').monitorShiftStatus;
      monitorShiftStatus.mockResolvedValue({
        status: 'ok',
        activeShifts: 2,
        scheduledShifts: 2,
        completedShifts: 0,
        issues: []
      });
      
      // Call the method
      const result = await systemMonitoringService.getShiftManagementHealth();
      
      // Assertions
      expect(result.status).toBe('operational');
      expect(result.issues).toHaveLength(0);
      expect(result.metrics).toEqual({
        activeShifts: 2,
        scheduledShifts: 2,
        completedShifts: 0,
        totalShifts: 4
      });
    });
    
    it('should return warning status when minor issues detected', async () => {
      // Mock the monitor-shift-status script response
      const monitorShiftStatus = require('../../scripts/monitor-shift-status').monitorShiftStatus;
      monitorShiftStatus.mockResolvedValue({
        status: 'warning',
        activeShifts: 2,
        scheduledShifts: 2,
        completedShifts: 0,
        issues: [
          {
            type: 'status_mismatch',
            severity: 'medium',
            description: 'Night shift not activated for DT-100',
            affectedRecords: ['shift_id_123'],
            autoFixable: true
          }
        ]
      });
      
      // Call the method
      const result = await systemMonitoringService.getShiftManagementHealth();
      
      // Assertions
      expect(result.status).toBe('warning');
      expect(result.issues).toHaveLength(1);
      expect(result.issues[0].type).toBe('status_mismatch');
      expect(result.issues[0].autoFixable).toBe(true);
    });
    
    it('should return critical status when major issues detected', async () => {
      // Mock the monitor-shift-status script response
      const monitorShiftStatus = require('../../scripts/monitor-shift-status').monitorShiftStatus;
      monitorShiftStatus.mockResolvedValue({
        status: 'critical',
        activeShifts: 0,
        scheduledShifts: 4,
        completedShifts: 0,
        issues: [
          {
            type: 'activation_failure',
            severity: 'critical',
            description: 'No active shifts during business hours',
            affectedRecords: ['shift_id_123', 'shift_id_124'],
            autoFixable: true
          }
        ]
      });
      
      // Call the method
      const result = await systemMonitoringService.getShiftManagementHealth();
      
      // Assertions
      expect(result.status).toBe('critical');
      expect(result.issues).toHaveLength(1);
      expect(result.issues[0].severity).toBe('critical');
    });
    
    it('should handle errors from monitoring script', async () => {
      // Mock the monitor-shift-status script to throw an error
      const monitorShiftStatus = require('../../scripts/monitor-shift-status').monitorShiftStatus;
      monitorShiftStatus.mockRejectedValue(new Error('Script execution failed'));
      
      // Call the method
      const result = await systemMonitoringService.getShiftManagementHealth();
      
      // Assertions
      expect(result.status).toBe('critical');
      expect(result.issues).toHaveLength(1);
      expect(result.issues[0].type).toBe('monitoring_error');
      expect(result.issues[0].description).toContain('Script execution failed');
    });
  });
  
  describe('getAssignmentManagementHealth', () => {
    it('should return operational status when assignments are properly synchronized', async () => {
      // Mock database response for assignments
      db.pool.query.mockResolvedValueOnce({
        rows: [
          { truck_id: 'DT-100', driver_id: 'D-001', shift_status: 'active', display_status: 'Active' },
          { truck_id: 'DT-101', driver_id: 'D-002', shift_status: 'active', display_status: 'Active' }
        ]
      });
      
      // Call the method
      const result = await systemMonitoringService.getAssignmentManagementHealth();
      
      // Assertions
      expect(result.status).toBe('operational');
      expect(result.issues).toHaveLength(0);
      expect(result.metrics.totalAssignments).toBe(2);
      expect(result.metrics.syncedAssignments).toBe(2);
    });
    
    it('should return warning status when assignment display issues detected', async () => {
      // Mock database response with display issues
      db.pool.query.mockResolvedValueOnce({
        rows: [
          { truck_id: 'DT-100', driver_id: 'D-001', shift_status: 'active', display_status: 'Active' },
          { truck_id: 'DT-101', driver_id: 'D-002', shift_status: 'active', display_status: 'No Active Shift' }
        ]
      });
      
      // Call the method
      const result = await systemMonitoringService.getAssignmentManagementHealth();
      
      // Assertions
      expect(result.status).toBe('warning');
      expect(result.issues).toHaveLength(1);
      expect(result.issues[0].type).toBe('display_mismatch');
      expect(result.issues[0].affectedRecords).toContain('DT-101');
      expect(result.metrics.syncedAssignments).toBe(1);
      expect(result.metrics.unsyncedAssignments).toBe(1);
    });
  });
  
  describe('getTripMonitoringHealth', () => {
    it('should return operational status when trip workflows are valid', async () => {
      // Mock database response for trips
      db.pool.query.mockResolvedValueOnce({
        rows: [
          { trip_id: 'T-001', status: 'COMPLETED', workflow_valid: true },
          { trip_id: 'T-002', status: 'IN_PROGRESS', workflow_valid: true }
        ]
      });
      
      // Call the method
      const result = await systemMonitoringService.getTripMonitoringHealth();
      
      // Assertions
      expect(result.status).toBe('operational');
      expect(result.issues).toHaveLength(0);
      expect(result.metrics.totalTrips).toBe(2);
      expect(result.metrics.validWorkflows).toBe(2);
    });
    
    it('should return warning status when trip workflow issues detected', async () => {
      // Mock database response with workflow issues
      db.pool.query.mockResolvedValueOnce({
        rows: [
          { trip_id: 'T-001', status: 'COMPLETED', workflow_valid: true },
          { trip_id: 'T-002', status: 'VERIFIED', workflow_valid: false, issue: 'Missing COMPLETED state' }
        ]
      });
      
      // Call the method
      const result = await systemMonitoringService.getTripMonitoringHealth();
      
      // Assertions
      expect(result.status).toBe('warning');
      expect(result.issues).toHaveLength(1);
      expect(result.issues[0].type).toBe('workflow_violation');
      expect(result.issues[0].affectedRecords).toContain('T-002');
      expect(result.metrics.validWorkflows).toBe(1);
      expect(result.metrics.invalidWorkflows).toBe(1);
    });
  });
  
  describe('getDatabaseHealth', () => {
    it('should return operational status when database metrics are within thresholds', async () => {
      // Mock database health metrics
      db.pool.query.mockResolvedValueOnce({
        rows: [{ connection_count: 5, max_connections: 20 }]
      }).mockResolvedValueOnce({
        rows: [
          { table_name: 'shifts', dead_tuples: 100, live_tuples: 9900 },
          { table_name: 'trips', dead_tuples: 200, live_tuples: 9800 }
        ]
      }).mockResolvedValueOnce({
        rows: [{ avg_query_time: 15 }]
      });
      
      // Call the method
      const result = await systemMonitoringService.getDatabaseHealth();
      
      // Assertions
      expect(result.status).toBe('operational');
      expect(result.issues).toHaveLength(0);
      expect(result.metrics.connectionPoolUsage).toBe(25); // 5/20 * 100
      expect(result.metrics.avgQueryTime).toBe(15);
      expect(result.metrics.tables).toHaveLength(2);
    });
    
    it('should return warning status when connection pool usage is high', async () => {
      // Mock database health metrics with high connection usage
      db.pool.query.mockResolvedValueOnce({
        rows: [{ connection_count: 16, max_connections: 20 }]
      }).mockResolvedValueOnce({
        rows: [
          { table_name: 'shifts', dead_tuples: 100, live_tuples: 9900 },
          { table_name: 'trips', dead_tuples: 200, live_tuples: 9800 }
        ]
      }).mockResolvedValueOnce({
        rows: [{ avg_query_time: 15 }]
      });
      
      // Call the method
      const result = await systemMonitoringService.getDatabaseHealth();
      
      // Assertions
      expect(result.status).toBe('warning');
      expect(result.issues).toHaveLength(1);
      expect(result.issues[0].type).toBe('high_connection_usage');
      expect(result.metrics.connectionPoolUsage).toBe(80); // 16/20 * 100
    });
    
    it('should return warning status when dead tuple ratio is high', async () => {
      // Mock database health metrics with high dead tuple ratio
      db.pool.query.mockResolvedValueOnce({
        rows: [{ connection_count: 5, max_connections: 20 }]
      }).mockResolvedValueOnce({
        rows: [
          { table_name: 'shifts', dead_tuples: 100, live_tuples: 9900 },
          { table_name: 'trips', dead_tuples: 2000, live_tuples: 8000 }
        ]
      }).mockResolvedValueOnce({
        rows: [{ avg_query_time: 15 }]
      });
      
      // Call the method
      const result = await systemMonitoringService.getDatabaseHealth();
      
      // Assertions
      expect(result.status).toBe('warning');
      expect(result.issues).toHaveLength(1);
      expect(result.issues[0].type).toBe('high_dead_tuple_ratio');
      expect(result.issues[0].affectedRecords).toContain('trips');
    });
    
    it('should return critical status when query times are excessive', async () => {
      // Mock database health metrics with slow queries
      db.pool.query.mockResolvedValueOnce({
        rows: [{ connection_count: 5, max_connections: 20 }]
      }).mockResolvedValueOnce({
        rows: [
          { table_name: 'shifts', dead_tuples: 100, live_tuples: 9900 },
          { table_name: 'trips', dead_tuples: 200, live_tuples: 9800 }
        ]
      }).mockResolvedValueOnce({
        rows: [{ avg_query_time: 550 }]
      });
      
      // Call the method
      const result = await systemMonitoringService.getDatabaseHealth();
      
      // Assertions
      expect(result.status).toBe('critical');
      expect(result.issues).toHaveLength(1);
      expect(result.issues[0].type).toBe('slow_queries');
      expect(result.issues[0].severity).toBe('critical');
    });
  });
  
  describe('checkShiftTransition', () => {
    it('should detect and report shift transition issues', async () => {
      // Mock the checkShiftTransitions function
      const checkShiftTransitions = require('../../scripts/monitor-shift-status').checkShiftTransitions;
      checkShiftTransitions.mockResolvedValue({
        transitionTime: '18:00',
        expectedTransitions: 4,
        actualTransitions: 2,
        missedTransitions: [
          { shift_id: 'shift_123', truck_id: 'DT-100' },
          { shift_id: 'shift_124', truck_id: 'DT-101' }
        ]
      });
      
      // Call the method
      const result = await systemMonitoringService.checkShiftTransition();
      
      // Assertions
      expect(result.status).toBe('warning');
      expect(result.issues).toHaveLength(1);
      expect(result.issues[0].type).toBe('missed_transition');
      expect(result.issues[0].affectedRecords).toHaveLength(2);
      expect(result.metrics.transitionEfficiency).toBe(50); // 2/4 * 100
    });
    
    it('should return operational status when transitions are complete', async () => {
      // Mock the checkShiftTransitions function
      const checkShiftTransitions = require('../../scripts/monitor-shift-status').checkShiftTransitions;
      checkShiftTransitions.mockResolvedValue({
        transitionTime: '18:00',
        expectedTransitions: 4,
        actualTransitions: 4,
        missedTransitions: []
      });
      
      // Call the method
      const result = await systemMonitoringService.checkShiftTransition();
      
      // Assertions
      expect(result.status).toBe('operational');
      expect(result.issues).toHaveLength(0);
      expect(result.metrics.transitionEfficiency).toBe(100);
    });
  });
});