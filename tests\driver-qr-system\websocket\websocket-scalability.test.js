const WebSocket = require('ws');
const { initializeWebSocket, notifyDriverConnected, notifyDriverDisconnected, notifyDriverHandover } = require('../../../server/websocket');
const http = require('http');

// Test configuration constants
const TEST_CONFIG = {
  MAX_CLIENTS: 10,
  CONNECTION_TIMEOUT: 5000,
  AUTH_TIMEOUT: 3000,
  MESSAGE_WAIT_TIMEOUT: 100,
  HIGH_FREQUENCY_NOTIFICATIONS: 50,
  HIGH_FREQUENCY_WAIT: 500
};

// Test data factory
const createTestData = {
  driver: (id = 1, employeeId = 'DR-001', name = '<PERSON>') => ({
    id,
    employee_id: employeeId,
    full_name: name
  }),
  
  truck: (id = 1, truckNumber = 'DT-100', licensePlate = 'ABC-123') => ({
    id,
    truck_number: truckNumber,
    license_plate: licensePlate
  }),
  
  shift: (shiftId = 1, checkInTime = new Date().toISOString()) => ({
    shift_id: shiftId,
    check_in_time: checkInTime
  })
};

// WebSocket test helper class
class WebSocketTestHelper {
  constructor(server) {
    this.server = server;
    this.clients = [];
  }

  async createAuthenticatedClient(userId, role) {
    const client = await this.createWebSocketClient();
    await this.authenticateClient(client, userId, role);
    return client;
  }

  async createWebSocketClient() {
    return new Promise((resolve, reject) => {
      const port = this.server.address().port;
      const client = new WebSocket(`ws://localhost:${port}`);
      
      const timeout = setTimeout(() => {
        reject(new Error('WebSocket connection timeout'));
      }, TEST_CONFIG.CONNECTION_TIMEOUT);
      
      client.on('open', () => {
        clearTimeout(timeout);
        this.clients.push(client);
        resolve(client);
      });
      
      client.on('error', (error) => {
        clearTimeout(timeout);
        reject(error);
      });
    });
  }

  async authenticateClient(client, userId, role) {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Authentication timeout'));
      }, TEST_CONFIG.AUTH_TIMEOUT);

      client.on('message', (data) => {
        try {
          const message = JSON.parse(data.toString());
          if (message.type === 'auth_success') {
            clearTimeout(timeout);
            resolve();
          }
        } catch (error) {
          clearTimeout(timeout);
          reject(error);
        }
      });
      
      client.send(JSON.stringify({
        type: 'auth',
        userId,
        role
      }));
    });
  }

  cleanup() {
    this.clients.forEach(client => {
      if (client.readyState === WebSocket.OPEN) {
        client.close();
      }
    });
    this.clients = [];
  }
}

describe('Driver QR WebSocket Scalability Tests', () => {
  let server;
  let wss;
  let testHelper;
  let clients = []; // Declare clients array

  beforeAll(async () => {
    // Create HTTP server for testing
    server = http.createServer();
    wss = initializeWebSocket(server);
    
    // Start server on random port
    await new Promise((resolve) => {
      server.listen(0, resolve);
    });
  });

  afterAll(async () => {
    // Close all client connections
    clients.forEach(client => {
      if (client.readyState === WebSocket.OPEN) {
        client.close();
      }
    });
    
    // Close server
    if (server) {
      await new Promise((resolve) => {
        server.close(resolve);
      });
    }
  });

  afterEach(() => {
    // Clean up clients after each test
    clients.forEach(client => {
      if (client.readyState === WebSocket.OPEN) {
        client.close();
      }
    });
    clients = [];
  });

  const createWebSocketClient = () => {
    return new Promise((resolve, reject) => {
      const port = server.address().port;
      const client = new WebSocket(`ws://localhost:${port}`);
      
      client.on('open', () => {
        clients.push(client);
        resolve(client);
      });
      
      client.on('error', reject);
      
      // Set timeout for connection
      setTimeout(() => {
        reject(new Error('WebSocket connection timeout'));
      }, 5000);
    });
  };

  const authenticateClient = (client, userId, role) => {
    return new Promise((resolve) => {
      client.on('message', (data) => {
        const message = JSON.parse(data.toString());
        if (message.type === 'auth_success') {
          resolve();
        }
      });
      
      client.send(JSON.stringify({
        type: 'auth',
        userId: userId,
        role: role
      }));
    });
  };

  test('should handle multiple concurrent driver connections', async () => {
    const numClients = 10;
    const connectedClients = [];

    // Create multiple WebSocket clients
    for (let i = 0; i < numClients; i++) {
      const client = await createWebSocketClient();
      await authenticateClient(client, `user_${i}`, 'admin');
      connectedClients.push(client);
    }

    expect(connectedClients).toHaveLength(numClients);

    // Test broadcasting driver connect event
    const driverData = {
      id: 1,
      employee_id: 'DR-001',
      full_name: 'John Doe'
    };

    const truckData = {
      id: 1,
      truck_number: 'DT-100',
      license_plate: 'ABC-123'
    };

    const shiftData = {
      shift_id: 1,
      check_in_time: new Date().toISOString()
    };

    // Set up message listeners
    const receivedMessages = [];
    connectedClients.forEach((client, index) => {
      client.on('message', (data) => {
        const message = JSON.parse(data.toString());
        if (message.type === 'driver_connected') {
          receivedMessages.push({ clientIndex: index, message });
        }
      });
    });

    // Send notification
    notifyDriverConnected(driverData, truckData, shiftData);

    // Wait for messages to be received
    await new Promise(resolve => setTimeout(resolve, 100));

    // Verify all clients received the message
    expect(receivedMessages).toHaveLength(numClients);
    
    receivedMessages.forEach(({ message }) => {
      expect(message.type).toBe('driver_connected');
      expect(message.title).toBe('Driver Checked In');
      expect(message.data.driver.employee_id).toBe('DR-001');
      expect(message.data.truck.truck_number).toBe('DT-100');
    });
  }, 10000);

  test('should handle driver handover notifications', async () => {
    const client = await createWebSocketClient();
    await authenticateClient(client, 'supervisor_1', 'supervisor');

    const receivedMessages = [];
    client.on('message', (data) => {
      const message = JSON.parse(data.toString());
      if (message.type === 'driver_handover') {
        receivedMessages.push(message);
      }
    });

    const previousDriverData = {
      id: 1,
      employee_id: 'DR-001',
      full_name: 'John Doe'
    };

    const newDriverData = {
      id: 2,
      employee_id: 'DR-002',
      full_name: 'Jane Smith'
    };

    const truckData = {
      id: 1,
      truck_number: 'DT-100',
      license_plate: 'ABC-123'
    };

    const handoverData = {
      previous_shift_id: 1,
      shift_id: 2,
      check_in_time: new Date().toISOString()
    };

    // Send handover notification
    notifyDriverHandover(previousDriverData, newDriverData, truckData, handoverData);

    // Wait for message
    await new Promise(resolve => setTimeout(resolve, 100));

    expect(receivedMessages).toHaveLength(1);
    const message = receivedMessages[0];
    
    expect(message.type).toBe('driver_handover');
    expect(message.title).toBe('Driver Handover');
    expect(message.data.previous_driver.employee_id).toBe('DR-001');
    expect(message.data.new_driver.employee_id).toBe('DR-002');
    expect(message.data.truck.truck_number).toBe('DT-100');
    expect(message.priority).toBe('high');
  });

  test('should handle driver disconnect notifications', async () => {
    const client = await createWebSocketClient();
    await authenticateClient(client, 'admin_1', 'admin');

    const receivedMessages = [];
    client.on('message', (data) => {
      const message = JSON.parse(data.toString());
      if (message.type === 'driver_disconnected') {
        receivedMessages.push(message);
      }
    });

    const driverData = {
      id: 1,
      employee_id: 'DR-001',
      full_name: 'John Doe'
    };

    const truckData = {
      id: 1,
      truck_number: 'DT-100',
      license_plate: 'ABC-123'
    };

    const shiftData = {
      shift_id: 1,
      check_in_time: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(), // 8 hours ago
      check_out_time: new Date().toISOString(),
      duration: '8h 0m',
      duration_ms: 8 * 60 * 60 * 1000
    };

    // Send disconnect notification
    notifyDriverDisconnected(driverData, truckData, shiftData);

    // Wait for message
    await new Promise(resolve => setTimeout(resolve, 100));

    expect(receivedMessages).toHaveLength(1);
    const message = receivedMessages[0];
    
    expect(message.type).toBe('driver_disconnected');
    expect(message.title).toBe('Driver Checked Out');
    expect(message.data.driver.employee_id).toBe('DR-001');
    expect(message.data.truck.truck_number).toBe('DT-100');
    expect(message.data.shift.duration).toBe('8h 0m');
    expect(message.icon).toBe('⏰');
  });

  test('should handle connection failures gracefully', async () => {
    const client = await createWebSocketClient();
    await authenticateClient(client, 'user_1', 'admin');

    // Close the client connection
    client.close();

    // Wait for connection to close
    await new Promise(resolve => setTimeout(resolve, 100));

    // Try to send notification (should not throw error)
    const driverData = {
      id: 1,
      employee_id: 'DR-001',
      full_name: 'John Doe'
    };

    const truckData = {
      id: 1,
      truck_number: 'DT-100',
      license_plate: 'ABC-123'
    };

    const shiftData = {
      shift_id: 1,
      check_in_time: new Date().toISOString()
    };

    // This should not throw an error even with closed connection
    expect(() => {
      notifyDriverConnected(driverData, truckData, shiftData);
    }).not.toThrow();
  });

  test('should handle high-frequency notifications', async () => {
    const client = await createWebSocketClient();
    await authenticateClient(client, 'admin_1', 'admin');

    const receivedMessages = [];
    client.on('message', (data) => {
      const message = JSON.parse(data.toString());
      if (message.type === 'driver_connected') {
        receivedMessages.push(message);
      }
    });

    // Send multiple rapid notifications
    const numNotifications = 50;
    for (let i = 0; i < numNotifications; i++) {
      const driverData = {
        id: i + 1,
        employee_id: `DR-${String(i + 1).padStart(3, '0')}`,
        full_name: `Driver ${i + 1}`
      };

      const truckData = {
        id: i + 1,
        truck_number: `DT-${String(i + 1).padStart(3, '0')}`,
        license_plate: `ABC-${String(i + 1).padStart(3, '0')}`
      };

      const shiftData = {
        shift_id: i + 1,
        check_in_time: new Date().toISOString()
      };

      notifyDriverConnected(driverData, truckData, shiftData);
    }

    // Wait for all messages to be received
    await new Promise(resolve => setTimeout(resolve, 500));

    expect(receivedMessages).toHaveLength(numNotifications);
    
    // Verify message order and content
    receivedMessages.forEach((message, index) => {
      expect(message.data.driver.employee_id).toBe(`DR-${String(index + 1).padStart(3, '0')}`);
      expect(message.data.truck.truck_number).toBe(`DT-${String(index + 1).padStart(3, '0')}`);
    });
  }, 15000);

  test('should maintain connection heartbeat', async () => {
    const client = await createWebSocketClient();
    await authenticateClient(client, 'user_1', 'admin');

    let pongReceived = false;
    client.on('message', (data) => {
      const message = JSON.parse(data.toString());
      if (message.type === 'pong') {
        pongReceived = true;
      }
    });

    // Send ping
    client.send(JSON.stringify({ type: 'ping' }));

    // Wait for pong response
    await new Promise(resolve => setTimeout(resolve, 100));

    expect(pongReceived).toBe(true);
  });
});