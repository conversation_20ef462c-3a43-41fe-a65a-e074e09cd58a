# Role-Based Access Control (RBAC) Implementation Guide

## Overview

The Hauling QR Trip Management System includes a comprehensive Role-Based Access Control (RBAC) system that allows administrators to manage user roles and configure granular page access permissions. This system provides secure, flexible access control while maintaining compatibility with the existing authentication infrastructure.

## Architecture

### Database Design

The RBAC system uses a simplified two-table approach that integrates with the existing `user_role` enum:

#### 1. Enhanced user_role enum
- Maintains existing enum structure for backward compatibility
- Adds PostgreSQL functions for safe CRUD operations on enum values
- Supports dynamic role creation and deletion

#### 2. role_permissions table
```sql
CREATE TABLE role_permissions (
    id SERIAL PRIMARY KEY,
    role_name user_role NOT NULL,
    page_key VARCHAR(100) NOT NULL,
    has_access BOOLEAN DEFAULT false,
    UNIQUE(role_name, page_key)
);
```

### System Components

#### Backend Components
- **`server/routes/roles.js`**: Role management API endpoints
- **`server/routes/permissions.js`**: Permission management API endpoints
- **`server/middleware/permissions.js`**: Route protection middleware
- **`database/migrations/017_role_based_access_control.sql`**: Database schema migration

#### Frontend Components
- **`client/src/pages/settings/components/UserRolesManagement.js`**: Main RBAC management interface
- **`client/src/hooks/usePermissions.js`**: Permission checking hook
- **Modified components**: Settings.js, Sidebar.js, DashboardLayout.js, AuthContext.js

## Implementation Plan

### Phase 1: Database Foundation
- [x] **Requirements Analysis**: Comprehensive requirements document completed
- [x] **System Design**: Architecture and database schema designed
- [ ] **Database Migration**: Create `017_role_based_access_control.sql`
  - Create role_permissions table
  - Add PostgreSQL functions for enum management
  - Insert default permissions for existing roles

### Phase 2: Backend API Development
- [ ] **Role Management API**: Create `server/routes/roles.js`
  - `GET /api/roles` - Get all user_role enum values
  - `POST /api/roles` - Add new role to enum
  - `DELETE /api/roles/:name` - Remove role (with safety checks)
- [ ] **Permission Management API**: Create `server/routes/permissions.js`
  - `GET /api/permissions` - Get all role-page permissions
  - `POST /api/permissions` - Bulk update permissions
- [ ] **Route Protection**: Create `server/middleware/permissions.js`
- [ ] **Server Integration**: Update `server/server.js` with new routes

### Phase 3: Frontend Interface Development
- [ ] **UserRolesManagement Component**: Create main RBAC interface
  - Role CRUD operations with user count validation
  - Permission matrix with checkbox interface
  - Integration with Settings page
- [ ] **Permission Hook**: Create `client/src/hooks/usePermissions.js`
- [ ] **Navigation Protection**: Update Sidebar.js to hide unauthorized menu items
- [ ] **Route Protection**: Update DashboardLayout.js with route guards

### Phase 4: Authentication Integration
- [ ] **Auth Enhancement**: Update `server/routes/auth.js` to include permissions in login response
- [ ] **Context Updates**: Enhance `client/src/context/AuthContext.js` with permission checking
- [ ] **Testing**: Comprehensive testing of role CRUD and permission enforcement

## Key Features

### Role Management
- **Dynamic Role Creation**: Create new user role types through the interface
- **Safe Role Deletion**: Prevents deletion of roles assigned to users
- **Role Editing**: Modify existing role names
- **User Count Validation**: Shows number of users assigned to each role

### Permission Management
- **Page-Level Permissions**: Control access to specific application pages
- **Checkbox Matrix Interface**: Intuitive permission assignment
- **Bulk Updates**: Save multiple permission changes simultaneously
- **Real-time Validation**: Immediate feedback on permission changes

### Access Control Enforcement
- **Frontend Route Protection**: Prevent unauthorized page access
- **Backend API Protection**: Secure API endpoints based on user roles
- **Navigation Filtering**: Hide menu items for unauthorized pages
- **Direct URL Protection**: Block direct URL access attempts

## Available Pages for Permission Control

The system supports granular permissions for the following pages:

| Page Key | Display Name | Description |
|----------|--------------|-------------|
| `dashboard` | Dashboard | Main system dashboard and overview |
| `users` | User Management | User administration and management |
| `trips` | Trip Monitoring | Trip tracking and management |
| `assignments` | Assignment Management | Assignment creation and tracking |
| `shifts` | Shift Management | Shift scheduling and management |
| `analytics` | Analytics | Performance metrics and reporting |
| `settings` | Settings | System configuration and admin tools |

### Settings Subpages
- User Management
- Shift Management
- System Health Monitor
- Manual Shift Management
- Appearance Settings
- Role Management (admin only)

## API Endpoints

### Role Management
```
GET    /api/roles              - Get all user_role enum values
POST   /api/roles              - Add new role to enum
DELETE /api/roles/:name        - Remove role (safety checks applied)
```

### Permission Management
```
GET    /api/permissions        - Get all role-page permissions
POST   /api/permissions        - Bulk update role permissions
```

### Request/Response Examples

#### Get All Roles
```javascript
// GET /api/roles
{
  "success": true,
  "data": [
    { "name": "admin", "userCount": 2, "canDelete": false },
    { "name": "supervisor", "userCount": 5, "canDelete": true },
    { "name": "operator", "userCount": 15, "canDelete": true }
  ]
}
```

#### Get Permissions
```javascript
// GET /api/permissions
{
  "success": true,
  "data": {
    "admin": {
      "dashboard": true,
      "users": true,
      "trips": true,
      "assignments": true,
      "shifts": true,
      "analytics": true,
      "settings": true
    },
    "supervisor": {
      "dashboard": true,
      "users": false,
      "trips": true,
      "assignments": true,
      "shifts": true,
      "analytics": true,
      "settings": false
    }
  }
}
```

## Security Considerations

### Access Control
- **Admin-Only Management**: Only admin users can access role management interface
- **Role Assignment Safety**: Prevents deletion of roles with assigned users
- **Permission Validation**: Server-side validation of all permission changes
- **JWT Integration**: Seamless integration with existing authentication system

### Data Protection
- **SQL Injection Prevention**: Parameterized queries for all database operations
- **Input Validation**: Comprehensive validation using Joi schemas
- **Error Handling**: Secure error messages without sensitive information exposure
- **Audit Trail**: Logging of all role and permission changes

## Usage Guide

### Accessing Role Management

1. **Login as Administrator**: Only admin users can access role management
2. **Navigate to Settings**: Click Settings from the main navigation menu
3. **Select User Roles**: Click on the "👥 User Roles" section
4. **Manage Roles and Permissions**: Use the interface to create, edit, and configure roles

### Creating a New Role

1. **Click "Add Role Type"**: Opens the role creation form
2. **Enter Role Name**: Provide a unique, descriptive role name
3. **Configure Permissions**: Use checkboxes to set page access permissions
4. **Save Changes**: Click "Save Settings" to create the role
5. **Assign to Users**: New role becomes available in the Users page

### Editing Role Permissions

1. **Select Existing Role**: Click on a role in the management interface
2. **Modify Permissions**: Check/uncheck boxes for page access
3. **Save Changes**: Click "Save Settings" to apply new permissions
4. **Immediate Effect**: Changes apply immediately without requiring user re-login

### Deleting a Role

1. **Check User Assignment**: System shows number of users assigned to each role
2. **Click Delete**: Only available for roles with no assigned users
3. **Confirm Deletion**: Confirm the deletion in the dialog
4. **Automatic Cleanup**: Role is removed from enum and permission tables

## Testing and Validation

### Manual Testing Checklist

#### Role Management
- [ ] Create new role with unique name
- [ ] Attempt to create role with duplicate name (should fail)
- [ ] Edit existing role name
- [ ] Delete role with no assigned users
- [ ] Attempt to delete role with assigned users (should be prevented)

#### Permission Management
- [ ] Set permissions for new role
- [ ] Modify permissions for existing role
- [ ] Save permission changes
- [ ] Verify permissions persist after page refresh

#### Access Control
- [ ] Login with different role types
- [ ] Verify navigation menu shows only authorized pages
- [ ] Attempt direct URL access to unauthorized pages
- [ ] Test API endpoint protection with different roles

### Automated Testing

```bash
# Run RBAC-specific tests
cd server && npm test -- --grep "RBAC"

# Run full test suite
npm test

# Test with different user roles
npm run test:roles
```

## Troubleshooting

### Common Issues

#### Role Creation Fails
- **Check for duplicate names**: Role names must be unique
- **Verify admin permissions**: Only admin users can create roles
- **Database connection**: Ensure database is accessible

#### Permission Changes Not Applied
- **Clear browser cache**: Force refresh the application
- **Check JWT token**: Ensure user token is valid and current
- **Verify database updates**: Check role_permissions table directly

#### Navigation Items Still Visible
- **Check permission cache**: Clear localStorage and refresh
- **Verify role assignment**: Ensure user has correct role assigned
- **Check component updates**: Verify Sidebar.js is using permission hook

### Debug Commands

```bash
# Check database role permissions
psql -d hauling_qr_system -c "SELECT * FROM role_permissions;"

# Verify user role assignments
psql -d hauling_qr_system -c "SELECT username, role FROM users;"

# Check enum values
psql -d hauling_qr_system -c "SELECT enumlabel FROM pg_enum WHERE enumtypid = 'user_role'::regtype;"
```

## Future Enhancements

### Planned Features
- **Granular Permissions**: Sub-page and feature-level permissions
- **Role Templates**: Pre-configured role templates for common use cases
- **Permission Inheritance**: Hierarchical role structures
- **Audit Logging**: Comprehensive logging of role and permission changes
- **Bulk Operations**: Import/export role configurations
- **Time-based Permissions**: Temporary access grants with expiration

### Integration Opportunities
- **LDAP/Active Directory**: Enterprise authentication integration
- **Multi-tenant Support**: Organization-specific role management
- **API Key Management**: Role-based API access control
- **Workflow Permissions**: Role-based workflow step restrictions

## Support and Maintenance

### Regular Maintenance Tasks
- **Permission Audit**: Regular review of role assignments and permissions
- **User Role Cleanup**: Remove unused roles and consolidate similar roles
- **Performance Monitoring**: Monitor permission check performance
- **Security Review**: Regular security assessment of access controls

### Monitoring and Alerts
- **Failed Access Attempts**: Log and monitor unauthorized access attempts
- **Permission Changes**: Alert on role and permission modifications
- **User Role Changes**: Track user role assignment changes
- **System Health**: Monitor RBAC system performance and availability

For additional support or questions about the RBAC system, refer to the main project documentation or contact the development team.