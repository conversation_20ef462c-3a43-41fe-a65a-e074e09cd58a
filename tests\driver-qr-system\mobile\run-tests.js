#!/usr/bin/env node

/**
 * Mobile Test Runner
 * 
 * Validates mobile compatibility test setup and runs basic checks
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔍 Mobile Compatibility Test Runner');
console.log('=====================================\n');

// Check if required dependencies are available
function checkDependencies() {
  console.log('📦 Checking dependencies...');
  
  const requiredPackages = [
    'selenium-webdriver',
    'chromedriver',
    'geckodriver'
  ];
  
  const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, '../../../package.json'), 'utf8'));
  const devDeps = packageJson.devDependencies || {};
  
  const missing = requiredPackages.filter(pkg => !devDeps[pkg]);
  
  if (missing.length > 0) {
    console.log('❌ Missing dependencies:', missing.join(', '));
    console.log('💡 Run: npm install --save-dev', missing.join(' '));
    return false;
  }
  
  console.log('✅ All dependencies found\n');
  return true;
}

// Check if test files exist
function checkTestFiles() {
  console.log('📁 Checking test files...');
  
  const testFiles = [
    'mobile-compatibility.test.js',
    'jest.config.js',
    'setup.js',
    'README.md'
  ];
  
  const missing = testFiles.filter(file => 
    !fs.existsSync(path.join(__dirname, file))
  );
  
  if (missing.length > 0) {
    console.log('❌ Missing test files:', missing.join(', '));
    return false;
  }
  
  console.log('✅ All test files found\n');
  return true;
}

// Validate test configuration
function validateConfig() {
  console.log('⚙️  Validating configuration...');
  
  try {
    const config = require('./jest.config.js');
    
    if (!config.testEnvironment || config.testEnvironment !== 'node') {
      console.log('❌ Invalid test environment');
      return false;
    }
    
    if (!config.testTimeout || config.testTimeout < 30000) {
      console.log('❌ Test timeout too low for mobile tests');
      return false;
    }
    
    console.log('✅ Configuration valid\n');
    return true;
  } catch (error) {
    console.log('❌ Configuration error:', error.message);
    return false;
  }
}

// Check if application is running
function checkApplication() {
  console.log('🌐 Checking application availability...');
  
  const testUrl = process.env.TEST_BASE_URL || 'http://localhost:3000';
  
  try {
    // Simple check - in real scenario, you'd make an HTTP request
    console.log(`📍 Target URL: ${testUrl}`);
    console.log('💡 Make sure the application is running before executing tests');
    console.log('   Run: npm run dev (in another terminal)\n');
    return true;
  } catch (error) {
    console.log('❌ Application check failed:', error.message);
    return false;
  }
}

// Display test execution instructions
function showInstructions() {
  console.log('🚀 Test Execution Instructions');
  console.log('==============================\n');
  
  console.log('1. Start the application:');
  console.log('   npm run dev\n');
  
  console.log('2. Run mobile tests:');
  console.log('   npm run test:mobile\n');
  
  console.log('3. Run specific test suites:');
  console.log('   npm run test:mobile -- --testNamePattern="Cross-Browser"');
  console.log('   npm run test:mobile -- --testNamePattern="Touch Interface"');
  console.log('   npm run test:mobile -- --testNamePattern="Camera Performance"\n');
  
  console.log('4. Run with coverage:');
  console.log('   npm run test:mobile:coverage\n');
  
  console.log('📋 Test Categories:');
  console.log('   • Cross-Browser QR Scanning Tests');
  console.log('   • Portrait and Landscape Mode Compatibility');
  console.log('   • Touch Interface Requirements');
  console.log('   • Camera Performance and Lighting Conditions');
  console.log('   • Performance and Responsiveness');
  console.log('   • Accessibility and Usability\n');
}

// Main execution
function main() {
  const checks = [
    checkDependencies,
    checkTestFiles,
    validateConfig,
    checkApplication
  ];
  
  const results = checks.map(check => check());
  const allPassed = results.every(result => result);
  
  if (allPassed) {
    console.log('🎉 All checks passed! Ready to run mobile tests.\n');
    showInstructions();
  } else {
    console.log('❌ Some checks failed. Please fix the issues above before running tests.\n');
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = {
  checkDependencies,
  checkTestFiles,
  validateConfig,
  checkApplication
};