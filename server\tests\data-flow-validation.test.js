/**
 * Data Flow Validation Tests
 * 
 * Tests the complete data flow chain:
 * Shift Management → Assignment Management → Trip Monitoring
 * 
 * Validates that each system in the chain has access to accurate, 
 * up-to-date information from the previous system.
 */

const { getClient } = require('../config/database');
const DataFlowValidationService = require('../services/DataFlowValidationService');
const StatusSynchronizationService = require('../services/StatusSynchronizationService');
const EndToEndDataFlowTest = require('../utils/EndToEndDataFlowTest');
const DataFlowLogger = require('../utils/DataFlowLogger');

describe('Data Flow Validation', () => {
  let client;

  beforeAll(async () => {
    client = await getClient();
  });

  afterAll(async () => {
    if (client) {
      client.release();
    }
  });

  describe('DataFlowValidationService', () => {
    test('should validate complete data flow chain', async () => {
      const validationResults = await DataFlowValidationService.validateCompleteDataFlow();

      expect(validationResults).toHaveProperty('overall_status');
      expect(validationResults).toHaveProperty('issues');
      expect(validationResults).toHaveProperty('metrics');
      expect(validationResults).toHaveProperty('validation_steps');
      expect(validationResults).toHaveProperty('timestamp');

      expect(['operational', 'warning', 'critical']).toContain(validationResults.overall_status);
      expect(Array.isArray(validationResults.issues)).toBe(true);
      expect(typeof validationResults.metrics).toBe('object');

      // Validate that all required validation steps are present
      expect(validationResults.validation_steps).toHaveProperty('shift_to_assignment');
      expect(validationResults.validation_steps).toHaveProperty('assignment_to_trip');
      expect(validationResults.validation_steps).toHaveProperty('end_to_end');
      expect(validationResults.validation_steps).toHaveProperty('driver_capture');

      console.log('Data Flow Validation Results:', {
        overall_status: validationResults.overall_status,
        total_issues: validationResults.issues.length,
        metrics: validationResults.metrics
      });
    }, 30000);

    test('should provide validation queries for manual verification', () => {
      const queries = DataFlowValidationService.getValidationQueries();

      expect(queries).toHaveProperty('shift_to_assignment_consistency');
      expect(queries).toHaveProperty('assignment_to_trip_display');
      expect(queries).toHaveProperty('recent_trip_driver_capture');
      expect(queries).toHaveProperty('end_to_end_consistency');

      // Validate that queries are non-empty strings
      Object.values(queries).forEach(query => {
        expect(typeof query).toBe('string');
        expect(query.length).toBeGreaterThan(0);
        expect(query.toLowerCase()).toContain('select');
      });
    });
  });

  describe('StatusSynchronizationService', () => {
    test('should monitor status synchronization across all systems', async () => {
      const monitoringResults = await StatusSynchronizationService.monitorStatusSynchronization();

      expect(monitoringResults).toHaveProperty('overall_status');
      expect(monitoringResults).toHaveProperty('sync_issues');
      expect(monitoringResults).toHaveProperty('auto_fixes_applied');
      expect(monitoringResults).toHaveProperty('metrics');
      expect(monitoringResults).toHaveProperty('monitoring_duration_ms');

      expect(['operational', 'warning', 'critical']).toContain(monitoringResults.overall_status);
      expect(Array.isArray(monitoringResults.sync_issues)).toBe(true);
      expect(typeof monitoringResults.auto_fixes_applied).toBe('number');
      expect(typeof monitoringResults.metrics).toBe('object');

      console.log('Status Synchronization Results:', {
        overall_status: monitoringResults.overall_status,
        sync_issues: monitoringResults.sync_issues.length,
        auto_fixes_applied: monitoringResults.auto_fixes_applied,
        duration_ms: monitoringResults.monitoring_duration_ms
      });
    }, 30000);

    test('should create appropriate alerts for synchronization issues', async () => {
      const monitoringResults = await StatusSynchronizationService.monitorStatusSynchronization();
      const alerts = await StatusSynchronizationService.createSyncAlerts(monitoringResults);

      expect(Array.isArray(alerts)).toBe(true);

      alerts.forEach(alert => {
        expect(alert).toHaveProperty('type');
        expect(alert).toHaveProperty('priority');
        expect(alert).toHaveProperty('title');
        expect(alert).toHaveProperty('description');
        expect(alert).toHaveProperty('affected_systems');
        expect(alert).toHaveProperty('recommended_action');
        expect(alert).toHaveProperty('auto_executable');

        expect(['high', 'medium', 'low', 'info']).toContain(alert.priority);
        expect(Array.isArray(alert.affected_systems)).toBe(true);
        expect(typeof alert.auto_executable).toBe('boolean');
      });

      console.log('Generated Alerts:', alerts.length);
    });

    test('should generate comprehensive synchronization report', async () => {
      const monitoringResults = await StatusSynchronizationService.monitorStatusSynchronization();
      const report = StatusSynchronizationService.generateSyncReport(monitoringResults);

      expect(report).toHaveProperty('summary');
      expect(report).toHaveProperty('metrics');
      expect(report).toHaveProperty('issues_by_severity');
      expect(report).toHaveProperty('issues_by_system');
      expect(report).toHaveProperty('detailed_issues');
      expect(report).toHaveProperty('recommendations');

      expect(report.summary).toHaveProperty('overall_status');
      expect(report.summary).toHaveProperty('total_issues');
      expect(report.summary).toHaveProperty('auto_fixes_applied');

      expect(report.issues_by_severity).toHaveProperty('critical');
      expect(report.issues_by_severity).toHaveProperty('warning');
      expect(report.issues_by_severity).toHaveProperty('info');

      expect(Array.isArray(report.detailed_issues)).toBe(true);
      expect(Array.isArray(report.recommendations)).toBe(true);

      console.log('Synchronization Report Summary:', report.summary);
    });
  });

  describe('EndToEndDataFlowTest', () => {
    test('should run complete end-to-end data flow test', async () => {
      const testResults = await EndToEndDataFlowTest.runEndToEndTest({
        scenario: 'test_day_shift'
      });

      expect(testResults).toHaveProperty('overall_success');
      expect(testResults).toHaveProperty('steps');
      expect(testResults).toHaveProperty('issues');
      expect(testResults).toHaveProperty('metrics');
      expect(testResults).toHaveProperty('duration_ms');

      expect(typeof testResults.overall_success).toBe('boolean');
      expect(typeof testResults.steps).toBe('object');
      expect(Array.isArray(testResults.issues)).toBe(true);
      expect(typeof testResults.metrics).toBe('object');
      expect(typeof testResults.duration_ms).toBe('number');

      // Validate that all required test steps are present
      expect(testResults.steps).toHaveProperty('setup');
      expect(testResults.steps).toHaveProperty('driver_checkin');
      expect(testResults.steps).toHaveProperty('shift_verification');
      expect(testResults.steps).toHaveProperty('assignment_verification');
      expect(testResults.steps).toHaveProperty('trip_verification');
      expect(testResults.steps).toHaveProperty('consistency_verification');
      expect(testResults.steps).toHaveProperty('driver_checkout');

      console.log('End-to-End Test Results:', {
        overall_success: testResults.overall_success,
        total_steps: Object.keys(testResults.steps).length,
        successful_steps: testResults.metrics.successful_steps,
        total_issues: testResults.issues.length,
        duration_ms: testResults.duration_ms
      });

      // Log any issues found during testing
      if (testResults.issues.length > 0) {
        console.log('Test Issues Found:');
        testResults.issues.forEach((issue, index) => {
          console.log(`  ${index + 1}. [${issue.severity}] ${issue.description}`);
        });
      }
    }, 60000);

    test('should run multiple test scenarios', async () => {
      const scenarios = ['day_shift', 'night_shift'];
      const aggregatedResults = await EndToEndDataFlowTest.runMultipleScenarios(scenarios);

      expect(aggregatedResults).toHaveProperty('total_scenarios');
      expect(aggregatedResults).toHaveProperty('successful_scenarios');
      expect(aggregatedResults).toHaveProperty('failed_scenarios');
      expect(aggregatedResults).toHaveProperty('scenario_results');
      expect(aggregatedResults).toHaveProperty('overall_success');
      expect(aggregatedResults).toHaveProperty('summary');

      expect(aggregatedResults.total_scenarios).toBe(scenarios.length);
      expect(typeof aggregatedResults.overall_success).toBe('boolean');
      expect(typeof aggregatedResults.summary).toBe('object');

      expect(aggregatedResults.summary).toHaveProperty('success_rate');
      expect(aggregatedResults.summary).toHaveProperty('total_issues');
      expect(aggregatedResults.summary).toHaveProperty('average_consistency_score');

      console.log('Multiple Scenarios Test Results:', {
        total_scenarios: aggregatedResults.total_scenarios,
        successful_scenarios: aggregatedResults.successful_scenarios,
        success_rate: aggregatedResults.summary.success_rate,
        average_consistency_score: aggregatedResults.summary.average_consistency_score
      });
    }, 120000);
  });

  describe('Data Flow Integration Tests', () => {
    test('should validate shift to assignment data flow', async () => {
      const testClient = await getClient();
      
      try {
        await testClient.query('BEGIN');

        // Create test data
        const driverResult = await testClient.query(`
          SELECT id, employee_id, full_name FROM drivers WHERE status = 'active' LIMIT 1
        `);
        
        const truckResult = await testClient.query(`
          SELECT id, truck_number FROM dump_trucks WHERE status = 'active' LIMIT 1
        `);

        if (driverResult.rows.length === 0 || truckResult.rows.length === 0) {
          console.log('Skipping integration test - no test data available');
          await testClient.query('ROLLBACK');
          return;
        }

        const driver = driverResult.rows[0];
        const truck = truckResult.rows[0];

        // Clean up any existing shifts
        await testClient.query(`
          UPDATE driver_shifts SET status = 'completed', end_date = CURRENT_DATE, end_time = CURRENT_TIME
          WHERE truck_id = $1 AND status = 'active'
        `, [truck.id]);

        // Create active shift
        const shiftResult = await testClient.query(`
          INSERT INTO driver_shifts (
            truck_id, driver_id, shift_type, start_date, start_time, 
            status, auto_created, created_at, updated_at
          ) VALUES ($1, $2, 'day', CURRENT_DATE, CURRENT_TIME, 'active', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
          RETURNING id
        `, [truck.id, driver.id]);

        const shiftId = shiftResult.rows[0].id;

        // Verify shift was created
        const shiftVerification = await testClient.query(`
          SELECT * FROM driver_shifts WHERE id = $1
        `, [shiftId]);

        expect(shiftVerification.rows.length).toBe(1);
        expect(shiftVerification.rows[0].status).toBe('active');
        expect(shiftVerification.rows[0].auto_created).toBe(true);

        // Check if assignment exists and sync it
        let assignmentResult = await testClient.query(`
          SELECT id, driver_id FROM assignments 
          WHERE truck_id = $1 AND status IN ('assigned', 'in_progress')
          ORDER BY created_at DESC LIMIT 1
        `, [truck.id]);

        if (assignmentResult.rows.length === 0) {
          // Create test assignment
          const locationResult = await testClient.query(`
            SELECT id FROM locations WHERE status = 'active' AND type = 'loading' LIMIT 1
          `);

          if (locationResult.rows.length > 0) {
            await testClient.query(`
              INSERT INTO assignments (
                assignment_code, truck_id, driver_id, loading_location_id, unloading_location_id,
                assigned_date, status, priority, expected_loads, created_at, updated_at
              ) VALUES ($1, $2, $3, $4, $4, CURRENT_DATE, 'assigned', 'normal', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            `, [`TEST_${truck.truck_number}_${Date.now()}`, truck.id, driver.id, locationResult.rows[0].id]);

            assignmentResult = await testClient.query(`
              SELECT id, driver_id FROM assignments 
              WHERE truck_id = $1 AND status IN ('assigned', 'in_progress')
              ORDER BY created_at DESC LIMIT 1
            `, [truck.id]);
          }
        }

        if (assignmentResult.rows.length > 0) {
          const assignment = assignmentResult.rows[0];

          // Verify assignment is synced with shift
          if (assignment.driver_id !== driver.id) {
            await testClient.query(`
              UPDATE assignments SET driver_id = $1, updated_at = CURRENT_TIMESTAMP
              WHERE id = $2
            `, [driver.id, assignment.id]);
          }

          // Verify sync
          const syncVerification = await testClient.query(`
            SELECT 
              ds.driver_id as shift_driver_id,
              a.driver_id as assignment_driver_id
            FROM driver_shifts ds
            JOIN assignments a ON ds.truck_id = a.truck_id
            WHERE ds.id = $1 AND a.id = $2
          `, [shiftId, assignment.id]);

          expect(syncVerification.rows.length).toBe(1);
          expect(syncVerification.rows[0].shift_driver_id).toBe(syncVerification.rows[0].assignment_driver_id);

          console.log('Shift to Assignment data flow validated successfully');
        }

        await testClient.query('ROLLBACK');
      } catch (error) {
        await testClient.query('ROLLBACK');
        throw error;
      } finally {
        testClient.release();
      }
    }, 30000);

    test('should validate assignment to trip monitoring data flow', async () => {
      const testClient = await getClient();
      
      try {
        await testClient.query('BEGIN');

        // Find an existing assignment with driver
        const assignmentResult = await testClient.query(`
          SELECT 
            a.id as assignment_id,
            a.truck_id,
            a.driver_id,
            dt.truck_number,
            d.full_name as driver_name
          FROM assignments a
          JOIN dump_trucks dt ON a.truck_id = dt.id
          JOIN drivers d ON a.driver_id = d.id
          WHERE a.status IN ('assigned', 'in_progress')
            AND a.driver_id IS NOT NULL
          LIMIT 1
        `);

        if (assignmentResult.rows.length === 0) {
          console.log('Skipping assignment to trip test - no suitable assignment found');
          await testClient.query('ROLLBACK');
          return;
        }

        const assignment = assignmentResult.rows[0];

        // Ensure there's an active shift for this truck/driver
        await testClient.query(`
          UPDATE driver_shifts SET status = 'completed', end_date = CURRENT_DATE, end_time = CURRENT_TIME
          WHERE truck_id = $1 AND status = 'active'
        `, [assignment.truck_id]);

        const shiftResult = await testClient.query(`
          INSERT INTO driver_shifts (
            truck_id, driver_id, shift_type, start_date, start_time, 
            status, auto_created, created_at, updated_at
          ) VALUES ($1, $2, 'day', CURRENT_DATE, CURRENT_TIME, 'active', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
          RETURNING id
        `, [assignment.truck_id, assignment.driver_id]);

        const shiftId = shiftResult.rows[0].id;

        // Test driver capture (simulating trip scan)
        const driverCaptureResult = await testClient.query(`
          SELECT
            ds.driver_id,
            d.full_name as driver_name,
            d.employee_id,
            ds.id as shift_id,
            ds.shift_type
          FROM driver_shifts ds
          JOIN drivers d ON ds.driver_id = d.id
          WHERE ds.truck_id = $1
            AND ds.status = 'active'
            AND d.status = 'active'
            AND (
              (ds.start_date IS NOT NULL AND ds.auto_created = true AND ds.end_time IS NULL AND 
               CURRENT_DATE >= ds.start_date) OR
              (ds.start_date IS NOT NULL AND ds.auto_created = true AND ds.end_time IS NOT NULL AND 
               CURRENT_DATE BETWEEN ds.start_date AND ds.end_date) OR
              (ds.start_date IS NULL AND ds.shift_date IS NOT NULL AND ds.shift_date = CURRENT_DATE)
            )
          ORDER BY 
            ds.auto_created DESC,
            ds.created_at DESC
          LIMIT 1
        `, [assignment.truck_id]);

        expect(driverCaptureResult.rows.length).toBe(1);
        
        const capturedDriver = driverCaptureResult.rows[0];
        expect(capturedDriver.driver_id).toBe(assignment.driver_id);
        expect(capturedDriver.shift_id).toBe(shiftId);

        console.log('Assignment to Trip Monitoring data flow validated successfully');

        await testClient.query('ROLLBACK');
      } catch (error) {
        await testClient.query('ROLLBACK');
        throw error;
      } finally {
        testClient.release();
      }
    }, 30000);
  });

  describe('DataFlowLogger', () => {
    test('should create correlation IDs for tracking', () => {
      const correlationId1 = DataFlowLogger.createCorrelationId('test');
      const correlationId2 = DataFlowLogger.createCorrelationId('test');

      expect(typeof correlationId1).toBe('string');
      expect(typeof correlationId2).toBe('string');
      expect(correlationId1).not.toBe(correlationId2);
      expect(correlationId1).toMatch(/^test_\d+_[a-z0-9]+$/);
    });

    test('should log data flow events with correlation', () => {
      const correlationId = DataFlowLogger.createCorrelationId('test');
      
      // These should not throw errors
      expect(() => {
        DataFlowLogger.logWithCorrelation(correlationId, 'test_system', 'TEST_EVENT', 'Test message', { test: true });
      }).not.toThrow();

      expect(() => {
        DataFlowLogger.logShiftEvent('TEST_SHIFT', 'Test shift event', { shift_id: 123 });
      }).not.toThrow();

      expect(() => {
        DataFlowLogger.logAssignmentEvent('TEST_ASSIGNMENT', 'Test assignment event', { assignment_id: 456 });
      }).not.toThrow();

      expect(() => {
        DataFlowLogger.logTripEvent('TEST_TRIP', 'Test trip event', { trip_id: 789 });
      }).not.toThrow();
    });
  });
});

// Helper function to run manual validation queries
async function runManualValidationQueries() {
  const client = await getClient();
  const queries = DataFlowValidationService.getValidationQueries();

  try {
    console.log('\n=== Manual Validation Query Results ===\n');

    for (const [queryName, querySQL] of Object.entries(queries)) {
      try {
        console.log(`--- ${queryName.toUpperCase().replace(/_/g, ' ')} ---`);
        const result = await client.query(querySQL);
        
        if (result.rows.length === 0) {
          console.log('No issues found - data flow is consistent');
        } else {
          console.table(result.rows.slice(0, 10)); // Show first 10 rows
          if (result.rows.length > 10) {
            console.log(`... and ${result.rows.length - 10} more rows`);
          }
        }
        console.log('');
      } catch (error) {
        console.error(`Error running ${queryName}:`, error.message);
      }
    }
  } finally {
    client.release();
  }
}

// Export helper function for manual testing
module.exports = {
  runManualValidationQueries
};