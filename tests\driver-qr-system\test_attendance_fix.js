// Test script to verify attendance API fixes
const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api/driver-admin';

async function testAttendanceEndpoints() {
  console.log('🧪 Testing Driver Attendance API Fixes...\n');

  // Test 1: Debug endpoint
  try {
    console.log('1️⃣ Testing debug endpoint...');
    const debugResponse = await axios.get(`${BASE_URL}/debug`);
    console.log('✅ Debug endpoint works');
    console.log('📊 Database stats:', debugResponse.data.debug_info);
    console.log('');
  } catch (error) {
    console.log('❌ Debug endpoint failed:', error.response?.data || error.message);
    console.log('');
  }

  // Test 2: Attendance with empty parameters (the failing case)
  try {
    console.log('2️⃣ Testing attendance with empty parameters...');
    const attendanceResponse = await axios.get(`${BASE_URL}/attendance`, {
      params: {
        driver_id: '',
        date_from: '',
        date_to: '',
        truck_id: '',
        status: 'completed',
        sort_by: 'start_date',
        sort_order: 'desc',
        limit: 50,
        offset: 0
      }
    });
    console.log('✅ Attendance with empty params works');
    console.log('📊 Records found:', attendanceResponse.data.data?.records?.length || 0);
    console.log('');
  } catch (error) {
    console.log('❌ Attendance with empty params failed:', error.response?.data || error.message);
    console.log('');
  }

  // Test 3: Attendance summary (the 500 error case)
  try {
    console.log('3️⃣ Testing attendance summary...');
    const summaryResponse = await axios.get(`${BASE_URL}/attendance-summary`, {
      params: {
        period: 'weekly'
      }
    });
    console.log('✅ Attendance summary works');
    console.log('📊 Summary data:', {
      total_shifts: summaryResponse.data.data?.overall_stats?.total_shifts,
      active_drivers: summaryResponse.data.data?.overall_stats?.active_drivers,
      driver_summaries: summaryResponse.data.data?.driver_summaries?.length
    });
    console.log('');
  } catch (error) {
    console.log('❌ Attendance summary failed:', error.response?.data || error.message);
    console.log('');
  }

  // Test 4: Basic attendance without parameters
  try {
    console.log('4️⃣ Testing basic attendance...');
    const basicResponse = await axios.get(`${BASE_URL}/attendance`);
    console.log('✅ Basic attendance works');
    console.log('📊 Records found:', basicResponse.data.data?.records?.length || 0);
    console.log('');
  } catch (error) {
    console.log('❌ Basic attendance failed:', error.response?.data || error.message);
    console.log('');
  }

  console.log('🏁 Test completed!');
}

// Run the tests
testAttendanceEndpoints().catch(console.error);