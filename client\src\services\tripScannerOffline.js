// Trip Scanner Offline Service
// Specialized offline operations for trip scanning functionality

import { offlineDB, SYNC_STATUS, PRIORITY } from './offlineDB.js';

// Trip scanner specific data structures and validation
export class TripScannerOfflineService {
  constructor() {
    this.storeName = 'scanQueue';
  }

  // Store trip scan data offline in exact API format for perfect sync compatibility
  async storeScan(scanData) {
    try {
      // Ensure database is initialized
      await offlineDB.initialize();
      if (!offlineDB.db) {
        throw new Error('Database not initialized');
      }

      // Validate scan data structure
      const validatedData = this.validateScanData(scanData);

      // Determine priority based on scan type and context
      const priority = this.calculatePriority(validatedData);

      // Create API-compatible payload for direct sync
      const apiPayload = this.createAPIPayload(validatedData);

      // Create offline scan record with exact API format
      const offlineScan = {
        // EXACT API PAYLOAD - Direct pass-through to /api/scanner/public-scan
        apiPayload: apiPayload,

        // Sync metadata (separate from payload)
        syncMetadata: {
          status: SYNC_STATUS.PENDING,
          priority: priority,
          retryCount: 0,
          maxRetries: 3,
          timestamp: new Date().toISOString(),
          scheduledSync: new Date().toISOString(),
          validationHash: this.generateValidationHash(apiPayload),
          dataIntegrity: true
        },

        // Quick access fields for IndexedDB queries (derived from payload)
        scanType: apiPayload.scan_type,
        locationId: apiPayload.qr_data?.id || null,
        truckId: apiPayload.qr_data?.id || null,

        // Device context for audit
        deviceInfo: this.getDeviceInfo()
      };

      // Store in IndexedDB
      const id = await offlineDB.addData(this.storeName, offlineScan);

      console.log('[TripScannerOffline] Scan stored offline (API-compatible format):', {
        id,
        scanType: offlineScan.scanType,
        priority: offlineScan.syncMetadata.priority,
        apiPayloadReady: true
      });

      return {
        success: true,
        id,
        message: '📱 Scan saved offline - will sync when connected',
        offlineMode: true,
        priority: offlineScan.syncMetadata.priority
      };

    } catch (error) {
      console.error('[TripScannerOffline] Failed to store scan:', error);
      throw new Error(`Failed to store scan offline: ${error.message}`);
    }
  }

  // Create exact API payload format for /api/scanner/public-scan endpoint
  createAPIPayload(scanData) {
    const payload = {
      scan_type: this.determineScanType(scanData),
      qr_data: this.extractQRData(scanData),
      ip_address: this.getClientIP(),
      user_agent: this.getUserAgent()
    };

    // Add location_scan_data for truck scans
    if (payload.scan_type === 'truck' && scanData.location_qr_data) {
      payload.location_scan_data = scanData.location_qr_data;
    }

    return payload;
  }

  // Determine scan type based on scan data
  determineScanType(scanData) {
    if (scanData.truck_qr_data) {
      return 'truck';
    } else if (scanData.location_qr_data) {
      return 'location';
    }
    throw new Error('Cannot determine scan type - missing QR data');
  }

  // Extract QR data in correct format
  extractQRData(scanData) {
    if (scanData.truck_qr_data) {
      return scanData.truck_qr_data;
    } else if (scanData.location_qr_data) {
      return scanData.location_qr_data;
    }
    throw new Error('No QR data found in scan data');
  }

  // Get client IP address (best effort)
  getClientIP() {
    // In browser environment, we can't directly get IP
    // This will be handled by the server during sync
    return 'offline_client';
  }

  // Get user agent string
  getUserAgent() {
    return navigator.userAgent || 'PWA_Offline_Client';
  }

  // Validate scan data structure for API compatibility
  validateScanData(scanData) {
    if (!scanData) {
      throw new Error('Scan data is required');
    }

    // Validate QR data structure
    if (scanData.location_qr_data) {
      this.validateQRData(scanData.location_qr_data, 'location');
    }

    if (scanData.truck_qr_data) {
      this.validateQRData(scanData.truck_qr_data, 'truck');
    }

    // Must have at least one QR data
    if (!scanData.location_qr_data && !scanData.truck_qr_data) {
      throw new Error('At least one QR data (location or truck) is required');
    }

    // Validate scan sequence for truck scans
    if (scanData.truck_qr_data && !scanData.location_qr_data) {
      throw new Error('Truck scan requires previous location scan data');
    }

    return scanData;
  }

  // Validate individual QR code data
  validateQRData(qrData, expectedType) {
    if (!qrData.type || !qrData.id) {
      throw new Error(`Invalid ${expectedType} QR data: missing type or id`);
    }
    
    if (qrData.type !== expectedType) {
      throw new Error(`Expected ${expectedType} QR code, got ${qrData.type}`);
    }
  }

  // Calculate priority based on scan context
  calculatePriority(scanData) {
    // Critical: Trip completion scans
    if (scanData.scan_step === 'truck' && scanData.location_qr_data?.type === 'unloading') {
      return PRIORITY.CRITICAL;
    }
    
    // High: Loading operations
    if (scanData.location_qr_data?.type === 'loading') {
      return PRIORITY.HIGH;
    }
    
    // Normal: Regular workflow scans
    if (scanData.truck_qr_data) {
      return PRIORITY.NORMAL;
    }
    
    // Low: Location-only scans
    return PRIORITY.LOW;
  }

  // Determine workflow phase from scan data
  determineWorkflowPhase(scanData) {
    if (!scanData.location_qr_data) return 'unknown';
    
    const locationType = scanData.location_qr_data.type;
    const hasTrip = scanData.truck_qr_data;
    
    if (locationType === 'loading') {
      return hasTrip ? 'loading_start' : 'location_scan';
    }
    
    if (locationType === 'unloading') {
      return hasTrip ? 'unloading_start' : 'location_scan';
    }
    
    return 'unknown';
  }

  // Get device information for context
  getDeviceInfo() {
    return {
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      language: navigator.language,
      online: navigator.onLine,
      timestamp: new Date().toISOString()
    };
  }

  // Generate validation hash for data integrity
  generateValidationHash(data) {
    // Simple hash for data integrity checking
    const str = JSON.stringify(data);
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString(16);
  }

  // Get all pending scans for sync
  async getPendingScans() {
    try {
      const pendingScans = await offlineDB.getAllPending(this.storeName);
      
      // Sort by priority and timestamp
      return pendingScans.sort((a, b) => {
        if (a.priority !== b.priority) {
          return b.priority - a.priority; // Higher priority first
        }
        return new Date(a.timestamp) - new Date(b.timestamp); // Older first
      });
      
    } catch (error) {
      console.error('[TripScannerOffline] Failed to get pending scans:', error);
      return [];
    }
  }

  // Update scan status
  async updateScanStatus(id, status, metadata = {}) {
    try {
      // Get current scan data
      const transaction = offlineDB.db.transaction([this.storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      const scan = await new Promise((resolve, reject) => {
        const request = store.get(id);
        request.onsuccess = () => resolve(request.result);
        request.onerror = () => reject(request.error);
      });

      if (!scan) {
        throw new Error(`Scan with id ${id} not found`);
      }

      // Update scan with new status and metadata
      const updatedScan = {
        ...scan,
        status,
        ...metadata,
        lastStatusUpdate: new Date().toISOString()
      };

      await offlineDB.updateData(this.storeName, updatedScan);
      
      console.log(`[TripScannerOffline] Updated scan ${id} status to ${status}`);
      return updatedScan;
      
    } catch (error) {
      console.error('[TripScannerOffline] Failed to update scan status:', error);
      throw error;
    }
  }

  // Remove successfully synced scan
  async removeSyncedScan(id) {
    try {
      await offlineDB.deleteData(this.storeName, id);
      console.log(`[TripScannerOffline] Removed synced scan: ${id}`);
    } catch (error) {
      console.error('[TripScannerOffline] Failed to remove synced scan:', error);
      throw error;
    }
  }

  // Get pending scan count (for PWA status hook)
  async getPendingCount() {
    try {
      const pendingScans = await offlineDB.getDataByIndex(this.storeName, 'status', SYNC_STATUS.PENDING);
      return pendingScans.length;
    } catch (error) {
      console.error('[TripScannerOffline] Failed to get pending count:', error);
      return 0;
    }
  }

  // Get scan statistics
  async getStats() {
    try {
      const allScans = await offlineDB.getDataByIndex(this.storeName, 'status', SYNC_STATUS.PENDING);
      const failedScans = await offlineDB.getDataByIndex(this.storeName, 'status', SYNC_STATUS.FAILED);

      const stats = {
        total: allScans.length + failedScans.length,
        pending: allScans.length,
        failed: failedScans.length,
        byPriority: {
          critical: allScans.filter(s => s.priority === PRIORITY.CRITICAL).length,
          high: allScans.filter(s => s.priority === PRIORITY.HIGH).length,
          normal: allScans.filter(s => s.priority === PRIORITY.NORMAL).length,
          low: allScans.filter(s => s.priority === PRIORITY.LOW).length
        },
        byWorkflowPhase: {}
      };

      // Count by workflow phase
      allScans.forEach(scan => {
        const phase = scan.workflowPhase || 'unknown';
        stats.byWorkflowPhase[phase] = (stats.byWorkflowPhase[phase] || 0) + 1;
      });

      return stats;

    } catch (error) {
      console.error('[TripScannerOffline] Failed to get stats:', error);
      return { total: 0, pending: 0, failed: 0, byPriority: {}, byWorkflowPhase: {} };
    }
  }

  // Clear all scan data (for testing/reset)
  async clearAllScans() {
    try {
      await offlineDB.initialize();
      if (!offlineDB.db) {
        throw new Error('Database not initialized');
      }
      const transaction = offlineDB.db.transaction([this.storeName], 'readwrite');
      const store = transaction.objectStore(this.storeName);

      await new Promise((resolve, reject) => {
        const request = store.clear();
        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
      });

      console.log('[TripScannerOffline] All scan data cleared');
    } catch (error) {
      console.error('[TripScannerOffline] Failed to clear scan data:', error);
      throw error;
    }
  }

  // Alias for test compatibility
  async clearAllData() {
    return this.clearAllScans();
  }
}

// Create singleton instance
export const tripScannerOffline = new TripScannerOfflineService();

// Export service instance
export default tripScannerOffline;
