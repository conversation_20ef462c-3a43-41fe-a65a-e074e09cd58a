#!/usr/bin/env node

/**
 * Assignment Management Shift Status Fix Validation Script
 * 
 * This script tests the fixes for the "⚠️ No Active Shift" warnings in Assignment Management
 * by validating both frontend display logic and backend data synchronization.
 */

const { query, getClient } = require('../server/config/database');
const axios = require('axios');

// Test configuration
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';
const TEST_SCENARIOS = [
  'active_day_shift',
  'active_night_shift', 
  'active_qr_shift',
  'overnight_shift',
  'null_end_time_shift'
];

class AssignmentShiftStatusTester {
  constructor() {
    this.results = {
      database_tests: [],
      api_tests: [],
      integration_tests: [],
      summary: {
        total_tests: 0,
        passed: 0,
        failed: 0,
        warnings: 0
      }
    };
  }

  /**
   * Run all test scenarios
   */
  async runAllTests() {
    console.log('🧪 Starting Assignment Management Shift Status Fix Validation');
    console.log('=' .repeat(70));

    try {
      // Test 1: Database LEFT JOIN Logic
      await this.testDatabaseLeftJoinLogic();
      
      // Test 2: API Endpoint Consistency
      await this.testAPIEndpointConsistency();
      
      // Test 3: Trip-Driver Capture Integration
      await this.testTripDriverCaptureIntegration();
      
      // Test 4: Cross-System Status Conflicts
      await this.testCrossSystemStatusConflicts();
      
      // Generate summary report
      this.generateSummaryReport();
      
    } catch (error) {
      console.error('❌ Test execution failed:', error);
      process.exit(1);
    }
  }

  /**
   * Test the enhanced LEFT JOIN logic in assignments route
   */
  async testDatabaseLeftJoinLogic() {
    console.log('\n📋 Test 1: Database LEFT JOIN Logic');
    console.log('-'.repeat(50));

    const client = await getClient();
    
    try {
      // Test the enhanced LEFT JOIN query directly
      const testQuery = `
        SELECT
          a.id as assignment_id,
          a.assignment_code,
          t.truck_number,
          d.full_name as assigned_driver,
          
          -- Enhanced LEFT JOIN logic (should match active shifts properly)
          ds.id as shift_id,
          ds.status as shift_status,
          ds.shift_type,
          ds.start_date,
          ds.end_date,
          ds.start_time,
          ds.end_time,
          sd.full_name as shift_driver_name,
          
          -- Test the CASE logic
          CASE
            WHEN ds.id IS NOT NULL AND ds.status = 'active' THEN
              CONCAT('✅ ', ds.shift_type, ' Shift Active')
            WHEN ds.id IS NOT NULL AND ds.status = 'scheduled' THEN
              CONCAT('📅 ', ds.shift_type, ' Shift Scheduled')
            WHEN ds.id IS NOT NULL AND ds.status = 'completed' THEN
              CONCAT('✅ ', ds.shift_type, ' Shift Completed')
            ELSE '⚠️ No Active Shift'
          END as active_shift_status

        FROM assignments a
        JOIN dump_trucks t ON a.truck_id = t.id
        LEFT JOIN drivers d ON a.driver_id = d.id
        LEFT JOIN driver_shifts ds ON (
          ds.truck_id = a.truck_id
          AND ds.status = 'active'
          AND CURRENT_DATE BETWEEN ds.start_date AND ds.end_date
          AND (
            -- Handle NULL end_time for active shifts (always match)
            ds.end_time IS NULL
            OR
            -- Handle overnight shifts correctly
            (ds.end_time < ds.start_time AND
             (CURRENT_TIME >= ds.start_time OR CURRENT_TIME <= ds.end_time))
            OR
            -- Handle regular shifts
            (ds.end_time >= ds.start_time AND
             CURRENT_TIME BETWEEN ds.start_time AND ds.end_time)
          )
        )
        LEFT JOIN drivers sd ON ds.driver_id = sd.id
        WHERE a.status IN ('assigned', 'in_progress')
        ORDER BY t.truck_number
        LIMIT 10
      `;

      const result = await client.query(testQuery);
      
      console.log(`📊 Query returned ${result.rows.length} assignments`);
      
      let noActiveShiftCount = 0;
      let activeShiftCount = 0;
      
      result.rows.forEach(row => {
        if (row.active_shift_status === '⚠️ No Active Shift') {
          noActiveShiftCount++;
          console.log(`   ⚠️  ${row.truck_number}: ${row.assigned_driver || 'No driver'} - No Active Shift`);
        } else {
          activeShiftCount++;
          console.log(`   ✅ ${row.truck_number}: ${row.shift_driver_name || row.assigned_driver || 'No driver'} - ${row.active_shift_status}`);
        }
      });
      
      this.results.database_tests.push({
        test: 'LEFT JOIN Logic',
        status: 'PASSED',
        details: {
          total_assignments: result.rows.length,
          active_shifts: activeShiftCount,
          no_active_shifts: noActiveShiftCount,
          improvement: noActiveShiftCount < result.rows.length * 0.5 ? 'GOOD' : 'NEEDS_REVIEW'
        }
      });
      
      console.log(`📈 Results: ${activeShiftCount} with active shifts, ${noActiveShiftCount} without active shifts`);
      
    } catch (error) {
      console.error('❌ Database test failed:', error);
      this.results.database_tests.push({
        test: 'LEFT JOIN Logic',
        status: 'FAILED',
        error: error.message
      });
    } finally {
      client.release();
    }
  }

  /**
   * Test API endpoint consistency between main and active endpoints
   */
  async testAPIEndpointConsistency() {
    console.log('\n🌐 Test 2: API Endpoint Consistency');
    console.log('-'.repeat(50));

    try {
      // Test main assignments endpoint
      const mainResponse = await axios.get(`${API_BASE_URL}/assignments?limit=10`);
      const mainData = mainResponse.data.data;
      
      // Test active assignments endpoint  
      const activeResponse = await axios.get(`${API_BASE_URL}/assignments/active`);
      const activeData = activeResponse.data.data;
      
      console.log(`📊 Main endpoint: ${mainData.length} assignments`);
      console.log(`📊 Active endpoint: ${activeData.length} assignments`);
      
      // Compare shift status consistency
      const mainNoShiftCount = mainData.filter(a => a.active_shift_status === '⚠️ No Active Shift').length;
      const activeNoShiftCount = activeData.filter(a => !a.current_shift_driver_name).length;
      
      console.log(`📈 Main endpoint "No Active Shift": ${mainNoShiftCount}`);
      console.log(`📈 Active endpoint missing shift driver: ${activeNoShiftCount}`);
      
      this.results.api_tests.push({
        test: 'API Endpoint Consistency',
        status: 'PASSED',
        details: {
          main_endpoint_assignments: mainData.length,
          active_endpoint_assignments: activeData.length,
          main_no_shift: mainNoShiftCount,
          active_no_shift: activeNoShiftCount,
          consistency: Math.abs(mainNoShiftCount - activeNoShiftCount) <= 2 ? 'GOOD' : 'INCONSISTENT'
        }
      });
      
    } catch (error) {
      console.error('❌ API test failed:', error);
      this.results.api_tests.push({
        test: 'API Endpoint Consistency',
        status: 'FAILED',
        error: error.message
      });
    }
  }

  /**
   * Test trip-driver capture integration
   */
  async testTripDriverCaptureIntegration() {
    console.log('\n🚛 Test 3: Trip-Driver Capture Integration');
    console.log('-'.repeat(50));

    const client = await getClient();
    
    try {
      // Check recent trips for driver capture success rate
      const tripDriverQuery = `
        SELECT 
          tl.id,
          tl.trip_number,
          tl.status,
          tl.performed_by_driver_name,
          tl.performed_by_shift_type,
          a.assignment_code,
          t.truck_number,
          tl.created_at
        FROM trip_logs tl
        JOIN assignments a ON tl.assignment_id = a.id
        JOIN dump_trucks t ON a.truck_id = t.id
        WHERE tl.created_at >= CURRENT_DATE - INTERVAL '7 days'
        ORDER BY tl.created_at DESC
        LIMIT 20
      `;
      
      const result = await client.query(tripDriverQuery);
      
      let captureSuccessCount = 0;
      let captureFailureCount = 0;
      
      result.rows.forEach(trip => {
        if (trip.performed_by_driver_name) {
          captureSuccessCount++;
          console.log(`   ✅ Trip ${trip.trip_number} (${trip.truck_number}): ${trip.performed_by_driver_name} (${trip.performed_by_shift_type})`);
        } else {
          captureFailureCount++;
          console.log(`   ⚠️  Trip ${trip.trip_number} (${trip.truck_number}): No driver captured`);
        }
      });
      
      const successRate = (captureSuccessCount / result.rows.length * 100).toFixed(1);
      
      console.log(`📈 Driver capture success rate: ${successRate}% (${captureSuccessCount}/${result.rows.length})`);
      
      this.results.integration_tests.push({
        test: 'Trip-Driver Capture',
        status: successRate >= 80 ? 'PASSED' : 'WARNING',
        details: {
          total_trips: result.rows.length,
          capture_success: captureSuccessCount,
          capture_failure: captureFailureCount,
          success_rate: `${successRate}%`
        }
      });
      
    } catch (error) {
      console.error('❌ Integration test failed:', error);
      this.results.integration_tests.push({
        test: 'Trip-Driver Capture',
        status: 'FAILED',
        error: error.message
      });
    } finally {
      client.release();
    }
  }

  /**
   * Test cross-system status conflicts
   */
  async testCrossSystemStatusConflicts() {
    console.log('\n🔍 Test 4: Cross-System Status Conflicts');
    console.log('-'.repeat(50));

    const client = await getClient();
    
    try {
      // Check for status conflicts between shifts, assignments, and trips
      const conflictQuery = `
        SELECT 
          t.truck_number,
          ds.status as shift_status,
          ds.shift_type,
          d.full_name as shift_driver,
          a.status as assignment_status,
          COUNT(tl.id) as active_trips,
          CASE 
            WHEN ds.status = 'active' AND COUNT(tl.id) > 0 THEN 'active_with_trips'
            WHEN ds.status = 'active' AND COUNT(tl.id) = 0 THEN 'active_no_trips'
            WHEN ds.status IS NULL AND COUNT(tl.id) > 0 THEN 'no_shift_with_trips'
            ELSE 'normal'
          END as conflict_type
        FROM dump_trucks t
        LEFT JOIN driver_shifts ds ON ds.truck_id = t.id AND ds.status = 'active'
        LEFT JOIN drivers d ON ds.driver_id = d.id
        LEFT JOIN assignments a ON a.truck_id = t.id AND a.status IN ('assigned', 'in_progress')
        LEFT JOIN trip_logs tl ON tl.assignment_id = a.id AND tl.status IN ('loading_start', 'loading_end', 'unloading_start', 'unloading_end')
        WHERE a.id IS NOT NULL
        GROUP BY t.truck_number, ds.status, ds.shift_type, d.full_name, a.status
        ORDER BY t.truck_number
      `;
      
      const result = await client.query(conflictQuery);
      
      let conflictCount = 0;
      let normalCount = 0;
      
      result.rows.forEach(row => {
        if (row.conflict_type !== 'normal') {
          conflictCount++;
          console.log(`   ⚠️  ${row.truck_number}: ${row.conflict_type} (${row.shift_driver || 'No driver'})`);
        } else {
          normalCount++;
          console.log(`   ✅ ${row.truck_number}: Normal status (${row.shift_driver || 'No driver'})`);
        }
      });
      
      console.log(`📈 Status conflicts: ${conflictCount}, Normal: ${normalCount}`);
      
      this.results.integration_tests.push({
        test: 'Cross-System Status Conflicts',
        status: conflictCount === 0 ? 'PASSED' : 'WARNING',
        details: {
          total_trucks: result.rows.length,
          conflicts: conflictCount,
          normal: normalCount,
          conflict_rate: `${(conflictCount / result.rows.length * 100).toFixed(1)}%`
        }
      });
      
    } catch (error) {
      console.error('❌ Conflict test failed:', error);
      this.results.integration_tests.push({
        test: 'Cross-System Status Conflicts',
        status: 'FAILED',
        error: error.message
      });
    } finally {
      client.release();
    }
  }

  /**
   * Generate comprehensive summary report
   */
  generateSummaryReport() {
    console.log('\n📊 TEST SUMMARY REPORT');
    console.log('='.repeat(70));
    
    const allTests = [
      ...this.results.database_tests,
      ...this.results.api_tests,
      ...this.results.integration_tests
    ];
    
    let passed = 0;
    let failed = 0;
    let warnings = 0;
    
    allTests.forEach(test => {
      if (test.status === 'PASSED') passed++;
      else if (test.status === 'FAILED') failed++;
      else if (test.status === 'WARNING') warnings++;
      
      console.log(`${test.status === 'PASSED' ? '✅' : test.status === 'FAILED' ? '❌' : '⚠️'} ${test.test}: ${test.status}`);
      if (test.details) {
        Object.entries(test.details).forEach(([key, value]) => {
          console.log(`   ${key}: ${value}`);
        });
      }
      if (test.error) {
        console.log(`   Error: ${test.error}`);
      }
    });
    
    this.results.summary = {
      total_tests: allTests.length,
      passed,
      failed,
      warnings
    };
    
    console.log('\n📈 OVERALL RESULTS:');
    console.log(`   Total Tests: ${this.results.summary.total_tests}`);
    console.log(`   Passed: ${this.results.summary.passed}`);
    console.log(`   Failed: ${this.results.summary.failed}`);
    console.log(`   Warnings: ${this.results.summary.warnings}`);
    
    const successRate = (this.results.summary.passed / this.results.summary.total_tests * 100).toFixed(1);
    console.log(`   Success Rate: ${successRate}%`);
    
    if (this.results.summary.failed === 0 && this.results.summary.warnings <= 1) {
      console.log('\n🎉 Assignment Management Shift Status Fix: SUCCESSFUL');
    } else if (this.results.summary.failed === 0) {
      console.log('\n⚠️  Assignment Management Shift Status Fix: MOSTLY SUCCESSFUL (with warnings)');
    } else {
      console.log('\n❌ Assignment Management Shift Status Fix: NEEDS ATTENTION');
    }
  }
}

// Run tests if called directly
if (require.main === module) {
  const tester = new AssignmentShiftStatusTester();
  tester.runAllTests().catch(console.error);
}

module.exports = AssignmentShiftStatusTester;
