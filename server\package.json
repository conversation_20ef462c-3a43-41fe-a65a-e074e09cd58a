{"name": "hauling-qr-server", "version": "1.0.0", "description": "Backend API for Hauling QR Trip Management System", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:exceptions": "jest exception-workflows.test.js", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:shift-fix": "node tests/run-comprehensive-tests.js", "validate:system": "node tests/validate-system-status.js", "test:overnight": "jest tests/captureActiveDriverInfo.test.js --verbose", "test:trip-logs": "jest tests/trip-logs-field-population.test.js --verbose", "test:e2e": "jest tests/end-to-end-shift-trip-flow.test.js --verbose"}, "dependencies": {"@supabase/mcp-server-postgrest": "^0.1.0", "axios": "^1.10.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "jsqr": "^1.4.0", "multer": "^1.4.5-lts.1", "node-fetch": "^3.3.2", "pg": "^8.11.3", "qrcode": "^1.5.3", "sharp": "^0.32.6", "uuid": "^9.0.1", "winston": "^3.17.0"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "keywords": ["express", "api", "qr-code", "postgresql", "hauling"], "author": "Hauling Management System", "license": "MIT"}