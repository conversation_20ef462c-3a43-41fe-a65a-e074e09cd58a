// Hauling QR Trip Management System - Service Worker
// PWA Service Worker with Offline Support and Background Sync

const CACHE_NAME = 'hauling-qr-v1.1.0';
const OFFLINE_CACHE = 'hauling-qr-offline-v1.1.0';
const CHUNK_CACHE = 'hauling-qr-chunks-v1.1.0';

// Core app files to cache for offline functionality
const CORE_CACHE_FILES = [
  '/',
  '/trip-scanner',
  '/driver-connect',
  '/manifest.json',
  '/favicon.ico',
  '/test-offline.html',
  '/test-offline-functionality.js',
  '/offline-fallback.html'
];

// Dynamic chunk caching strategy
let DYNAMIC_CHUNKS = [];

// Fetch and cache all JavaScript chunks from asset-manifest.json
async function loadAssetManifest() {
  try {
    const response = await fetch('/asset-manifest.json');
    const manifest = await response.json();

    // Extract all JavaScript and CSS files
    const assets = [];
    for (const [key, path] of Object.entries(manifest.files)) {
      if (path.endsWith('.js') || path.endsWith('.css')) {
        assets.push(path);
      }
    }

    DYNAMIC_CHUNKS = assets;
    console.log('[SW] Loaded asset manifest:', assets.length, 'assets found');
    return assets;
  } catch (error) {
    console.error('[SW] Failed to load asset manifest:', error);
    // Fallback to common chunk patterns
    return [
      '/static/css/main.css',
      '/static/js/main.js'
    ];
  }
}

// API endpoints that can work offline (removed unused variable)

// Install event - cache core files and all JavaScript chunks
self.addEventListener('install', (event) => {
  console.log('[SW] Installing service worker...');

  event.waitUntil(
    Promise.all([
      // Cache core files
      caches.open(CACHE_NAME).then((cache) => {
        console.log('[SW] Caching core files');
        return cache.addAll(CORE_CACHE_FILES);
      }),

      // Load and cache all JavaScript chunks
      loadAssetManifest().then((assets) => {
        return caches.open(CHUNK_CACHE).then((cache) => {
          console.log('[SW] Caching JavaScript chunks:', assets.length, 'files');
          // Cache chunks in batches to avoid overwhelming the browser
          return cacheInBatches(cache, assets, 5);
        });
      })
    ])
    .then(() => {
      console.log('[SW] All files cached successfully');
      return self.skipWaiting(); // Activate immediately
    })
    .catch((error) => {
      console.error('[SW] Failed to cache files:', error);
    })
  );
});

// Cache files in batches to prevent overwhelming the browser
async function cacheInBatches(cache, urls, batchSize = 5) {
  for (let i = 0; i < urls.length; i += batchSize) {
    const batch = urls.slice(i, i + batchSize);
    try {
      await Promise.all(
        batch.map(async (url) => {
          try {
            const response = await fetch(url);
            if (response.ok) {
              await cache.put(url, response);
              console.log('[SW] Cached:', url);
            }
          } catch (error) {
            console.warn('[SW] Failed to cache:', url, error);
          }
        })
      );
    } catch (error) {
      console.warn('[SW] Batch caching error:', error);
    }
  }
}

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('[SW] Activating service worker...');

  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== CACHE_NAME &&
                cacheName !== OFFLINE_CACHE &&
                cacheName !== CHUNK_CACHE) {
              console.log('[SW] Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
            return Promise.resolve(); // Return resolved promise for non-matching caches
          })
        );
      })
      .then(() => {
        console.log('[SW] Service worker activated');
        return self.clients.claim(); // Take control immediately
      })
  );
});

// Fetch event - handle network requests with offline fallback
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Skip non-GET requests and chrome-extension requests
  if (request.method !== 'GET' || url.protocol === 'chrome-extension:') {
    return;
  }

  // Handle API requests - only intercept when offline
  if (url.pathname.startsWith('/api/')) {
    event.respondWith(handleApiRequest(request));
    return;
  }

  // Handle app navigation
  if (request.mode === 'navigate') {
    event.respondWith(handleNavigation(request));
    return;
  }

  // Handle static assets
  event.respondWith(handleStaticAssets(request));
});

// Handle API requests with offline fallback
async function handleApiRequest(request) {
  // Check if we're online - if so, let the request pass through normally
  if (navigator.onLine) {
    try {
      const response = await fetch(request);

      // Cache successful responses for certain endpoints when online
      if (response.ok && shouldCacheApiResponse(request.url)) {
        const cache = await caches.open(OFFLINE_CACHE);
        cache.put(request, response.clone());
      }

      return response;
    } catch (error) {
      // If network fails while supposedly online, fall through to offline handling
      console.log('[SW] Network request failed while online, falling back to cache:', error);
    }
  }

  // Offline handling - try cache first
  const cachedResponse = await caches.match(request);
  if (cachedResponse) {
    console.log('[SW] Serving API response from cache');
    return cachedResponse;
  }

  // Return offline indicator for scan endpoints
  if (request.url.includes('/api/scanner/')) {
    return new Response(
      JSON.stringify({
        success: false,
        offline: true,
        message: 'Offline mode - scan will be queued for sync'
      }),
      {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }

  // For other API endpoints, return a generic offline response
  return new Response(
    JSON.stringify({
      success: false,
      offline: true,
      message: 'Service unavailable offline'
    }),
    {
      status: 503,
      headers: { 'Content-Type': 'application/json' }
    }
  );
}

// Handle navigation requests with enhanced offline fallback
async function handleNavigation(request) {
  try {
    // Try network first
    const response = await fetch(request);
    if (response.ok) {
      return response;
    }
    throw new Error(`Navigation failed: ${response.status}`);
  } catch (error) {
    console.log('[SW] Navigation failed, serving from cache');

    // Try to serve cached page first
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }

    // If specific pages aren't cached, try to serve them from cache
    const url = new URL(request.url);
    if (url.pathname === '/trip-scanner') {
      const tripScannerCache = await caches.match('/trip-scanner');
      if (tripScannerCache) return tripScannerCache;
    } else if (url.pathname === '/driver-connect') {
      const driverConnectCache = await caches.match('/driver-connect');
      if (driverConnectCache) return driverConnectCache;
    }

    // Try main page
    const mainPageCache = await caches.match('/');
    if (mainPageCache) {
      return mainPageCache;
    }

    // Last resort: serve offline fallback page
    console.log('[SW] Serving offline fallback page');
    const offlineFallback = await caches.match('/offline-fallback.html');
    if (offlineFallback) {
      return offlineFallback;
    }

    // If even the fallback isn't cached, create a minimal response
    return new Response(
      `<!DOCTYPE html>
      <html><head><title>Offline</title></head>
      <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
        <h1>App Offline</h1>
        <p>The app is currently unavailable. Please check your connection and try again.</p>
        <button onclick="window.location.reload()">Retry</button>
      </body></html>`,
      {
        status: 200,
        headers: { 'Content-Type': 'text/html' }
      }
    );
  }
}

// Handle static assets with enhanced chunk caching
async function handleStaticAssets(request) {
  try {
    // Try cache first - check all cache stores
    let cachedResponse = await caches.match(request);
    if (!cachedResponse) {
      // Check chunk cache specifically for JavaScript files
      if (request.url.includes('/static/js/') || request.url.includes('.chunk.js')) {
        cachedResponse = await caches.match(request, { cacheName: CHUNK_CACHE });
      }
    }

    if (cachedResponse) {
      console.log('[SW] Serving from cache:', request.url);
      return cachedResponse;
    }

    // If online, fetch from network and cache
    if (navigator.onLine) {
      const response = await fetch(request);
      if (response.ok) {
        // Determine which cache to use
        const cache = request.url.includes('/static/js/') || request.url.includes('.chunk.js')
          ? await caches.open(CHUNK_CACHE)
          : await caches.open(CACHE_NAME);
        cache.put(request, response.clone());
        console.log('[SW] Cached new asset:', request.url);
      }
      return response;
    } else {
      // Offline and no cache - provide fallback
      throw new Error('Asset not available offline');
    }
  } catch (error) {
    console.log('[SW] Static asset failed:', request.url, error.message);

    // For JavaScript chunks, provide a fallback that prevents app crash
    if (request.url.includes('.chunk.js') || request.url.includes('/static/js/')) {
      return new Response(
        `console.warn('Chunk ${request.url} not available offline - providing fallback');`,
        {
          status: 200,
          headers: { 'Content-Type': 'application/javascript' }
        }
      );
    }

    throw error;
  }
}

// Determine if API response should be cached
function shouldCacheApiResponse(url) {
  // Cache location and truck data for offline access
  return url.includes('/api/locations') || 
         url.includes('/api/trucks') ||
         url.includes('/api/assignments');
}

// Background Sync for offline data
self.addEventListener('sync', (event) => {
  console.log('[SW] Background sync triggered:', event.tag);

  if (event.tag === 'trip-scan-sync') {
    event.waitUntil(syncOfflineData('trip-scans'));
  } else if (event.tag === 'driver-connect-sync') {
    event.waitUntil(syncOfflineData('driver-connections'));
  } else if (event.tag === 'comprehensive-sync') {
    event.waitUntil(syncOfflineData('all'));
  }
});

// Sync offline data using centralized background sync service
async function syncOfflineData(syncType) {
  console.log(`[SW] Starting ${syncType} sync...`);

  try {
    // Send message to main thread to trigger sync
    const clients = await self.clients.matchAll();

    for (const client of clients) {
      client.postMessage({
        type: 'TRIGGER_SYNC',
        syncType: syncType,
        source: 'service-worker'
      });
    }

    console.log(`[SW] ${syncType} sync message sent to clients`);
  } catch (error) {
    console.error(`[SW] Failed to trigger ${syncType} sync:`, error);
  }
}

// Removed unused syncOfflineConnections function

// Message handling for communication with main app
self.addEventListener('message', (event) => {
  console.log('[SW] Received message:', event.data);

  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }

  if (event.data && event.data.type === 'GET_CACHE_STATUS') {
    event.ports[0].postMessage({
      type: 'CACHE_STATUS',
      cached: true // We'll implement proper cache checking later
    });
  }

  // Handle sync trigger messages from main thread
  if (event.data && event.data.type === 'TRIGGER_SYNC') {
    const { syncType } = event.data;
    console.log(`[SW] Triggering ${syncType} sync from main thread`);

    // Delegate sync back to main thread via message
    event.source.postMessage({
      type: 'SYNC_DELEGATED',
      syncType: syncType,
      message: `${syncType} sync delegated to main thread`
    });
  }
});



console.log('[SW] Service worker script loaded');
