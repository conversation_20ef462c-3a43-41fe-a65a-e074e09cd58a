<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PWA Offline Functionality Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #005a87;
        }
        .button.success {
            background: #28a745;
        }
        .button.warning {
            background: #ffc107;
            color: #333;
        }
        .button.danger {
            background: #dc3545;
        }
        .results {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            margin: 5px 0;
            font-weight: bold;
        }
        .status.online {
            background: #d4edda;
            color: #155724;
        }
        .status.offline {
            background: #f8d7da;
            color: #721c24;
        }
        .navigation {
            margin: 20px 0;
            padding: 15px;
            background: #e9ecef;
            border-radius: 5px;
        }
        .navigation a {
            display: inline-block;
            margin: 5px 10px;
            padding: 8px 15px;
            background: #6c757d;
            color: white;
            text-decoration: none;
            border-radius: 3px;
        }
        .navigation a:hover {
            background: #5a6268;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 PWA Offline Functionality Test</h1>
        <p>This page tests the offline functionality of the Hauling QR Trip System PWA implementation.</p>
        
        <div class="navigation">
            <h3>🔗 Quick Navigation</h3>
            <a href="/trip-scanner" target="_blank">Trip Scanner (No Auth)</a>
            <a href="/driver-connect" target="_blank">Driver Connect</a>
            <a href="/login" target="_blank">Main App (Auth Required)</a>
            <a href="/" target="_blank">Home Page</a>
        </div>

        <div class="test-section">
            <h3>📊 Current Status</h3>
            <div id="status-display">
                <div class="status" id="online-status">Checking connection...</div>
                <div>User Agent: <span id="user-agent"></span></div>
                <div>Current URL: <span id="current-url"></span></div>
                <div>Service Worker: <span id="sw-status">Checking...</span></div>
                <div>IndexedDB: <span id="idb-status">Checking...</span></div>
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 Automated Tests</h3>
            <button class="button" onclick="runAllTests()">Run All Tests</button>
            <button class="button" onclick="clearResults()">Clear Results</button>
            <div class="results" id="test-results">Click "Run All Tests" to start testing...</div>
        </div>

        <div class="test-section">
            <h3>🔌 Network Simulation</h3>
            <p>Use browser DevTools to simulate offline mode:</p>
            <ol>
                <li>Open DevTools (F12)</li>
                <li>Go to Network tab</li>
                <li>Check "Offline" checkbox</li>
                <li>Test scanner functionality</li>
            </ol>
            <button class="button" onclick="testNetworkStatus()">Test Network Status</button>
            <button class="button" onclick="simulateOfflineRequest()">Simulate Offline Request</button>
        </div>

        <div class="test-section">
            <h3>📱 PWA Installation</h3>
            <button class="button" onclick="checkInstallability()">Check PWA Installability</button>
            <button class="button" onclick="triggerInstallPrompt()" id="install-button" style="display:none;">Install PWA</button>
            <div class="results" id="install-results">PWA installation status will appear here...</div>
        </div>

        <div class="test-section">
            <h3>🗄️ IndexedDB Testing</h3>
            <button class="button" onclick="testIndexedDB()">Test IndexedDB Operations</button>
            <button class="button" onclick="clearIndexedDB()">Clear Test Data</button>
            <div class="results" id="idb-results">IndexedDB test results will appear here...</div>
        </div>
    </div>

    <script>
        // Update status display
        function updateStatus() {
            document.getElementById('online-status').textContent = navigator.onLine ? 'Online' : 'Offline';
            document.getElementById('online-status').className = 'status ' + (navigator.onLine ? 'online' : 'offline');
            document.getElementById('user-agent').textContent = navigator.userAgent.substring(0, 100) + '...';
            document.getElementById('current-url').textContent = window.location.href;
            
            // Check Service Worker
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.getRegistration().then(reg => {
                    document.getElementById('sw-status').textContent = reg ? 'Registered' : 'Not Registered';
                });
            } else {
                document.getElementById('sw-status').textContent = 'Not Supported';
            }
            
            // Check IndexedDB
            document.getElementById('idb-status').textContent = 'indexedDB' in window ? 'Available' : 'Not Available';
        }

        // Load and run the test script
        function loadTestScript() {
            const script = document.createElement('script');
            script.src = '/test-offline-functionality.js';
            script.onload = () => {
                console.log('Test script loaded successfully');
            };
            script.onerror = () => {
                console.error('Failed to load test script');
                document.getElementById('test-results').textContent = 'Failed to load test script. Make sure test-offline-functionality.js is available.';
            };
            document.head.appendChild(script);
        }

        // Run all tests
        function runAllTests() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.textContent = 'Running tests...\n';
            
            // Capture console output
            const originalLog = console.log;
            console.log = function(...args) {
                resultsDiv.textContent += args.join(' ') + '\n';
                resultsDiv.scrollTop = resultsDiv.scrollHeight;
                originalLog.apply(console, arguments);
            };
            
            if (window.PWATests) {
                window.PWATests.runAllTests().finally(() => {
                    console.log = originalLog;
                });
            } else {
                resultsDiv.textContent += 'PWATests not available. Loading test script...\n';
                loadTestScript();
            }
        }

        // Clear results
        function clearResults() {
            document.getElementById('test-results').textContent = 'Results cleared. Click "Run All Tests" to start testing...';
        }

        // Test network status
        function testNetworkStatus() {
            const status = navigator.onLine ? 'ONLINE' : 'OFFLINE';
            alert(`Current network status: ${status}`);
            updateStatus();
        }

        // Simulate offline request
        async function simulateOfflineRequest() {
            try {
                const response = await fetch('/api/system-health');
                alert('Request succeeded - you are online');
            } catch (error) {
                alert('Request failed - you may be offline or server is down');
            }
        }

        // Check PWA installability
        function checkInstallability() {
            const resultsDiv = document.getElementById('install-results');
            
            if (window.matchMedia && window.matchMedia('(display-mode: standalone)').matches) {
                resultsDiv.textContent = '✅ PWA is already installed and running in standalone mode';
            } else if (window.deferredPrompt) {
                resultsDiv.textContent = '✅ PWA installation prompt is available';
                document.getElementById('install-button').style.display = 'inline-block';
            } else {
                resultsDiv.textContent = '⚠️ PWA installation prompt not currently available\nThis is normal - prompt appears based on engagement heuristics';
            }
        }

        // Trigger install prompt
        function triggerInstallPrompt() {
            if (window.deferredPrompt) {
                window.deferredPrompt.prompt();
                window.deferredPrompt.userChoice.then((choiceResult) => {
                    document.getElementById('install-results').textContent += '\nUser choice: ' + choiceResult.outcome;
                    window.deferredPrompt = null;
                    document.getElementById('install-button').style.display = 'none';
                });
            }
        }

        // Test IndexedDB
        async function testIndexedDB() {
            const resultsDiv = document.getElementById('idb-results');
            resultsDiv.textContent = 'Testing IndexedDB...\n';
            
            try {
                const dbName = 'PWA_Test_' + Date.now();
                const request = indexedDB.open(dbName, 1);
                
                request.onupgradeneeded = (event) => {
                    const db = event.target.result;
                    const store = db.createObjectStore('testStore', { keyPath: 'id', autoIncrement: true });
                    resultsDiv.textContent += 'Database and object store created\n';
                };
                
                request.onsuccess = (event) => {
                    const db = event.target.result;
                    resultsDiv.textContent += 'Database opened successfully\n';
                    
                    // Test adding data
                    const transaction = db.transaction(['testStore'], 'readwrite');
                    const store = transaction.objectStore('testStore');
                    const addRequest = store.add({ data: 'test data', timestamp: new Date().toISOString() });
                    
                    addRequest.onsuccess = () => {
                        resultsDiv.textContent += 'Test data added successfully\n';
                        db.close();
                        indexedDB.deleteDatabase(dbName);
                        resultsDiv.textContent += '✅ IndexedDB test completed successfully\n';
                    };
                };
                
                request.onerror = () => {
                    resultsDiv.textContent += '❌ IndexedDB test failed: ' + request.error + '\n';
                };
            } catch (error) {
                resultsDiv.textContent += '❌ IndexedDB test error: ' + error.message + '\n';
            }
        }

        // Clear IndexedDB test data
        function clearIndexedDB() {
            document.getElementById('idb-results').textContent = 'IndexedDB test data cleared. Ready for new tests.';
        }

        // Listen for online/offline events
        window.addEventListener('online', updateStatus);
        window.addEventListener('offline', updateStatus);

        // Listen for beforeinstallprompt event
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            window.deferredPrompt = e;
            document.getElementById('install-results').textContent = '✅ PWA installation prompt is now available';
            document.getElementById('install-button').style.display = 'inline-block';
        });

        // Initialize on page load
        window.addEventListener('load', () => {
            updateStatus();
            loadTestScript();
        });
    </script>
</body>
</html>
