const request = require('supertest');
const WebSocket = require('ws');
const express = require('express');
const http = require('http');
const { initializeWebSocket } = require('../../../server/websocket');

describe('Driver QR WebSocket Integration Tests', () => {
  let app;
  let server;
  let wss;
  let wsClient;

  beforeAll(async () => {
    // Create Express app
    app = express();
    app.use(express.json());

    // Create HTTP server
    server = http.createServer(app);
    
    // Initialize WebSocket
    wss = initializeWebSocket(server);

    // Start server
    await new Promise((resolve) => {
      server.listen(0, resolve);
    });

    // Create WebSocket client for testing
    const port = server.address().port;
    wsClient = new WebSocket(`ws://localhost:${port}`);
    
    await new Promise((resolve, reject) => {
      wsClient.on('open', resolve);
      wsClient.on('error', reject);
    });

    // Authenticate WebSocket client
    await new Promise((resolve) => {
      wsClient.on('message', (data) => {
        const message = JSON.parse(data.toString());
        if (message.type === 'auth_success') {
          resolve();
        }
      });
      
      wsClient.send(JSON.stringify({
        type: 'auth',
        userId: 'test_admin',
        role: 'admin'
      }));
    });
  });

  afterAll(async () => {
    if (wsClient) {
      wsClient.close();
    }
    
    if (server) {
      await new Promise((resolve) => {
        server.close(resolve);
      });
    }
  });

  test('should send WebSocket notification on driver connect', (done) => {
    // Set up WebSocket message listener
    wsClient.on('message', (data) => {
      const message = JSON.parse(data.toString());
      
      if (message.type === 'driver_connected') {
        expect(message.title).toBe('Driver Checked In');
        expect(message.data.driver.employee_id).toBe('DR-001');
        expect(message.data.truck.truck_number).toBe('DT-100');
        expect(message.data.action).toBe('check_in');
        expect(message.icon).toBe('✅');
        expect(message.priority).toBe('medium');
        done();
      }
    });

    // Import and call the WebSocket notification function directly
    const { notifyDriverConnected } = require('../../../server/websocket');
    
    const driverData = {
      id: 1,
      employee_id: 'DR-001',
      full_name: 'John Doe'
    };

    const truckData = {
      id: 1,
      truck_number: 'DT-100',
      license_plate: 'ABC-123'
    };

    const shiftData = {
      shift_id: 1,
      check_in_time: new Date().toISOString()
    };

    notifyDriverConnected(driverData, truckData, shiftData);
  });

  test('should send WebSocket notification on driver disconnect', (done) => {
    // Set up WebSocket message listener
    wsClient.on('message', (data) => {
      const message = JSON.parse(data.toString());
      
      if (message.type === 'driver_disconnected') {
        expect(message.title).toBe('Driver Checked Out');
        expect(message.data.driver.employee_id).toBe('DR-002');
        expect(message.data.truck.truck_number).toBe('DT-200');
        expect(message.data.action).toBe('check_out');
        expect(message.data.shift.duration).toBe('8h 30m');
        expect(message.icon).toBe('⏰');
        expect(message.priority).toBe('medium');
        done();
      }
    });

    // Import and call the WebSocket notification function directly
    const { notifyDriverDisconnected } = require('../../../server/websocket');
    
    const driverData = {
      id: 2,
      employee_id: 'DR-002',
      full_name: 'Jane Smith'
    };

    const truckData = {
      id: 2,
      truck_number: 'DT-200',
      license_plate: 'XYZ-456'
    };

    const shiftData = {
      shift_id: 2,
      check_in_time: new Date(Date.now() - 8.5 * 60 * 60 * 1000).toISOString(),
      check_out_time: new Date().toISOString(),
      duration: '8h 30m',
      duration_ms: 8.5 * 60 * 60 * 1000
    };

    notifyDriverDisconnected(driverData, truckData, shiftData);
  });

  test('should send WebSocket notification on driver handover', (done) => {
    // Set up WebSocket message listener
    wsClient.on('message', (data) => {
      const message = JSON.parse(data.toString());
      
      if (message.type === 'driver_handover') {
        expect(message.title).toBe('Driver Handover');
        expect(message.data.previous_driver.employee_id).toBe('DR-003');
        expect(message.data.new_driver.employee_id).toBe('DR-004');
        expect(message.data.truck.truck_number).toBe('DT-300');
        expect(message.data.action).toBe('handover');
        expect(message.icon).toBe('🔄');
        expect(message.priority).toBe('high');
        done();
      }
    });

    // Import and call the WebSocket notification function directly
    const { notifyDriverHandover } = require('../../../server/websocket');
    
    const previousDriverData = {
      id: 3,
      employee_id: 'DR-003',
      full_name: 'Bob Johnson'
    };

    const newDriverData = {
      id: 4,
      employee_id: 'DR-004',
      full_name: 'Alice Brown'
    };

    const truckData = {
      id: 3,
      truck_number: 'DT-300',
      license_plate: 'DEF-789'
    };

    const handoverData = {
      previous_shift_id: 3,
      shift_id: 4,
      check_in_time: new Date().toISOString()
    };

    notifyDriverHandover(previousDriverData, newDriverData, truckData, handoverData);
  });

  test('should send WebSocket notification on manual checkout', (done) => {
    // Set up WebSocket message listener
    wsClient.on('message', (data) => {
      const message = JSON.parse(data.toString());
      
      if (message.type === 'driver_manual_checkout') {
        expect(message.title).toBe('Manual Driver Checkout');
        expect(message.data.driver.employee_id).toBe('DR-005');
        expect(message.data.truck.truck_number).toBe('DT-400');
        expect(message.data.checkout.reason).toBe('Emergency');
        expect(message.data.supervisor.username).toBe('supervisor1');
        expect(message.data.action).toBe('manual_checkout');
        expect(message.icon).toBe('⚠️');
        expect(message.priority).toBe('high');
        done();
      }
    });

    // Import and call the WebSocket notification function directly
    const { notifyDriverManualCheckout } = require('../../../server/websocket');
    
    const driverData = {
      id: 5,
      employee_id: 'DR-005',
      full_name: 'Charlie Wilson'
    };

    const truckData = {
      id: 4,
      truck_number: 'DT-400'
    };

    const checkoutData = {
      shift_id: 5,
      reason: 'Emergency',
      notes: 'Driver had to leave due to family emergency',
      checkout_time: new Date().toISOString(),
      duration: '6h 15m'
    };

    const supervisorData = {
      id: 1,
      username: 'supervisor1'
    };

    notifyDriverManualCheckout(driverData, truckData, checkoutData, supervisorData);
  });

  test('should handle WebSocket message broadcasting to multiple clients', async () => {
    // Create additional WebSocket clients
    const port = server.address().port;
    const clients = [];
    
    for (let i = 0; i < 3; i++) {
      const client = new WebSocket(`ws://localhost:${port}`);
      
      await new Promise((resolve, reject) => {
        client.on('open', resolve);
        client.on('error', reject);
      });

      // Authenticate client
      await new Promise((resolve) => {
        client.on('message', (data) => {
          const message = JSON.parse(data.toString());
          if (message.type === 'auth_success') {
            resolve();
          }
        });
        
        client.send(JSON.stringify({
          type: 'auth',
          userId: `test_user_${i}`,
          role: 'admin'
        }));
      });

      clients.push(client);
    }

    // Set up message listeners
    const receivedMessages = [];
    clients.forEach((client, index) => {
      client.on('message', (data) => {
        const message = JSON.parse(data.toString());
        if (message.type === 'driver_connected') {
          receivedMessages.push({ clientIndex: index, message });
        }
      });
    });

    // Send notification
    const { notifyDriverConnected } = require('../../../server/websocket');
    
    const driverData = {
      id: 6,
      employee_id: 'DR-006',
      full_name: 'David Lee'
    };

    const truckData = {
      id: 5,
      truck_number: 'DT-500',
      license_plate: 'GHI-012'
    };

    const shiftData = {
      shift_id: 6,
      check_in_time: new Date().toISOString()
    };

    notifyDriverConnected(driverData, truckData, shiftData);

    // Wait for messages to be received
    await new Promise(resolve => setTimeout(resolve, 100));

    // Verify all clients received the message
    expect(receivedMessages).toHaveLength(3);
    
    receivedMessages.forEach(({ message }) => {
      expect(message.type).toBe('driver_connected');
      expect(message.data.driver.employee_id).toBe('DR-006');
      expect(message.data.truck.truck_number).toBe('DT-500');
    });

    // Clean up additional clients
    clients.forEach(client => client.close());
  });

  test('should handle role-based notifications for handovers', (done) => {
    // This test verifies that handover notifications are sent to both admin and supervisor roles
    let messagesReceived = 0;
    
    wsClient.on('message', (data) => {
      const message = JSON.parse(data.toString());
      
      if (message.type === 'driver_handover') {
        messagesReceived++;
        expect(message.title).toBe('Driver Handover');
        expect(message.data.previous_driver.employee_id).toBe('DR-007');
        expect(message.data.new_driver.employee_id).toBe('DR-008');
        expect(message.priority).toBe('high');
        
        // Since we're testing with one client (admin role), we should receive the message
        if (messagesReceived === 1) {
          done();
        }
      }
    });

    // Import and call the WebSocket notification function directly
    const { notifyDriverHandover } = require('../../../server/websocket');
    
    const previousDriverData = {
      id: 7,
      employee_id: 'DR-007',
      full_name: 'Eve Davis'
    };

    const newDriverData = {
      id: 8,
      employee_id: 'DR-008',
      full_name: 'Frank Miller'
    };

    const truckData = {
      id: 6,
      truck_number: 'DT-600',
      license_plate: 'JKL-345'
    };

    const handoverData = {
      previous_shift_id: 7,
      shift_id: 8,
      check_in_time: new Date().toISOString()
    };

    notifyDriverHandover(previousDriverData, newDriverData, truckData, handoverData);
  });
});