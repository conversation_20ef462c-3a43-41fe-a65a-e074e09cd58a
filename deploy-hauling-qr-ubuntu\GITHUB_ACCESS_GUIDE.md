# GitHub Private Repository Access Guide

## 🚀 Quick Start for Fresh Ubuntu VPS

### Step 0: Create a Regular User (Recommended for Production)

For production environments, first create a regular user with sudo privileges:

```bash
# As root, create a new user
adduser deployer

# Add user to sudo group
usermod -aG sudo deployer

# Switch to the new user
su - deployer
# OR log out and log back in as deployer
```

### Step 1: Install Essential Tools

```bash
# Update package lists (as regular user with sudo)
sudo apt update

# Install curl (only essential tool needed)
sudo apt install -y curl

# Create directory for deployment
mkdir -p ~/hauling-deployment
cd ~/hauling-deployment
```

### Step 2: Download the Initial Download Script

```bash
# Download the script with curl (no sudo needed)
curl -H "Authorization: token *********************************************************************************************" \
     -H "Accept: application/vnd.github.v3.raw" \
     -o download-deployment.sh \
     https://api.github.com/repos/mightybadz18/hauling-qr-trip-management/contents/deploy-hauling-qr-ubuntu/download-deployment.sh

# Make it executable (no sudo needed)
chmod +x download-deployment.sh
```

### Step 3: Run the Download Script

```bash
# Run the download script to get all deployment files (no sudo needed)
./download-deployment.sh

# When prompted, choose option 1 to download deployment scripts only
```

### Step 4: Run Deployment

```bash
# Run the deployment with sudo (requires sudo for system-level operations)
sudo ./run-deployment.sh
```

This approach gives you full control over the deployment process and is the recommended method for production environments.

### User Permissions and Deployment Steps

For a complete step-by-step deployment process, see the [DEPLOYMENT_STEPS.md](DEPLOYMENT_STEPS.md) file.

For detailed guidance on user permissions, see the [USER_PERMISSIONS.md](USER_PERMISSIONS.md) file.

**Quick Summary:**
- **Production environments**: Use a regular user with sudo (more secure)
- **Development/testing**: Root is acceptable for simplicity

The deployment script requires root privileges for system-level operations, but you can run it with sudo from a regular user account.

### Special Environments

#### Docker/Container Environments
If you're using Docker or a container environment:

```bash
# Install curl first (as root in container)
apt update && apt install -y curl

# Download and run as in the standard steps
curl -H "Authorization: token *********************************************************************************************" \
     -H "Accept: application/vnd.github.v3.raw" \
     -o download-deployment.sh \
     https://api.github.com/repos/mightybadz18/hauling-qr-trip-management/contents/deploy-hauling-qr-ubuntu/download-deployment.sh

chmod +x download-deployment.sh
./download-deployment.sh
```

#### Minimal VPS (Limited Memory)
If you're on a very small VPS with limited memory:

```bash
# Choose option 4 (quick download) when running the script
./download-deployment.sh
# Enter "4" when prompted

# This downloads only the essential files to minimize memory usage
```

## Method 1: Personal Access Token (Recommended)

### Step 1: Create Personal Access Token
1. Go to GitHub.com → Settings → Developer settings → Personal access tokens → Tokens (classic)
2. Click "Generate new token (classic)"
3. Give it a name like "Ubuntu Deployment"
4. Select scopes:
   - ✅ `repo` (Full control of private repositories)
   - ✅ `read:org` (Read org and team membership)
5. Click "Generate token"
6. **COPY THE TOKEN IMMEDIATELY** (you won't see it again)

### Step 2: Clone with Token
```bash
# Replace YOUR_TOKEN with your actual token
git clone https://<EMAIL>/mightybadz18/hauling-qr-trip-management.git

# Or set it in the URL like this:
git clone https://<EMAIL>/mightybadz18/hauling-qr-trip-management.git
```

## Method 2: SSH Keys (More Secure)

### Step 1: Generate SSH Key on Ubuntu
```bash
# Generate SSH key
ssh-keygen -t ed25519 -C "<EMAIL>"

# Start SSH agent
eval "$(ssh-agent -s)"

# Add key to agent
ssh-add ~/.ssh/id_ed25519

# Copy public key
cat ~/.ssh/id_ed25519.pub
```

### Step 2: Add SSH Key to GitHub
1. Go to GitHub.com → Settings → SSH and GPG keys
2. Click "New SSH key"
3. Paste the public key content
4. Save

### Step 3: Clone with SSH
```bash
<NAME_EMAIL>:mightybadz18/hauling-qr-trip-management.git
```

## Method 3: Download Deployment Scripts Only

If you just need the deployment scripts, you can download them directly:

```bash
# Create directory
mkdir -p ~/hauling-deployment
cd ~/hauling-deployment

# Download individual files using curl
curl -H "Authorization: token YOUR_TOKEN" \
     -H "Accept: application/vnd.github.v3.raw" \
     -o deploy-hauling-qr-ubuntu-fixed.sh \
     https://api.github.com/repos/mightybadz18/hauling-qr-trip-management/contents/deploy-hauling-qr-ubuntu/deploy-hauling-qr-ubuntu-fixed.sh

curl -H "Authorization: token YOUR_TOKEN" \
     -H "Accept: application/vnd.github.v3.raw" \
     -o run-deployment.sh \
     https://api.github.com/repos/mightybadz18/hauling-qr-trip-management/contents/deploy-hauling-qr-ubuntu/run-deployment.sh

curl -H "Authorization: token YOUR_TOKEN" \
     -H "Accept: application/vnd.github.v3.raw" \
     -o deployment-config.conf \
     https://api.github.com/repos/mightybadz18/hauling-qr-trip-management/contents/deploy-hauling-qr-ubuntu/deployment-config.conf

# Make scripts executable
chmod +x deploy-hauling-qr-ubuntu-fixed.sh
chmod +x run-deployment.sh
```

## Method 4: Use GitHub CLI

```bash
# Install GitHub CLI
curl -fsSL https://cli.github.com/packages/githubcli-archive-keyring.gpg | sudo dd of=/usr/share/keyrings/githubcli-archive-keyring.gpg
echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/githubcli-archive-keyring.gpg] https://cli.github.com/packages stable main" | sudo tee /etc/apt/sources.list.d/github-cli.list > /dev/null
sudo apt update
sudo apt install gh

# Login to GitHub
gh auth login

# Clone repository
gh repo clone mightybadz18/hauling-qr-trip-management
```

## Troubleshooting

### Error: "Repository not found"
- Check if the repository name is correct
- Verify your token has the right permissions
- Make sure the repository exists and you have access

### Error: "Authentication failed"
- Your token might be expired
- Check if the token has `repo` scope
- Try regenerating the token

### Error: "Permission denied"
- For SSH: Check if your SSH key is added to GitHub
- For HTTPS: Verify your token is correct

## Security Best Practices

1. **Never commit tokens to code**
2. **Use environment variables for tokens**
3. **Rotate tokens regularly**
4. **Use SSH keys for long-term access**
5. **Limit token scopes to minimum required**