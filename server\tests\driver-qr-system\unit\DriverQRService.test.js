const DriverQRService = require('../../../services/DriverQRService');
const DriverQRCodeGenerator = require('../../../utils/DriverQRCodeGenerator');
const { query, getClient } = require('../../../config/database');

// Mock dependencies
jest.mock('../../../config/database', () => ({
  query: jest.fn(),
  getClient: jest.fn()
}));

jest.mock('../../../utils/DriverQRCodeGenerator');
jest.mock('../../../utils/logger', () => ({
  logError: jest.fn(),
  logInfo: jest.fn()
}));

describe('DriverQRService', () => {
  let mockClient;

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock database client
    mockClient = {
      query: jest.fn(),
      release: jest.fn()
    };
    getClient.mockResolvedValue(mockClient);
  });

  describe('authenticateDriver', () => {
    const validQRData = {
      id: 'DR-001',
      driver_id: 123,
      employee_id: 'DR-001',
      type: 'driver'
    };

    const mockDriver = {
      id: 123,
      employee_id: 'DR-001',
      full_name: 'John Doe',
      status: 'active'
    };

    it('should successfully authenticate valid driver', async () => {
      DriverQRCodeGenerator.validateDriverQR.mockResolvedValue({
        success: true,
        valid: true,
        driver: mockDriver,
        qr_data: validQRData
      });

      const result = await DriverQRService.authenticateDriver(validQRData);

      expect(result.success).toBe(true);
      expect(result.driver).toEqual(mockDriver);
      expect(result.qr_data).toEqual(validQRData);
    });

    it('should fail authentication for invalid driver', async () => {
      DriverQRCodeGenerator.validateDriverQR.mockResolvedValue({
        success: false,
        valid: false,
        error: 'Driver account is inactive. Please contact your supervisor.'
      });

      const result = await DriverQRService.authenticateDriver(validQRData);

      expect(result.success).toBe(false);
      expect(result.error).toBe('AUTHENTICATION_FAILED');
      expect(result.message).toBe('Driver account is inactive. Please contact your supervisor.');
      expect(result.driver).toBe(null);
    });

    it('should handle authentication errors gracefully', async () => {
      DriverQRCodeGenerator.validateDriverQR.mockRejectedValue(new Error('Database error'));

      const result = await DriverQRService.authenticateDriver(validQRData);

      expect(result.success).toBe(false);
      expect(result.error).toBe('AUTHENTICATION_ERROR');
      expect(result.driver).toBe(null);
    });
  });

  describe('validateTruck', () => {
    const validTruckQR = {
      id: 'DT-100',
      type: 'truck'
    };

    const mockTruck = {
      id: 456,
      truck_number: 'DT-100',
      license_plate: 'ABC-123',
      status: 'active',
      qr_code_data: validTruckQR
    };

    it('should successfully validate active truck', async () => {
      query.mockResolvedValue({ rows: [mockTruck] });

      const result = await DriverQRService.validateTruck(validTruckQR);

      expect(result.success).toBe(true);
      expect(result.truck).toEqual({
        id: 456,
        truck_number: 'DT-100',
        license_plate: 'ABC-123',
        status: 'active'
      });
      expect(result.qr_data).toEqual(validTruckQR);
    });

    it('should validate truck from JSON string', async () => {
      query.mockResolvedValue({ rows: [mockTruck] });

      const result = await DriverQRService.validateTruck(JSON.stringify(validTruckQR));

      expect(result.success).toBe(true);
      expect(result.truck.truck_number).toBe('DT-100');
    });

    it('should reject invalid JSON format', async () => {
      const result = await DriverQRService.validateTruck('invalid json');

      expect(result.success).toBe(false);
      expect(result.error).toBe('INVALID_QR_FORMAT');
      expect(result.message).toBe('Truck QR code is not valid JSON format');
    });

    it('should reject invalid QR structure', async () => {
      const invalidQR = { id: 'DT-100' }; // Missing type

      const result = await DriverQRService.validateTruck(invalidQR);

      expect(result.success).toBe(false);
      expect(result.error).toBe('INVALID_QR_STRUCTURE');
    });

    it('should reject non-existent truck', async () => {
      query.mockResolvedValue({ rows: [] });

      const result = await DriverQRService.validateTruck(validTruckQR);

      expect(result.success).toBe(false);
      expect(result.error).toBe('TRUCK_NOT_FOUND');
    });

    it('should reject inactive truck', async () => {
      const inactiveTruck = { ...mockTruck, status: 'maintenance' };
      query.mockResolvedValue({ rows: [inactiveTruck] });

      const result = await DriverQRService.validateTruck(validTruckQR);

      expect(result.success).toBe(false);
      expect(result.error).toBe('TRUCK_INACTIVE');
      expect(result.message).toBe('Truck is not available for assignment. Please contact maintenance or supervisor.');
    });

    it('should reject QR data mismatch', async () => {
      const mismatchedTruck = {
        ...mockTruck,
        qr_code_data: { id: 'DT-200', type: 'truck' }
      };
      query.mockResolvedValue({ rows: [mismatchedTruck] });

      const result = await DriverQRService.validateTruck(validTruckQR);

      expect(result.success).toBe(false);
      expect(result.error).toBe('QR_DATA_MISMATCH');
    });
  });

  describe('getCurrentDriverShift', () => {
    it('should return current active shift', async () => {
      const mockShift = {
        id: 789,
        truck_id: 456,
        start_date: '2025-01-01',
        start_time: '08:00:00',
        truck_number: 'DT-100',
        license_plate: 'ABC-123'
      };

      query.mockResolvedValue({ rows: [mockShift] });

      const result = await DriverQRService.getCurrentDriverShift(123);

      expect(result).toEqual(mockShift);
    });

    it('should return null when no active shift', async () => {
      query.mockResolvedValue({ rows: [] });

      const result = await DriverQRService.getCurrentDriverShift(123);

      expect(result).toBe(null);
    });

    it('should handle database errors', async () => {
      query.mockRejectedValue(new Error('Database error'));

      await expect(DriverQRService.getCurrentDriverShift(123))
        .rejects.toThrow('Database error');
    });
  });

  describe('processDriverTruckConnection', () => {
    const driverId = 123;
    const truckId = 456;

    it('should handle check-in with no active shift', async () => {
      // Reset mock and set up the sequence
      mockClient.query.mockReset();
      mockClient.query
        .mockResolvedValueOnce(undefined) // BEGIN
        .mockResolvedValueOnce({ rows: [] }) // No active shift (FOR UPDATE query)
        .mockResolvedValueOnce({ rows: [{ id: 789 }] }) // Insert new shift
        .mockResolvedValueOnce({ rows: [{ truck_number: 'DT-100' }] }) // Get truck info
        .mockResolvedValueOnce(undefined); // COMMIT

      const result = await DriverQRService.processDriverTruckConnection(
        driverId, truckId, 'check_in'
      );

      expect(result.success).toBe(true);
      expect(result.action).toBe('check_in');
      expect(result.truck).toBe('DT-100');
      expect(result.shift_id).toBe(789);
    });

    it('should handle check-out with active shift', async () => {
      const mockActiveShift = {
        id: 789,
        truck_id: truckId,
        start_date: '2025-01-01',
        start_time: '08:00:00',
        truck_number: 'DT-100'
      };

      mockClient.query.mockReset();
      mockClient.query
        .mockResolvedValueOnce(undefined) // BEGIN
        .mockResolvedValueOnce({ rows: [mockActiveShift] }) // Active shift found (FOR UPDATE)
        .mockResolvedValueOnce(undefined) // Update shift to completed
        .mockResolvedValueOnce(undefined); // COMMIT

      const result = await DriverQRService.processDriverTruckConnection(
        driverId, truckId, 'check_out'
      );

      expect(result.success).toBe(true);
      expect(result.action).toBe('check_out');
      expect(result.truck).toBe('DT-100');
      expect(result.shift_id).toBe(789);
      expect(result).toHaveProperty('duration');
    });

    it('should handle handover scenario', async () => {
      const mockActiveShift = {
        id: 789,
        truck_id: 999, // Different truck
        start_date: '2025-01-01',
        start_time: '08:00:00',
        truck_number: 'DT-200'
      };

      mockClient.query.mockReset();
      mockClient.query
        .mockResolvedValueOnce(undefined) // BEGIN
        .mockResolvedValueOnce({ rows: [mockActiveShift] }) // Active shift on different truck (FOR UPDATE)
        .mockResolvedValueOnce(undefined) // Update old shift
        .mockResolvedValueOnce({ rows: [{ id: 890 }] }) // Insert new shift
        .mockResolvedValueOnce({ rows: [{ truck_number: 'DT-100' }] }) // Get new truck info
        .mockResolvedValueOnce(undefined); // COMMIT

      const result = await DriverQRService.processDriverTruckConnection(
        driverId, truckId, 'check_in'
      );

      expect(result.success).toBe(true);
      expect(result.action).toBe('handover');
      expect(result.previous_truck).toBe('DT-200');
      expect(result.new_truck).toBe('DT-100');
      expect(result.shift_id).toBe(890);
    });

    it('should reject check-out with no active shift', async () => {
      mockClient.query.mockReset();
      mockClient.query
        .mockResolvedValueOnce(undefined) // BEGIN
        .mockResolvedValueOnce({ rows: [] }) // No active shift (FOR UPDATE)
        .mockResolvedValueOnce(undefined); // ROLLBACK

      const result = await DriverQRService.processDriverTruckConnection(
        driverId, truckId, 'check_out'
      );

      expect(result.success).toBe(false);
      expect(result.error).toBe('NO_ACTIVE_SHIFT');
    });

    it('should reject check-out from wrong truck', async () => {
      const mockActiveShift = {
        id: 789,
        truck_id: 999, // Different truck
        start_date: '2025-01-01',
        start_time: '08:00:00',
        truck_number: 'DT-200'
      };

      mockClient.query.mockReset();
      mockClient.query
        .mockResolvedValueOnce(undefined) // BEGIN
        .mockResolvedValueOnce({ rows: [mockActiveShift] }) // Active shift on different truck (FOR UPDATE)
        .mockResolvedValueOnce(undefined); // ROLLBACK

      const result = await DriverQRService.processDriverTruckConnection(
        driverId, truckId, 'check_out'
      );

      expect(result.success).toBe(false);
      expect(result.error).toBe('TRUCK_MISMATCH');
      expect(result.message).toContain('DT-200');
    });

    it('should handle database errors with rollback', async () => {
      mockClient.query.mockReset();
      mockClient.query
        .mockResolvedValueOnce(undefined) // BEGIN
        .mockRejectedValueOnce(new Error('Database error')) // Query fails
        .mockResolvedValueOnce(undefined); // ROLLBACK

      await expect(
        DriverQRService.processDriverTruckConnection(driverId, truckId, 'check_in')
      ).rejects.toThrow('Database error');

      expect(mockClient.release).toHaveBeenCalled();
    });
  });

  describe('getDriverShiftHistory', () => {
    const mockShifts = [
      {
        id: 1,
        truck_id: 456,
        shift_type: 'custom',
        start_date: '2025-01-01',
        end_date: '2025-01-01',
        start_time: '08:00:00',
        end_time: '16:00:00',
        status: 'completed',
        auto_created: true,
        truck_number: 'DT-100',
        license_plate: 'ABC-123',
        employee_id: 'DR-001',
        full_name: 'John Doe'
      }
    ];

    it('should return driver shift history with durations', async () => {
      query.mockResolvedValue({ rows: mockShifts });

      const result = await DriverQRService.getDriverShiftHistory(123);

      expect(result.success).toBe(true);
      expect(result.shifts).toHaveLength(1);
      expect(result.shifts[0]).toHaveProperty('duration');
      expect(result.shifts[0]).toHaveProperty('duration_ms');
      expect(result.shifts[0].duration).toBe('8h 0m');
    });

    it('should handle date filtering', async () => {
      query.mockResolvedValue({ rows: mockShifts });

      const options = {
        date_from: '2025-01-01',
        date_to: '2025-01-31',
        limit: 10,
        offset: 0
      };

      await DriverQRService.getDriverShiftHistory(123, options);

      expect(query).toHaveBeenCalledWith(
        expect.stringContaining('ds.start_date >= $2'),
        expect.arrayContaining([123, '2025-01-01', '2025-01-31', 10, 0])
      );
    });

    it('should handle database errors', async () => {
      query.mockRejectedValue(new Error('Database error'));

      await expect(DriverQRService.getDriverShiftHistory(123))
        .rejects.toThrow('Database error');
    });
  });

  describe('getActiveDriverForTruck', () => {
    it('should return active driver for truck', async () => {
      const mockDriver = {
        shift_id: 789,
        start_date: '2025-01-01',
        start_time: '08:00:00',
        driver_id: 123,
        employee_id: 'DR-001',
        full_name: 'John Doe',
        truck_number: 'DT-100'
      };

      query.mockResolvedValue({ rows: [mockDriver] });

      const result = await DriverQRService.getActiveDriverForTruck(456);

      expect(result.success).toBe(true);
      expect(result.has_active_driver).toBe(true);
      expect(result.driver).toEqual(mockDriver);
    });

    it('should return no active driver when truck is unassigned', async () => {
      query.mockResolvedValue({ rows: [] });

      const result = await DriverQRService.getActiveDriverForTruck(456);

      expect(result.success).toBe(true);
      expect(result.has_active_driver).toBe(false);
      expect(result.driver).toBe(null);
    });
  });

  describe('validateDriverTruckAction', () => {
    it('should validate check-in action with no current shift', async () => {
      // Mock getCurrentDriverShift to return null
      jest.spyOn(DriverQRService, 'getCurrentDriverShift').mockResolvedValue(null);
      
      // Mock getActiveDriverForTruck to return no active driver
      jest.spyOn(DriverQRService, 'getActiveDriverForTruck').mockResolvedValue({
        has_active_driver: false,
        driver: null
      });

      const result = await DriverQRService.validateDriverTruckAction(123, 456, 'check_in');

      expect(result.success).toBe(true);
      expect(result.can_perform).toBe(true);
      expect(result.suggested_action).toBe('check_in');
    });

    it('should suggest handover when driver has different active shift', async () => {
      const currentShift = {
        id: 789,
        truck_id: 999,
        truck_number: 'DT-200'
      };

      jest.spyOn(DriverQRService, 'getCurrentDriverShift').mockResolvedValue(currentShift);
      jest.spyOn(DriverQRService, 'getActiveDriverForTruck').mockResolvedValue({
        has_active_driver: false,
        driver: null
      });

      const result = await DriverQRService.validateDriverTruckAction(123, 456, 'check_in');

      expect(result.success).toBe(true);
      expect(result.can_perform).toBe(true);
      expect(result.suggested_action).toBe('handover');
      expect(result.message).toContain('DT-200');
    });

    it('should reject check-out with no active shift', async () => {
      jest.spyOn(DriverQRService, 'getCurrentDriverShift').mockResolvedValue(null);
      jest.spyOn(DriverQRService, 'getActiveDriverForTruck').mockResolvedValue({
        has_active_driver: false,
        driver: null
      });

      const result = await DriverQRService.validateDriverTruckAction(123, 456, 'check_out');

      expect(result.success).toBe(true);
      expect(result.can_perform).toBe(false);
      expect(result.message).toBe('No active shift to check out from');
    });
  });
});