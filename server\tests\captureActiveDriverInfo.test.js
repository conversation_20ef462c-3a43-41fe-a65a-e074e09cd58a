const { query, getClient } = require('../config/database');
const { logger } = require('../utils/logger');

// Import the scanner module to access captureActiveDriverInfo
// We need to extract the function from the scanner.js file
const fs = require('fs');
const path = require('path');

// Read and evaluate the scanner.js file to extract captureActiveDriverInfo
const scannerPath = path.join(__dirname, '../routes/scanner.js');
const scannerContent = fs.readFileSync(scannerPath, 'utf8');

// Extract the captureActiveDriverInfo function using regex
const functionMatch = scannerContent.match(/async function captureActiveDriverInfo\([\s\S]*?\n\}/);
if (!functionMatch) {
  throw new Error('captureActiveDriverInfo function not found in scanner.js');
}

// Create a test module that includes the function
const testModule = `
const { query, getClient } = require('../config/database');
const { logger, EnhancedLogger } = require('../utils/logger');
const DataFlowLogger = require('../utils/DataFlowLogger');

// Enhanced logging functions for critical operations only
function logDebug(context, message, data = {}) {
  if (context.includes('TRANSACTION') || context.includes('VALIDATION') || context.includes('WORKFLOW')) {
    logger.info(message, {
      context: \`SCANNER.\${context}\`,
      data
    });
  }
}

function logError(context, error, additionalData = {}) {
  EnhancedLogger.logScanError(context, error, additionalData.scanData || {}, additionalData.userInfo || {});
}

${functionMatch[0]}

module.exports = { captureActiveDriverInfo };
`;

// Write temporary test module
const tempModulePath = path.join(__dirname, 'temp-captureActiveDriverInfo.js');
fs.writeFileSync(tempModulePath, testModule);

// Import the function
const { captureActiveDriverInfo } = require('./temp-captureActiveDriverInfo');

describe('captureActiveDriverInfo - Overnight Scenarios and Status Protection', () => {
  let client;
  let testTruckId;
  let testDriverId;
  let testShiftId;

  beforeAll(async () => {
    client = await getClient();
    
    // Create test truck
    const truckResult = await client.query(`
      INSERT INTO dump_trucks (truck_number, license_plate, status, qr_code_data)
      VALUES ('TEST001', 'TEST-001', 'active', '{"type": "truck", "id": "TEST001"}')
      RETURNING id
    `);
    testTruckId = truckResult.rows[0].id;

    // Create test driver
    const driverResult = await client.query(`
      INSERT INTO drivers (full_name, employee_id, status)
      VALUES ('Test Driver', 'EMP001', 'active')
      RETURNING id
    `);
    testDriverId = driverResult.rows[0].id;
  });

  afterAll(async () => {
    // Clean up test data
    await client.query('DELETE FROM driver_shifts WHERE driver_id = $1', [testDriverId]);
    await client.query('DELETE FROM drivers WHERE id = $1', [testDriverId]);
    await client.query('DELETE FROM dump_trucks WHERE id = $1', [testTruckId]);
    
    client.release();
    
    // Clean up temporary module
    if (fs.existsSync(tempModulePath)) {
      fs.unlinkSync(tempModulePath);
    }
  });

  beforeEach(async () => {
    // Clean up any existing shifts before each test
    await client.query('DELETE FROM driver_shifts WHERE driver_id = $1', [testDriverId]);
  });

  describe('Overnight Shift Scenarios', () => {
    test('Day shift check-in 08:00 AM, trip creation 10:00 PM same day - should find active driver', async () => {
      // Create QR-created day shift: July 28 08:00 AM check-in
      const checkInTime = new Date('2024-07-28T08:00:00');
      const shiftResult = await client.query(`
        INSERT INTO driver_shifts (
          truck_id, driver_id, shift_type, start_date, end_date,
          start_time, end_time, status, auto_created, created_at, updated_at
        ) VALUES ($1, $2, 'day', $3, NULL, $4, NULL, 'active', true, $5, $6)
        RETURNING id
      `, [
        testTruckId, testDriverId, '2024-07-28', '08:00:00',
        checkInTime, checkInTime
      ]);
      testShiftId = shiftResult.rows[0].id;

      // Test trip creation at 10:00 PM same day
      const tripTime = new Date('2024-07-28T22:00:00');
      const result = await captureActiveDriverInfo(client, testTruckId, tripTime);

      expect(result).not.toBeNull();
      expect(result.driver_id).toBe(testDriverId);
      expect(result.driver_name).toBe('Test Driver');
      expect(result.employee_id).toBe('EMP001');
      expect(result.shift_type).toBe('day');
      expect(result.shift_id).toBe(testShiftId);
    });

    test('Night shift check-in 10:00 PM, trip creation 02:00 AM next day - should find active driver', async () => {
      // Create QR-created night shift: July 28 22:00 PM check-in
      const checkInTime = new Date('2024-07-28T22:00:00');
      const shiftResult = await client.query(`
        INSERT INTO driver_shifts (
          truck_id, driver_id, shift_type, start_date, end_date,
          start_time, end_time, status, auto_created, created_at, updated_at
        ) VALUES ($1, $2, 'night', $3, NULL, $4, NULL, 'active', true, $5, $6)
        RETURNING id
      `, [
        testTruckId, testDriverId, '2024-07-28', '22:00:00',
        checkInTime, checkInTime
      ]);
      testShiftId = shiftResult.rows[0].id;

      // Test trip creation at 02:00 AM next day
      const tripTime = new Date('2024-07-29T02:00:00');
      const result = await captureActiveDriverInfo(client, testTruckId, tripTime);

      expect(result).not.toBeNull();
      expect(result.driver_id).toBe(testDriverId);
      expect(result.driver_name).toBe('Test Driver');
      expect(result.employee_id).toBe('EMP001');
      expect(result.shift_type).toBe('night');
      expect(result.shift_id).toBe(testShiftId);
    });

    test('Day shift check-in 08:00 AM, check-out 08:00 AM next day, trip creation 10:00 AM - should NOT find driver', async () => {
      // Create completed QR-created day shift: July 28 08:00 AM to July 29 08:00 AM
      const checkInTime = new Date('2024-07-28T08:00:00');
      const checkOutTime = new Date('2024-07-29T08:00:00');
      
      await client.query(`
        INSERT INTO driver_shifts (
          truck_id, driver_id, shift_type, start_date, end_date,
          start_time, end_time, status, auto_created, created_at, updated_at
        ) VALUES ($1, $2, 'day', $3, $4, $5, $6, 'completed', true, $7, $8)
      `, [
        testTruckId, testDriverId, '2024-07-28', '2024-07-29',
        '08:00:00', '08:00:00', checkInTime, checkOutTime
      ]);

      // Test trip creation at 10:00 AM after check-out
      const tripTime = new Date('2024-07-29T10:00:00');
      const result = await captureActiveDriverInfo(client, testTruckId, tripTime);

      expect(result).toBeNull();
    });
  });

  describe('Status Protection Tests', () => {
    test('QR-created active shift should remain active during automated function calls', async () => {
      // Create QR-created day shift: July 28 08:00 AM check-in (still active)
      const checkInTime = new Date('2024-07-28T08:00:00');
      const shiftResult = await client.query(`
        INSERT INTO driver_shifts (
          truck_id, driver_id, shift_type, start_date, end_date,
          start_time, end_time, status, auto_created, created_at, updated_at
        ) VALUES ($1, $2, 'day', $3, NULL, $4, NULL, 'active', true, $5, $6)
        RETURNING id
      `, [
        testTruckId, testDriverId, '2024-07-28', '08:00:00',
        checkInTime, checkInTime
      ]);
      testShiftId = shiftResult.rows[0].id;

      // Simulate automated function call at 3:00 PM same day
      const automatedCallTime = new Date('2024-07-28T15:00:00');
      
      // Call update_all_shift_statuses function
      await client.query('SELECT update_all_shift_statuses($1)', [automatedCallTime]);

      // Verify shift status remains 'active'
      const statusCheck = await client.query(
        'SELECT status, auto_created FROM driver_shifts WHERE id = $1',
        [testShiftId]
      );

      expect(statusCheck.rows[0].status).toBe('active');
      expect(statusCheck.rows[0].auto_created).toBe(true);

      // Test driver capture still works
      const result = await captureActiveDriverInfo(client, testTruckId, automatedCallTime);
      expect(result).not.toBeNull();
      expect(result.shift_id).toBe(testShiftId);
    });

    test('QR-created active shift should remain active during overnight automated calls', async () => {
      // Create QR-created day shift: July 28 08:00 AM check-in (still active)
      const checkInTime = new Date('2024-07-28T08:00:00');
      const shiftResult = await client.query(`
        INSERT INTO driver_shifts (
          truck_id, driver_id, shift_type, start_date, end_date,
          start_time, end_time, status, auto_created, created_at, updated_at
        ) VALUES ($1, $2, 'day', $3, NULL, $4, NULL, 'active', true, $5, $6)
        RETURNING id
      `, [
        testTruckId, testDriverId, '2024-07-28', '08:00:00',
        checkInTime, checkInTime
      ]);
      testShiftId = shiftResult.rows[0].id;

      // Simulate automated function call at 2:00 AM next day
      const overnightCallTime = new Date('2024-07-29T02:00:00');
      
      // Call update_all_shift_statuses function
      await client.query('SELECT update_all_shift_statuses($1)', [overnightCallTime]);

      // Verify shift status remains 'active'
      const statusCheck = await client.query(
        'SELECT status, auto_created FROM driver_shifts WHERE id = $1',
        [testShiftId]
      );

      expect(statusCheck.rows[0].status).toBe('active');
      expect(statusCheck.rows[0].auto_created).toBe(true);

      // Test driver capture still works during overnight period
      const result = await captureActiveDriverInfo(client, testTruckId, overnightCallTime);
      expect(result).not.toBeNull();
      expect(result.shift_id).toBe(testShiftId);
    });

    test('Only manual check-out should change QR-created shift from active to completed', async () => {
      // Create QR-created day shift: July 28 08:00 AM check-in (still active)
      const checkInTime = new Date('2024-07-28T08:00:00');
      const shiftResult = await client.query(`
        INSERT INTO driver_shifts (
          truck_id, driver_id, shift_type, start_date, end_date,
          start_time, end_time, status, auto_created, created_at, updated_at
        ) VALUES ($1, $2, 'day', $3, NULL, $4, NULL, 'active', true, $5, $6)
        RETURNING id
      `, [
        testTruckId, testDriverId, '2024-07-28', '08:00:00',
        checkInTime, checkInTime
      ]);
      testShiftId = shiftResult.rows[0].id;

      // Multiple automated function calls should not change status
      const times = [
        new Date('2024-07-28T15:00:00'), // 3:00 PM same day
        new Date('2024-07-28T23:00:00'), // 11:00 PM same day
        new Date('2024-07-29T02:00:00'), // 2:00 AM next day
        new Date('2024-07-29T06:00:00')  // 6:00 AM next day
      ];

      for (const time of times) {
        await client.query('SELECT update_all_shift_statuses($1)', [time]);
        
        const statusCheck = await client.query(
          'SELECT status FROM driver_shifts WHERE id = $1',
          [testShiftId]
        );
        expect(statusCheck.rows[0].status).toBe('active');
      }

      // Simulate manual check-out at 8:00 AM next day
      const checkOutTime = new Date('2024-07-29T08:00:00');
      await client.query(`
        UPDATE driver_shifts 
        SET status = 'completed', end_date = $1, end_time = $2, updated_at = $3
        WHERE id = $4
      `, ['2024-07-29', '08:00:00', checkOutTime, testShiftId]);

      // Verify status is now completed
      const finalStatusCheck = await client.query(
        'SELECT status, end_date, end_time FROM driver_shifts WHERE id = $1',
        [testShiftId]
      );
      expect(finalStatusCheck.rows[0].status).toBe('completed');
      expect(finalStatusCheck.rows[0].end_date).toEqual(new Date('2024-07-29'));
      expect(finalStatusCheck.rows[0].end_time).toBe('08:00:00');

      // Driver capture should now return null
      const result = await captureActiveDriverInfo(client, testTruckId, new Date('2024-07-29T10:00:00'));
      expect(result).toBeNull();
    });
  });

  describe('Fallback Mechanism Tests', () => {
    test('Primary query fails, QR fallback succeeds', async () => {
      // Create QR-created shift with unusual time pattern that might confuse primary query
      const checkInTime = new Date('2024-07-28T08:00:00');
      const shiftResult = await client.query(`
        INSERT INTO driver_shifts (
          truck_id, driver_id, shift_type, start_date, end_date,
          start_time, end_time, status, auto_created, created_at, updated_at
        ) VALUES ($1, $2, 'custom', $3, NULL, $4, NULL, 'active', true, $5, $6)
        RETURNING id
      `, [
        testTruckId, testDriverId, '2024-07-28', '08:00:00',
        checkInTime, checkInTime
      ]);
      testShiftId = shiftResult.rows[0].id;

      // Test at a time that should trigger fallback
      const tripTime = new Date('2024-07-28T14:00:00');
      const result = await captureActiveDriverInfo(client, testTruckId, tripTime);

      expect(result).not.toBeNull();
      expect(result.driver_id).toBe(testDriverId);
      expect(result.shift_type).toBe('custom');
    });
  });

  describe('Performance and Data Quality Tests', () => {
    test('Function performance under load', async () => {
      // Create QR-created shift
      const checkInTime = new Date('2024-07-28T08:00:00');
      await client.query(`
        INSERT INTO driver_shifts (
          truck_id, driver_id, shift_type, start_date, end_date,
          start_time, end_time, status, auto_created, created_at, updated_at
        ) VALUES ($1, $2, 'day', $3, NULL, $4, NULL, 'active', true, $5, $6)
      `, [
        testTruckId, testDriverId, '2024-07-28', '08:00:00',
        checkInTime, checkInTime
      ]);

      // Test multiple rapid calls
      const startTime = Date.now();
      const promises = [];
      
      for (let i = 0; i < 10; i++) {
        promises.push(captureActiveDriverInfo(client, testTruckId, new Date('2024-07-28T14:00:00')));
      }

      const results = await Promise.all(promises);
      const endTime = Date.now();

      // All calls should succeed
      results.forEach(result => {
        expect(result).not.toBeNull();
        expect(result.driver_id).toBe(testDriverId);
      });

      // Performance should be reasonable (under 5 seconds for 10 calls)
      expect(endTime - startTime).toBeLessThan(5000);
    });

    test('Data consistency validation', async () => {
      // Create QR-created shift
      const checkInTime = new Date('2024-07-28T08:00:00');
      const shiftResult = await client.query(`
        INSERT INTO driver_shifts (
          truck_id, driver_id, shift_type, start_date, end_date,
          start_time, end_time, status, auto_created, created_at, updated_at
        ) VALUES ($1, $2, 'day', $3, NULL, $4, NULL, 'active', true, $5, $6)
        RETURNING id
      `, [
        testTruckId, testDriverId, '2024-07-28', '08:00:00',
        checkInTime, checkInTime
      ]);
      testShiftId = shiftResult.rows[0].id;

      const result = await captureActiveDriverInfo(client, testTruckId, new Date('2024-07-28T14:00:00'));

      // Validate all required fields are present and correct
      expect(result.driver_id).toBeDefined();
      expect(result.driver_name).toBeDefined();
      expect(result.employee_id).toBeDefined();
      expect(result.shift_id).toBeDefined();
      expect(result.shift_type).toBeDefined();

      expect(typeof result.driver_id).toBe('number');
      expect(typeof result.driver_name).toBe('string');
      expect(typeof result.employee_id).toBe('string');
      expect(typeof result.shift_id).toBe('number');
      expect(typeof result.shift_type).toBe('string');

      // Validate data matches database
      const dbCheck = await client.query(`
        SELECT ds.id, ds.shift_type, d.id as driver_id, d.full_name, d.employee_id
        FROM driver_shifts ds
        JOIN drivers d ON ds.driver_id = d.id
        WHERE ds.id = $1
      `, [testShiftId]);

      expect(result.driver_id).toBe(dbCheck.rows[0].driver_id);
      expect(result.driver_name).toBe(dbCheck.rows[0].full_name);
      expect(result.employee_id).toBe(dbCheck.rows[0].employee_id);
      expect(result.shift_id).toBe(dbCheck.rows[0].id);
      expect(result.shift_type).toBe(dbCheck.rows[0].shift_type);
    });
  });
});