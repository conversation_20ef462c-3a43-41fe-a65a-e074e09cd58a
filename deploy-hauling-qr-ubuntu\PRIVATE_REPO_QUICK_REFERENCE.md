# Private Repository Quick Reference

## 🔐 Authentication Required

This repository is **private** and requires GitHub token authentication.

## 🚀 Quick Download Commands

### Option 1: Download Script (Recommended)
```bash
curl -H "Authorization: token *********************************************************************************************" \
     -H "Accept: application/vnd.github.v3.raw" \
     -o download-deployment.sh \
     "https://api.github.com/repos/mightybadz18/hauling-qr-trip-management/contents/deploy-hauling-qr-ubuntu/download-deployment.sh?ref=main"
chmod +x download-deployment.sh
./download-deployment.sh
```

### Option 2: Clone Repository
```bash
git clone https://<EMAIL>/mightybadz18/hauling-qr-trip-management.git
cd hauling-qr-trip-management/deploy-hauling-qr-ubuntu/
```

### Option 3: Manual Files
```bash
TOKEN="*********************************************************************************************"
curl -H "Authorization: token $TOKEN" -H "Accept: application/vnd.github.v3.raw" \
     -o deploy-hauling-qr-ubuntu-fixed.sh \
     "https://api.github.com/repos/mightybadz18/hauling-qr-trip-management/contents/deploy-hauling-qr-ubuntu/deploy-hauling-qr-ubuntu-fixed.sh?ref=main"
curl -H "Authorization: token $TOKEN" -H "Accept: application/vnd.github.v3.raw" \
     -o deployment-config.conf \
     "https://api.github.com/repos/mightybadz18/hauling-qr-trip-management/contents/deploy-hauling-qr-ubuntu/deployment-config.conf?ref=main"
chmod +x deploy-hauling-qr-ubuntu-fixed.sh
```

## ⚠️ Important Notes

- **Token Required**: All downloads need the GitHub token
- **Private Repository**: Public URLs no longer work
- **Built-in Authentication**: The download script includes the token
- **Configuration**: `deployment-config.conf` has authenticated repository URL

## 📚 Full Documentation

- [DEPLOYMENT_STEPS.md](DEPLOYMENT_STEPS.md) - Complete deployment guide
- [PRIVATE_REPO_AUTHENTICATION_UPDATE.md](PRIVATE_REPO_AUTHENTICATION_UPDATE.md) - Detailed changes
- [DOWNLOAD_UPDATED_FILES.md](DOWNLOAD_UPDATED_FILES.md) - Download methods guide

---
**Quick Start**: Use Option 1 above, then run `sudo ./run-deployment.sh`