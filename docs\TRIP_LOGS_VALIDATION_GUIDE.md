# Trip Logs Data Quality Validation Guide

This guide explains how to use the trip_logs field population validation system to ensure data completeness and quality in the hauling QR trip management system.

## Overview

The validation system provides comprehensive monitoring and alerting for trip_logs data quality, ensuring that all required fields are properly populated when trips are created. This addresses the requirements from the shift-assignment-trip-status-fix specification.

## Components

### 1. TripLogsValidationService

The main service class that provides all validation functionality:

- **Driver Information Validation**: Ensures all driver-related fields are populated
- **Notes Quality Validation**: Verifies notes contain meaningful contextual information
- **Location Sequence Validation**: Validates sequence numbers for different workflow types
- **Missing Fields Detection**: Identifies trips with missing required fields
- **Data Quality Alerts**: Generates alerts when quality thresholds are not met

### 2. Validation API Endpoints

RESTful API endpoints for accessing validation reports:

```
GET /api/validation/trip-logs/driver-info?hours=24
GET /api/validation/trip-logs/notes-quality?hours=24
GET /api/validation/trip-logs/location-sequence?hours=24
GET /api/validation/trip-logs/missing-fields?hours=24
GET /api/validation/trip-logs/comprehensive?hours=24
GET /api/validation/trip-logs/health-check
POST /api/validation/trip-logs/alerts
```

### 3. Command Line Script

Standalone script for manual validation and monitoring:

```bash
node server/scripts/validate-trip-logs-data-quality.js [options]
```

### 4. SQL Queries

Direct database queries for validation (located in `server/utils/trip-logs-validation-queries.sql`).

## Usage Examples

### API Usage

#### Get Comprehensive Validation Report

```javascript
// GET /api/validation/trip-logs/comprehensive?hours=24
{
  "success": true,
  "data": {
    "validation_timestamp": "2025-01-28T10:30:00.000Z",
    "time_range_hours": 24,
    "execution_time_ms": 1250,
    "overall_summary": {
      "total_trips_analyzed": 150,
      "driver_info_success_rate": "96.67%",
      "notes_quality_success_rate": "92.00%",
      "location_sequence_success_rate": "98.00%",
      "overall_completeness_rate": "94.67%"
    },
    "detailed_results": {
      // Detailed validation results for each category
    }
  }
}
```

#### Generate Data Quality Alerts

```javascript
// POST /api/validation/trip-logs/alerts
{
  "thresholds": {
    "driver_info_success_rate": 95,
    "notes_success_rate": 90,
    "sequence_success_rate": 95,
    "completeness_rate": 98
  }
}

// Response
{
  "success": true,
  "data": {
    "alerts_generated": 2,
    "alerts": [
      {
        "type": "DRIVER_INFO_LOW_SUCCESS_RATE",
        "severity": "HIGH",
        "message": "Driver information capture success rate (93.33%) is below threshold (95%)",
        "details": {
          "current_rate": "93.33%",
          "threshold": "95%",
          "total_trips": 150,
          "missing_breakdown": {
            "missing_driver_id": 5,
            "missing_shift_type": 5
          }
        }
      }
    ]
  }
}
```

#### Quick Health Check

```javascript
// GET /api/validation/trip-logs/health-check
{
  "success": true,
  "data": {
    "status": "HEALTHY", // HEALTHY, WARNING, CRITICAL
    "timestamp": "2025-01-28T10:30:00.000Z",
    "time_range_hours": 1,
    "total_trips_analyzed": 12,
    "metrics": {
      "driver_info_success_rate": "100.00%",
      "notes_quality_success_rate": "91.67%",
      "location_sequence_success_rate": "100.00%",
      "overall_completeness_rate": "100.00%"
    },
    "issues": []
  }
}
```

### Command Line Usage

#### Basic Validation

```bash
# Validate last 24 hours with summary output
node server/scripts/validate-trip-logs-data-quality.js

# Validate last 6 hours with alerts
node server/scripts/validate-trip-logs-data-quality.js --hours 6 --alerts

# JSON output for monitoring systems
node server/scripts/validate-trip-logs-data-quality.js --output json

# Table format for structured display
node server/scripts/validate-trip-logs-data-quality.js --output table
```

#### Custom Thresholds

Create a `custom-thresholds.json` file:

```json
{
  "driver_info_success_rate": 98,
  "notes_success_rate": 95,
  "sequence_success_rate": 99,
  "completeness_rate": 99,
  "time_range_hours": 2
}
```

Then run:

```bash
node server/scripts/validate-trip-logs-data-quality.js --alerts --thresholds ./custom-thresholds.json
```

### Service Usage in Code

```javascript
const TripLogsValidationService = require('../services/TripLogsValidationService');

// Run comprehensive validation
const report = await TripLogsValidationService.runComprehensiveValidation(24);

// Generate alerts with custom thresholds
const alerts = await TripLogsValidationService.createDataQualityAlerts({
  driver_info_success_rate: 95,
  notes_success_rate: 90
});

// Individual validation checks
const driverValidation = await TripLogsValidationService.validateDriverInformationCompleteness(24);
const notesValidation = await TripLogsValidationService.validateNotesFieldQuality(24);
```

## Validation Criteria

### Driver Information Completeness

A trip_logs entry has complete driver information when all of these fields are populated:

- `performed_by_driver_id`
- `performed_by_driver_name`
- `performed_by_employee_id`
- `performed_by_shift_id`
- `performed_by_shift_type`

### Notes Quality

Notes are considered valid when:

- `notes` field is not NULL
- Notes are in valid JSON format (string or object)
- Notes content is at least 10 characters long

### Location Sequence Accuracy

Location sequences are validated based on workflow type:

- **Standard workflow**: Sequence should be 1 or 2
- **Extended workflow**: Sequence should be 1, 2, or 3
- **Cycle workflow**: Sequence should be 1 or higher
- **Dynamic workflow**: Any non-null sequence is valid

### Overall Completeness

A trip_logs entry is considered complete when all required fields are populated:

- `assignment_id`
- `trip_number`
- `status`
- All driver information fields
- `notes`
- `location_sequence`

## Alert Thresholds

Default alert thresholds:

- **Driver Information Success Rate**: 95%
- **Notes Quality Success Rate**: 90%
- **Location Sequence Success Rate**: 95%
- **Overall Completeness Rate**: 98%

Alert severities:

- **CRITICAL**: Overall completeness below threshold
- **HIGH**: Driver info or location sequence below threshold
- **MEDIUM**: Notes quality below threshold

## Monitoring Integration

### Automated Monitoring

Set up a cron job to run validation checks:

```bash
# Run every hour and alert on issues
0 * * * * cd /path/to/app && node server/scripts/validate-trip-logs-data-quality.js --hours 1 --alerts >> /var/log/trip-validation.log 2>&1

# Daily comprehensive report
0 6 * * * cd /path/to/app && node server/scripts/validate-trip-logs-data-quality.js --hours 24 --output table --alerts >> /var/log/daily-validation.log 2>&1
```

### Integration with Monitoring Systems

The validation script returns appropriate exit codes:

- **0**: No issues found
- **1**: Data quality alerts generated
- **2**: Validation error occurred
- **3**: System error (uncaught exception)

Example monitoring integration:

```bash
#!/bin/bash
# monitoring-check.sh

cd /path/to/app
node server/scripts/validate-trip-logs-data-quality.js --hours 1 --alerts --output json > /tmp/validation-result.json

EXIT_CODE=$?

if [ $EXIT_CODE -eq 1 ]; then
    # Send alert to monitoring system
    curl -X POST "https://monitoring.example.com/alerts" \
         -H "Content-Type: application/json" \
         -d @/tmp/validation-result.json
elif [ $EXIT_CODE -gt 1 ]; then
    # System error - send critical alert
    echo "CRITICAL: Trip logs validation system error (exit code: $EXIT_CODE)"
fi
```

### Dashboard Integration

Create a dashboard widget using the health check endpoint:

```javascript
// Dashboard widget example
async function updateTripLogsHealthWidget() {
  try {
    const response = await fetch('/api/validation/trip-logs/health-check');
    const data = await response.json();
    
    const widget = document.getElementById('trip-logs-health');
    widget.className = `health-widget ${data.data.status.toLowerCase()}`;
    widget.innerHTML = `
      <h3>Trip Logs Data Quality</h3>
      <div class="status">${data.data.status}</div>
      <div class="metrics">
        <div>Driver Info: ${data.data.metrics.driver_info_success_rate}</div>
        <div>Notes Quality: ${data.data.metrics.notes_quality_success_rate}</div>
        <div>Completeness: ${data.data.metrics.overall_completeness_rate}</div>
      </div>
      ${data.data.issues.length > 0 ? `
        <div class="issues">
          <h4>Issues:</h4>
          <ul>${data.data.issues.map(issue => `<li>${issue}</li>`).join('')}</ul>
        </div>
      ` : ''}
    `;
  } catch (error) {
    console.error('Error updating trip logs health widget:', error);
  }
}

// Update every 5 minutes
setInterval(updateTripLogsHealthWidget, 5 * 60 * 1000);
```

## Troubleshooting

### Common Issues

1. **High Missing Driver Information Rate**
   - Check `captureActiveDriverInfo` function in scanner.js
   - Verify driver shifts are being created correctly
   - Check shift time validation logic

2. **Poor Notes Quality**
   - Review trip creation functions for notes population
   - Ensure `generateTripNotes` helper function is being used
   - Check for null or empty notes being inserted

3. **Invalid Location Sequences**
   - Verify `calculateLocationSequence` function logic
   - Check workflow type assignment
   - Ensure sequence numbers match workflow patterns

4. **Performance Issues**
   - Add database indexes on `created_at` and validation fields
   - Consider reducing validation time range for frequent checks
   - Use the health check endpoint for real-time monitoring

### Debug Mode

Enable debug logging by setting environment variable:

```bash
NODE_ENV=development node server/scripts/validate-trip-logs-data-quality.js --hours 1
```

This will provide detailed logging information for troubleshooting validation issues.

## Testing

Run the validation test suite:

```bash
# Run validation service tests
npm test server/tests/trip-logs-validation.test.js

# Run with coverage
npm run test:coverage -- server/tests/trip-logs-validation.test.js
```

The test suite creates sample data with various quality scenarios and validates that the service correctly identifies issues and generates appropriate reports.

## Best Practices

1. **Regular Monitoring**: Set up automated validation checks to run hourly
2. **Alert Tuning**: Adjust thresholds based on your operational requirements
3. **Trend Analysis**: Use the 7-day trend queries to identify improving or degrading data quality
4. **Integration Testing**: Include validation checks in your deployment pipeline
5. **Documentation**: Keep validation criteria updated as business requirements change

## Support

For issues or questions about the validation system:

1. Check the logs in `/var/log/trip-validation.log`
2. Review the test suite for expected behavior
3. Use the debug mode for detailed troubleshooting
4. Consult the API documentation for endpoint specifications