import React, { useState, useEffect, useCallback } from 'react';
import { driversAPI } from '../../services/api';
import toast from 'react-hot-toast';

const DriverAttendance = () => {
  const [attendanceRecords, setAttendanceRecords] = useState([]);
  const [loading, setLoading] = useState(false);
  const [summary, setSummary] = useState(null);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 50
  });

  // Filter states
  const [filters, setFilters] = useState({
    driver_id: '',
    date_from: '',
    date_to: '',
    truck_id: '',
    status: 'all',
    sort_by: 'start_date',
    sort_order: 'desc'
  });

  // Summary period state
  const [summaryPeriod, setSummaryPeriod] = useState('weekly');

  // Load attendance records
  const loadAttendanceRecords = useCallback(async (page = 1) => {
    setLoading(true);
    try {
      const params = {
        ...filters,
        limit: pagination.itemsPerPage,
        offset: (page - 1) * pagination.itemsPerPage
      };

      const response = await driversAPI.getAttendance({ params });
      setAttendanceRecords(response.data.data.records);
      setPagination({
        currentPage: page,
        totalPages: response.data.data.pagination.total_pages,
        totalItems: response.data.data.pagination.total,
        itemsPerPage: pagination.itemsPerPage
      });
    } catch (error) {
      console.error('Error loading attendance records:', error);
      toast.error('Failed to load attendance records');
    } finally {
      setLoading(false);
    }
  }, [filters, pagination.itemsPerPage]);

  // Load attendance summary
  const loadAttendanceSummary = useCallback(async () => {
    try {
      const response = await driversAPI.getAttendanceSummary({
        params: {
          period: summaryPeriod,
          driver_id: filters.driver_id || undefined,
          date_from: filters.date_from || undefined,
          date_to: filters.date_to || undefined
        }
      });
      setSummary(response.data.data);
    } catch (error) {
      console.error('Error loading attendance summary:', error);
      toast.error('Failed to load attendance summary');
    }
  }, [summaryPeriod, filters.driver_id, filters.date_from, filters.date_to]);

  // Initial load
  useEffect(() => {
    loadAttendanceRecords();
    loadAttendanceSummary();
  }, [loadAttendanceRecords, loadAttendanceSummary]);

  // Handle filter change
  const handleFilterChange = (newFilters) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  // Handle page change
  const handlePageChange = (page) => {
    loadAttendanceRecords(page);
  };

  // Handle export
  const handleExport = async () => {
    try {
      setLoading(true);

      // Prepare filters for export - remove empty values
      const exportFilters = Object.fromEntries(
        Object.entries(filters).filter(([key, value]) =>
          value !== undefined && value !== null && value !== ''
        )
      );

      console.log('Exporting with filters:', exportFilters);

      const response = await driversAPI.exportAttendance({
        params: exportFilters
      });

      // Check response structure and extract data
      const exportData = response?.data?.data?.payroll_records || response?.data?.payroll_records || [];

      if (!exportData || exportData.length === 0) {
        toast.warning('No data available for export with current filters');
        return;
      }

      // Create and download CSV
      const csvContent = convertToCSV(exportData);
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;

      // Generate filename with current date and filter info
      const dateStr = new Date().toISOString().split('T')[0];
      const statusFilter = filters.status && filters.status !== 'all' ? `-${filters.status}` : '';
      link.download = `driver-attendance${statusFilter}-${dateStr}.csv`;

      // Trigger download
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success(`Exported ${exportData.length} attendance records successfully`);
    } catch (error) {
      console.error('Error exporting attendance:', error);
      const errorMessage = error?.response?.data?.message || error?.message || 'Failed to export attendance data';
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Convert data to CSV format
  const convertToCSV = (data) => {
    if (!data || data.length === 0) return '';

    // Define the exact 8 columns including End Date for overnight shift clarity
    const csvColumns = [
      { header: 'Driver', field: 'driver_name' },
      { header: 'Start Date', field: 'date' },
      { header: 'End Date', field: 'end_date' },
      { header: 'Time In', field: 'start_time' },
      { header: 'Time Out', field: 'end_time' },
      { header: 'Truck', field: 'truck_number' },
      { header: 'Duration', field: 'duration_formatted' },
      { header: 'Status', field: 'status' }
    ];

    // Create CSV header row with exact column names
    const headerRow = csvColumns.map(col => col.header).join(',');

    // Create data rows
    const csvRows = [
      headerRow,
      ...data.map(row =>
        csvColumns.map(col => {
          let value = row[col.field];

          // Handle null/undefined values
          if (value === null || value === undefined) {
            value = '';
          }

          // Convert value to string and handle special characters
          value = String(value);

          // Escape quotes and wrap in quotes if contains comma, quote, or newline
          if (value.includes(',') || value.includes('"') || value.includes('\n')) {
            value = `"${value.replace(/"/g, '""')}"`;
          }

          return value;
        }).join(',')
      )
    ];

    return csvRows.join('\n');
  };

  // Format date for display
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Duration display logic based on business rules
  const getDurationDisplay = (record) => {
    if (record.status === 'completed') {
      // For completed shifts, show calculated duration or fallback
      return record.duration_formatted || 'Duration not calculated';
    } else if (record.status === 'active' || record.status === 'scheduled') {
      // For active or scheduled shifts, show "In Progress"
      return 'In Progress';
    } else {
      // For any other status, show appropriate message
      return record.duration_formatted || 'Not available';
    }
  };

  // Format time for display
  const formatTime = (timeString) => {
    return new Date(`2000-01-01T${timeString}`).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  // Get status badge
  const getStatusBadge = (status) => {
    const statusClasses = {
      'active': 'bg-success-100 text-success-800',
      'completed': 'bg-primary-100 text-primary-800',
      'cancelled': 'bg-danger-100 text-danger-800'
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusClasses[status] || 'bg-secondary-100 text-secondary-800'}`}>
        {status?.charAt(0).toUpperCase() + status?.slice(1)}
      </span>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="md:flex md:items-center md:justify-between">
        <div className="flex-1 min-w-0">
          <h1 className="text-2xl font-bold leading-7 text-secondary-900 sm:text-3xl sm:truncate">
            📊 Driver Attendance
          </h1>
          <p className="mt-1 text-sm text-secondary-500">
            Track driver shift records, durations, and generate payroll reports.
          </p>
        </div>
        <div className="mt-4 flex space-x-3 md:mt-0 md:ml-4">
          <button
            onClick={handleExport}
            disabled={loading}
            className={`btn btn-secondary ${loading ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            {loading ? (
              <>
                <svg className="w-4 h-4 mr-2 animate-spin" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Exporting...
              </>
            ) : (
              <>
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Export CSV
              </>
            )}
          </button>
        </div>
      </div>

      {/* Summary Cards */}
      {summary && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-primary-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-medium">👥</span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-secondary-500 truncate">
                      Active Drivers
                    </dt>
                    <dd className="text-lg font-medium text-secondary-900">
                      {summary.overall_stats.active_drivers}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-success-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-medium">📋</span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-secondary-500 truncate">
                      Total Shifts
                    </dt>
                    <dd className="text-lg font-medium text-secondary-900">
                      {summary.overall_stats.total_shifts}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-warning-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-medium">⏰</span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-secondary-500 truncate">
                      Total Hours
                    </dt>
                    <dd className="text-lg font-medium text-secondary-900">
                      {summary.overall_stats.total_hours_formatted}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-indigo-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-medium">🚛</span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-secondary-500 truncate">
                      Trucks Used
                    </dt>
                    <dd className="text-lg font-medium text-secondary-900">
                      {summary.overall_stats.trucks_used}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white shadow-sm rounded-lg border border-secondary-200">
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Date From */}
            <div>
              <label className="block text-sm font-medium text-secondary-700 mb-1">
                From Date
              </label>
              <input
                type="date"
                value={filters.date_from}
                onChange={(e) => handleFilterChange({ date_from: e.target.value })}
                className="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:ring-primary-500 focus:border-primary-500"
              />
            </div>

            {/* Date To */}
            <div>
              <label className="block text-sm font-medium text-secondary-700 mb-1">
                To Date
              </label>
              <input
                type="date"
                value={filters.date_to}
                onChange={(e) => handleFilterChange({ date_to: e.target.value })}
                className="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:ring-primary-500 focus:border-primary-500"
              />
            </div>

            {/* Status Filter */}
            <div>
              <label className="block text-sm font-medium text-secondary-700 mb-1">
                Status
              </label>
              <select
                value={filters.status}
                onChange={(e) => handleFilterChange({ status: e.target.value })}
                className="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="scheduled">Scheduled</option>
                <option value="completed">Completed</option>
              </select>
            </div>

            {/* Summary Period */}
            <div>
              <label className="block text-sm font-medium text-secondary-700 mb-1">
                Summary Period
              </label>
              <select
                value={summaryPeriod}
                onChange={(e) => setSummaryPeriod(e.target.value)}
                className="block w-full px-3 py-2 border border-secondary-300 rounded-lg focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="daily">Daily</option>
                <option value="weekly">Weekly</option>
                <option value="monthly">Monthly</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Attendance Records Table */}
      <div className="bg-white shadow-lg overflow-hidden sm:rounded-lg border border-secondary-200">
        <div className="px-4 sm:px-6 py-4 border-b border-secondary-200">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-medium text-secondary-900">
                Attendance Records ({pagination.totalItems})
              </h3>
              <p className="text-sm text-secondary-500">
                Driver shift records with calculated durations
              </p>
            </div>
            <div className="text-sm text-secondary-500">
              Page {pagination.currentPage} of {pagination.totalPages}
            </div>
          </div>
        </div>

        {loading ? (
          <div className="px-4 py-5 sm:p-6">
            <div className="animate-pulse space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="flex space-x-4">
                  <div className="h-4 bg-secondary-200 rounded w-1/6"></div>
                  <div className="h-4 bg-secondary-200 rounded w-1/6"></div>
                  <div className="h-4 bg-secondary-200 rounded w-1/6"></div>
                  <div className="h-4 bg-secondary-200 rounded w-1/6"></div>
                  <div className="h-4 bg-secondary-200 rounded w-1/6"></div>
                  <div className="h-4 bg-secondary-200 rounded w-1/6"></div>
                </div>
              ))}
            </div>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-secondary-200">
              <thead className="bg-secondary-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                    Driver
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                    Start Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                    End Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                    Time In
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                    Time Out
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                    Truck
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                    Duration
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                    Status
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-secondary-200">
                {attendanceRecords.length === 0 ? (
                  <tr>
                    <td colSpan="8" className="px-6 py-12 text-center">
                      <div className="flex flex-col items-center">
                        <svg className="w-12 h-12 text-secondary-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                        </svg>
                        <h3 className="text-lg font-medium text-secondary-900 mb-2">No attendance records found</h3>
                        <p className="text-secondary-500">
                          Try adjusting your filters or date range
                        </p>
                      </div>
                    </td>
                  </tr>
                ) : (
                  attendanceRecords.map((record) => (
                    <tr key={record.shift_id} className="hover:bg-secondary-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <span className="text-2xl mr-3">👤</span>
                          <div>
                            <div className="text-sm font-medium text-secondary-900">
                              {record.driver_name}
                            </div>
                            <div className="text-sm text-secondary-500">
                              {record.employee_id}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-secondary-900">
                          {formatDate(record.start_date)}
                        </div>
                        <div className="text-xs text-secondary-500">
                          Start Date
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-secondary-900">
                          {formatDate(record.end_date)}
                        </div>
                        <div className="text-xs text-secondary-500">
                          End Date
                        </div>
                        {record.is_overnight && (
                          <div className="text-xs text-warning-600 mt-1">
                            Overnight shift
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-secondary-900">
                          {formatTime(record.start_time)}
                        </div>
                        <div className="text-xs text-secondary-500">
                          Check In
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-secondary-900">
                          {record.end_time ? formatTime(record.end_time) : (
                            <span className="text-warning-600">In Progress</span>
                          )}
                        </div>
                        {record.end_time && (
                          <div className="text-xs text-secondary-500">
                            Check Out
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-secondary-900">
                          {record.truck_number}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-secondary-900">
                          {getDurationDisplay(record)}
                        </div>
                        {record.total_hours && (
                          <div className="text-xs text-secondary-500">
                            {record.total_hours}h
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(record.status)}
                        {record.auto_created && (
                          <div className="text-xs text-primary-600 mt-1">
                            Auto-created
                          </div>
                        )}
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        )}

        {/* Pagination */}
        {pagination.totalPages > 1 && (
          <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-secondary-200 sm:px-6">
            <div className="flex-1 flex justify-between sm:hidden">
              <button
                onClick={() => handlePageChange(pagination.currentPage - 1)}
                disabled={pagination.currentPage === 1}
                className="relative inline-flex items-center px-4 py-2 border border-secondary-300 text-sm font-medium rounded-md text-secondary-700 bg-white hover:bg-secondary-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              <button
                onClick={() => handlePageChange(pagination.currentPage + 1)}
                disabled={pagination.currentPage === pagination.totalPages}
                className="ml-3 relative inline-flex items-center px-4 py-2 border border-secondary-300 text-sm font-medium rounded-md text-secondary-700 bg-white hover:bg-secondary-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-secondary-700">
                  Showing{' '}
                  <span className="font-medium">
                    {(pagination.currentPage - 1) * pagination.itemsPerPage + 1}
                  </span>{' '}
                  to{' '}
                  <span className="font-medium">
                    {Math.min(pagination.currentPage * pagination.itemsPerPage, pagination.totalItems)}
                  </span>{' '}
                  of{' '}
                  <span className="font-medium">{pagination.totalItems}</span>{' '}
                  results
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                  <button
                    onClick={() => handlePageChange(pagination.currentPage - 1)}
                    disabled={pagination.currentPage === 1}
                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-secondary-300 bg-white text-sm font-medium text-secondary-500 hover:bg-secondary-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Previous
                  </button>
                  <button
                    onClick={() => handlePageChange(pagination.currentPage + 1)}
                    disabled={pagination.currentPage === pagination.totalPages}
                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-secondary-300 bg-white text-sm font-medium text-secondary-500 hover:bg-secondary-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Next
                  </button>
                </nav>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DriverAttendance;