// Background Sync Service
// Comprehensive offline data synchronization with conflict resolution

import { tripScannerOffline } from './tripScannerOffline.js';
import { driverConnectOffline } from './driverConnectOffline.js';
import { conflictResolution, referenceData, SYNC_STATUS } from './offlineDB.js';

// Background sync coordination service
export class BackgroundSyncService {
  constructor() {
    this.isOnline = navigator.onLine;
    this.syncInProgress = false;
    this.syncQueue = [];
    this.retryDelays = [1000, 5000, 15000, 30000]; // Progressive retry delays
    this.maxConcurrentSyncs = 3;
    
    // Bind network event handlers
    this.handleOnline = this.handleOnline.bind(this);
    this.handleOffline = this.handleOffline.bind(this);
    
    // Initialize network monitoring
    this.initializeNetworkMonitoring();
  }

  // Initialize network event monitoring
  initializeNetworkMonitoring() {
    window.addEventListener('online', this.handleOnline);
    window.addEventListener('offline', this.handleOffline);
    
    console.log('[BackgroundSync] Network monitoring initialized');
  }

  // Handle network connection restored
  async handleOnline() {
    this.isOnline = true;
    console.log('[BackgroundSync] Network connection restored, starting sync...');
    
    // Trigger immediate sync
    await this.startSync();
    
    // Register service worker background sync if available
    if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
      try {
        const registration = await navigator.serviceWorker.ready;
        await registration.sync.register('comprehensive-sync');
        console.log('[BackgroundSync] Service worker sync registered');
      } catch (error) {
        console.error('[BackgroundSync] Failed to register service worker sync:', error);
      }
    }
  }

  // Handle network connection lost
  handleOffline() {
    this.isOnline = false;
    console.log('[BackgroundSync] Network connection lost, entering offline mode');
  }

  // Start comprehensive sync process
  async startSync() {
    if (this.syncInProgress || !this.isOnline) {
      console.log('[BackgroundSync] Sync already in progress or offline');
      return;
    }

    this.syncInProgress = true;
    console.log('[BackgroundSync] Starting comprehensive sync...');

    try {
      // Sync in priority order
      const syncResults = {
        tripScans: await this.syncTripScans(),
        driverConnections: await this.syncDriverConnections(),
        conflicts: await this.resolveConflicts(),
        referenceData: await this.updateReferenceData()
      };

      console.log('[BackgroundSync] Sync completed:', syncResults);
      
      // Notify components of sync completion
      this.notifySyncComplete(syncResults);
      
      return syncResults;
      
    } catch (error) {
      console.error('[BackgroundSync] Sync failed:', error);
      throw error;
    } finally {
      this.syncInProgress = false;
    }
  }

  // Sync trip scanner data
  async syncTripScans() {
    console.log('[BackgroundSync] Syncing trip scans...');
    
    try {
      const pendingScans = await tripScannerOffline.getPendingScans();
      console.log(`[BackgroundSync] Found ${pendingScans.length} pending trip scans`);
      
      const results = {
        total: pendingScans.length,
        synced: 0,
        failed: 0,
        conflicts: 0
      };

      // Process scans in batches
      const batchSize = this.maxConcurrentSyncs;
      for (let i = 0; i < pendingScans.length; i += batchSize) {
        const batch = pendingScans.slice(i, i + batchSize);
        
        const batchPromises = batch.map(scan => this.syncSingleTripScan(scan));
        const batchResults = await Promise.allSettled(batchPromises);
        
        // Process batch results
        batchResults.forEach((result, index) => {
          if (result.status === 'fulfilled') {
            if (result.value.success) {
              results.synced++;
            } else if (result.value.conflict) {
              results.conflicts++;
            } else {
              results.failed++;
            }
          } else {
            results.failed++;
            console.error('[BackgroundSync] Trip scan sync failed:', result.reason);
          }
        });
      }

      console.log('[BackgroundSync] Trip scan sync results:', results);
      return results;
      
    } catch (error) {
      console.error('[BackgroundSync] Trip scan sync error:', error);
      return { total: 0, synced: 0, failed: 0, conflicts: 0, error: error.message };
    }
  }

  // Sync single trip scan with retry logic
  async syncSingleTripScan(scan, retryCount = 0) {
    try {
      // Update scan status to syncing
      await tripScannerOffline.updateScanStatus(scan.id, SYNC_STATUS.SYNCING);
      
      // Attempt to sync with server using exact API format (public endpoint)
      const apiPayload = scan.apiPayload || scan.scanData;
      const response = await fetch('/api/scanner/public-scan', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(apiPayload)
      });

      if (response.ok) {
        const result = await response.json();
        
        // Validate response data integrity
        if (this.validateSyncResponse(result, scan)) {
          await tripScannerOffline.removeSyncedScan(scan.id);
          console.log(`[BackgroundSync] Trip scan ${scan.id} synced successfully`);
          return { success: true, scanId: scan.id };
        } else {
          // Data integrity issue - log conflict
          const conflict = await conflictResolution.detectConflict(
            scan.scanData, 
            result, 
            'validation'
          );
          return { success: false, conflict: true, conflictId: conflict.id };
        }
      } else {
        throw new Error(`Server error: ${response.status}`);
      }
      
    } catch (error) {
      console.error(`[BackgroundSync] Trip scan ${scan.id} sync failed:`, error);
      
      // Implement retry logic
      if (retryCount < this.retryDelays.length) {
        const delay = this.retryDelays[retryCount];
        console.log(`[BackgroundSync] Retrying trip scan ${scan.id} in ${delay}ms...`);
        
        setTimeout(() => {
          this.syncSingleTripScan(scan, retryCount + 1);
        }, delay);
        
        return { success: false, retrying: true };
      } else {
        // Max retries reached
        await tripScannerOffline.updateScanStatus(scan.id, SYNC_STATUS.FAILED, {
          lastError: error.message,
          failedAt: new Date().toISOString()
        });
        
        return { success: false, maxRetriesReached: true };
      }
    }
  }

  // Sync driver connections
  async syncDriverConnections() {
    console.log('[BackgroundSync] Syncing driver connections...');
    
    try {
      const pendingConnections = await driverConnectOffline.getPendingConnections();
      console.log(`[BackgroundSync] Found ${pendingConnections.length} pending driver connections`);
      
      const results = {
        total: pendingConnections.length,
        synced: 0,
        failed: 0,
        conflicts: 0
      };

      // Process connections sequentially to avoid conflicts
      for (const connection of pendingConnections) {
        try {
          const result = await this.syncSingleDriverConnection(connection);
          
          if (result.success) {
            results.synced++;
          } else if (result.conflict) {
            results.conflicts++;
          } else {
            results.failed++;
          }
        } catch (error) {
          results.failed++;
          console.error('[BackgroundSync] Driver connection sync failed:', error);
        }
      }

      console.log('[BackgroundSync] Driver connection sync results:', results);
      return results;
      
    } catch (error) {
      console.error('[BackgroundSync] Driver connection sync error:', error);
      return { total: 0, synced: 0, failed: 0, conflicts: 0, error: error.message };
    }
  }

  // Sync single driver connection
  async syncSingleDriverConnection(connection) {
    try {
      // Update connection status to syncing
      await driverConnectOffline.updateConnectionStatus(connection.id, SYNC_STATUS.SYNCING);
      
      // Attempt to sync with server using exact API format
      const apiPayload = connection.apiPayload || connection.connectionData;
      const response = await fetch('/api/driver/connect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(apiPayload)
      });

      if (response.ok) {
        const result = await response.json();
        
        // Check for conflicts (e.g., driver already checked in)
        if (result.conflict) {
          const conflict = await conflictResolution.detectConflict(
            connection.connectionData,
            result,
            'duplicate'
          );
          return { success: false, conflict: true, conflictId: conflict.id };
        }
        
        await driverConnectOffline.removeSyncedConnection(connection.id);
        console.log(`[BackgroundSync] Driver connection ${connection.id} synced successfully`);
        return { success: true, connectionId: connection.id };
        
      } else {
        throw new Error(`Server error: ${response.status}`);
      }
      
    } catch (error) {
      console.error(`[BackgroundSync] Driver connection ${connection.id} sync failed:`, error);
      
      await driverConnectOffline.updateConnectionStatus(connection.id, SYNC_STATUS.FAILED, {
        lastError: error.message,
        failedAt: new Date().toISOString()
      });
      
      return { success: false, error: error.message };
    }
  }

  // Resolve pending conflicts
  async resolveConflicts() {
    console.log('[BackgroundSync] Resolving conflicts...');
    
    try {
      const pendingConflicts = await conflictResolution.getPendingConflicts();
      console.log(`[BackgroundSync] Found ${pendingConflicts.length} pending conflicts`);
      
      const results = {
        total: pendingConflicts.length,
        autoResolved: 0,
        manualRequired: 0
      };

      for (const conflict of pendingConflicts) {
        const resolution = await conflictResolution.autoResolveConflict(conflict.id);
        
        if (resolution) {
          results.autoResolved++;
          console.log(`[BackgroundSync] Auto-resolved conflict ${conflict.id}`);
        } else {
          results.manualRequired++;
          console.log(`[BackgroundSync] Conflict ${conflict.id} requires manual resolution`);
        }
      }

      console.log('[BackgroundSync] Conflict resolution results:', results);
      return results;
      
    } catch (error) {
      console.error('[BackgroundSync] Conflict resolution error:', error);
      return { total: 0, autoResolved: 0, manualRequired: 0, error: error.message };
    }
  }

  // Update reference data cache
  async updateReferenceData() {
    console.log('[BackgroundSync] Updating reference data...');
    
    try {
      const updates = {
        locations: await this.fetchAndCacheReferenceData('locations', '/api/locations'),
        trucks: await this.fetchAndCacheReferenceData('trucks', '/api/trucks'),
        drivers: await this.fetchAndCacheReferenceData('drivers', '/api/drivers')
      };

      console.log('[BackgroundSync] Reference data updated:', updates);
      return updates;
      
    } catch (error) {
      console.error('[BackgroundSync] Reference data update error:', error);
      return { error: error.message };
    }
  }

  // Fetch and cache reference data
  async fetchAndCacheReferenceData(type, endpoint) {
    try {
      const response = await fetch(endpoint);
      
      if (response.ok) {
        const data = await response.json();
        await referenceData.cacheReferenceData(type, data);
        return { success: true, count: data.length || 0 };
      } else {
        throw new Error(`Failed to fetch ${type}: ${response.status}`);
      }
    } catch (error) {
      console.error(`[BackgroundSync] Failed to update ${type}:`, error);
      return { success: false, error: error.message };
    }
  }

  // Validate sync response data integrity
  validateSyncResponse(response, originalData) {
    // Basic validation - can be enhanced based on business rules
    return response && response.success !== false;
  }

  // Notify components of sync completion
  notifySyncComplete(results) {
    // Dispatch custom event for components to listen to
    const event = new CustomEvent('backgroundSyncComplete', {
      detail: results
    });
    window.dispatchEvent(event);
  }

  // Get sync statistics
  async getSyncStats() {
    const [tripStats, connectionStats] = await Promise.all([
      tripScannerOffline.getStats(),
      driverConnectOffline.getStats()
    ]);

    return {
      tripScans: tripStats,
      driverConnections: connectionStats,
      isOnline: this.isOnline,
      syncInProgress: this.syncInProgress,
      lastSync: localStorage.getItem('lastBackgroundSync') || null
    };
  }

  // Cleanup - remove event listeners
  destroy() {
    window.removeEventListener('online', this.handleOnline);
    window.removeEventListener('offline', this.handleOffline);
  }
}

// Create singleton instance
export const backgroundSync = new BackgroundSyncService();

// Export service instance
export default backgroundSync;
