# Driver QR System Tests

This directory contains test files for the Driver QR Code system functionality.

## Test Files

### `test-date-parsing.js`
Tests the original date parsing scenarios that were causing "Invalid time value" errors. This demonstrates the problems that existed before the fix.

**Usage:**
```bash
node tests/driver-qr-system/test-date-parsing.js
```

### `test-improved-date-parsing.js`
Tests the improved date parsing logic with graceful error handling. This shows how the system now handles various edge cases without crashing.

**Usage:**
```bash
node tests/driver-qr-system/test-improved-date-parsing.js
```

### `test-driver-connect-endpoint.js`
Integration test for the `/api/driver/connect` endpoint. Tests the actual API endpoint with sample data.

**Usage:**
```bash
# Make sure the server is running first
npm run dev

# Then run the test
node tests/driver-qr-system/test-driver-connect-endpoint.js
```

## Test Scenarios Covered

1. **Valid date/time combinations** - Normal operation
2. **Date objects** - Handles JavaScript Date objects properly
3. **NULL values** - Graceful fallback for missing data
4. **Invalid date strings** - Error handling for malformed data
5. **Timezone-aware dates** - <PERSON>les dates with timezone information
6. **API integration** - End-to-end testing of the driver connect endpoint

## Running All Tests

To run all tests in sequence:

```bash
# Run date parsing tests
node tests/driver-qr-system/test-date-parsing.js
node tests/driver-qr-system/test-improved-date-parsing.js

# Run API integration test (requires server to be running)
node tests/driver-qr-system/test-driver-connect-endpoint.js
```

## Expected Results

- **test-date-parsing.js**: Shows errors for problematic scenarios (demonstrates the original issue)
- **test-improved-date-parsing.js**: Shows graceful handling of all scenarios with no crashes
- **test-driver-connect-endpoint.js**: Should successfully connect to the API and return valid responses

## Related Documentation

See `docs/driver-qr-system/date-parsing-fix.md` for detailed information about the fix implementation.