// Driver Connect Offline Service
// Specialized offline operations for driver check-in/check-out functionality

import { offlineDB, SYNC_STATUS, PRIORITY } from './offlineDB.js';

// Driver connect specific data structures and validation
export class DriverConnectOfflineService {
  constructor() {
    this.storeName = 'connectionQueue';
  }

  // Store driver connection data offline in exact API format for perfect sync compatibility
  async storeConnection(connectionData) {
    try {
      // Ensure database is initialized
      await offlineDB.initialize();
      if (!offlineDB.db) {
        throw new Error('Database not initialized');
      }

      // Validate connection data structure
      const validatedData = this.validateConnectionData(connectionData);

      // Determine priority and action type
      const priority = this.calculatePriority(validatedData);
      const action = this.determineAction(validatedData);

      // Create API-compatible payload for direct sync
      const apiPayload = this.createAPIPayload(validatedData, action);

      // Create offline connection record with exact API format
      const offlineConnection = {
        // EXACT API PAYLOAD - Direct pass-through to /api/driver/connect
        apiPayload: apiPayload,

        // Sync metadata (separate from payload)
        syncMetadata: {
          status: SYNC_STATUS.PENDING,
          priority: priority,
          retryCount: 0,
          maxRetries: 3,
          timestamp: new Date().toISOString(),
          scheduledSync: new Date().toISOString(),
          validationHash: this.generateValidationHash(apiPayload),
          dataIntegrity: true
        },

        // Quick access fields for IndexedDB queries (derived from payload)
        action: action,
        employeeId: apiPayload.driver_qr_data?.employee_id || 'unknown',
        truckId: apiPayload.truck_qr_data?.id || null,

        // Device context for audit
        deviceInfo: this.getDeviceInfo()
      };

      // Store in IndexedDB
      const id = await offlineDB.addData(this.storeName, offlineConnection);

      console.log('[DriverConnectOffline] Connection stored offline (API-compatible format):', {
        id,
        action: offlineConnection.action,
        employeeId: offlineConnection.employeeId,
        priority: offlineConnection.syncMetadata.priority,
        apiPayloadReady: true
      });

      return {
        success: true,
        id,
        action: action,
        message: '📱 Connection saved offline - will sync when connected',
        truck: offlineConnection.truckId,
        check_in_time: action === 'check_in' ? offlineConnection.syncMetadata.timestamp : null,
        check_out_time: action === 'check_out' ? offlineConnection.syncMetadata.timestamp : null,
        offlineMode: true,
        priority: offlineConnection.syncMetadata.priority
      };

    } catch (error) {
      console.error('[DriverConnectOffline] Failed to store connection:', error);
      throw new Error(`Failed to store connection offline: ${error.message}`);
    }
  }

  // Create exact API payload format for /api/driver/connect endpoint
  createAPIPayload(connectionData, action) {
    return {
      driver_qr_data: connectionData.driver_qr_data,
      truck_qr_data: connectionData.truck_qr_data,
      action: action || null // null allows server to auto-determine
    };
  }

  // Validate connection data structure and completeness
  validateConnectionData(connectionData) {
    if (!connectionData) {
      throw new Error('Connection data is required');
    }

    // Required fields validation
    if (!connectionData.driver_qr_data) {
      throw new Error('Driver QR data is required');
    }
    
    if (!connectionData.truck_qr_data) {
      throw new Error('Truck QR data is required');
    }

    // Validate driver QR data
    this.validateDriverQRData(connectionData.driver_qr_data);
    
    // Validate truck QR data
    this.validateTruckQRData(connectionData.truck_qr_data);

    return connectionData;
  }

  // Validate driver QR code data
  validateDriverQRData(driverData) {
    if (!driverData.type || driverData.type !== 'driver') {
      throw new Error('Invalid driver QR data: incorrect type');
    }
    
    if (!driverData.employee_id) {
      throw new Error('Invalid driver QR data: missing employee_id');
    }
    
    if (!driverData.full_name) {
      throw new Error('Invalid driver QR data: missing full_name');
    }
  }

  // Validate truck QR code data
  validateTruckQRData(truckData) {
    if (!truckData.type || truckData.type !== 'truck') {
      throw new Error('Invalid truck QR data: incorrect type');
    }
    
    if (!truckData.id) {
      throw new Error('Invalid truck QR data: missing id');
    }
    
    if (!truckData.truck_number) {
      throw new Error('Invalid truck QR data: missing truck_number');
    }
  }

  // Calculate priority based on connection context
  calculatePriority(connectionData) {
    const currentHour = new Date().getHours();
    
    // Critical: Shift change times (6-8 AM, 6-8 PM)
    if ((currentHour >= 6 && currentHour <= 8) || (currentHour >= 18 && currentHour <= 20)) {
      return PRIORITY.CRITICAL;
    }
    
    // High: Emergency or supervisor connections
    if (connectionData.driver_qr_data?.role === 'supervisor') {
      return PRIORITY.HIGH;
    }
    
    // Normal: Regular driver connections
    return PRIORITY.NORMAL;
  }

  // Determine action type (check_in or check_out)
  determineAction(connectionData) {
    // This would typically check current shift status
    // For offline mode, we'll use timestamp patterns or explicit action
    if (connectionData.action) {
      return connectionData.action;
    }
    
    // Default logic: assume check_in during day hours, check_out during evening
    const currentHour = new Date().getHours();
    return (currentHour >= 6 && currentHour <= 14) ? 'check_in' : 'check_out';
  }

  // Determine shift type based on time
  determineShiftType() {
    const currentHour = new Date().getHours();
    
    if (currentHour >= 6 && currentHour < 18) {
      return 'day';
    } else {
      return 'night';
    }
  }

  // Calculate expected duration for the action
  calculateExpectedDuration(action) {
    // Standard shift durations in hours
    return action === 'check_in' ? 12 : 0; // 12-hour shifts
  }

  // Get device information for context
  getDeviceInfo() {
    return {
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      language: navigator.language,
      online: navigator.onLine,
      timestamp: new Date().toISOString(),
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
    };
  }

  // Generate validation hash for data integrity
  generateValidationHash(data) {
    const str = JSON.stringify(data);
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return hash.toString(16);
  }

  // Get business rules for the action
  getBusinessRules(action) {
    return {
      allowDuplicateConnections: false,
      requireShiftValidation: true,
      maxShiftDuration: 14, // hours
      minBreakBetweenShifts: 8, // hours
      allowOvertime: true,
      requireSupervisorApproval: false
    };
  }

  // Get all pending connections for sync
  async getPendingConnections() {
    try {
      const pendingConnections = await offlineDB.getAllPending(this.storeName);
      
      // Sort by priority and timestamp
      return pendingConnections.sort((a, b) => {
        if (a.priority !== b.priority) {
          return b.priority - a.priority; // Higher priority first
        }
        return new Date(a.timestamp) - new Date(b.timestamp); // Older first
      });
      
    } catch (error) {
      console.error('[DriverConnectOffline] Failed to get pending connections:', error);
      return [];
    }
  }

  // Update connection status
  async updateConnectionStatus(id, status, metadata = {}) {
    try {
      // Get current connection data
      const transaction = offlineDB.db.transaction([this.storeName], 'readonly');
      const store = transaction.objectStore(this.storeName);
      const connection = await new Promise((resolve, reject) => {
        const request = store.get(id);
        request.onsuccess = () => resolve(request.result);
        request.onerror = () => reject(request.error);
      });

      if (!connection) {
        throw new Error(`Connection with id ${id} not found`);
      }

      // Update connection with new status and metadata
      const updatedConnection = {
        ...connection,
        status,
        ...metadata,
        lastStatusUpdate: new Date().toISOString()
      };

      await offlineDB.updateData(this.storeName, updatedConnection);
      
      console.log(`[DriverConnectOffline] Updated connection ${id} status to ${status}`);
      return updatedConnection;
      
    } catch (error) {
      console.error('[DriverConnectOffline] Failed to update connection status:', error);
      throw error;
    }
  }

  // Remove successfully synced connection
  async removeSyncedConnection(id) {
    try {
      await offlineDB.deleteData(this.storeName, id);
      console.log(`[DriverConnectOffline] Removed synced connection: ${id}`);
    } catch (error) {
      console.error('[DriverConnectOffline] Failed to remove synced connection:', error);
      throw error;
    }
  }

  // Get pending connection count (for PWA status hook)
  async getPendingCount() {
    try {
      const pendingConnections = await offlineDB.getDataByIndex(this.storeName, 'status', SYNC_STATUS.PENDING);
      return pendingConnections.length;
    } catch (error) {
      console.error('[DriverConnectOffline] Failed to get pending count:', error);
      return 0;
    }
  }

  // Get connection statistics
  async getStats() {
    try {
      const allConnections = await offlineDB.getDataByIndex(this.storeName, 'status', SYNC_STATUS.PENDING);
      const failedConnections = await offlineDB.getDataByIndex(this.storeName, 'status', SYNC_STATUS.FAILED);
      
      const stats = {
        total: allConnections.length + failedConnections.length,
        pending: allConnections.length,
        failed: failedConnections.length,
        byAction: {
          check_in: allConnections.filter(c => c.action === 'check_in').length,
          check_out: allConnections.filter(c => c.action === 'check_out').length
        },
        byPriority: {
          critical: allConnections.filter(c => c.priority === PRIORITY.CRITICAL).length,
          high: allConnections.filter(c => c.priority === PRIORITY.HIGH).length,
          normal: allConnections.filter(c => c.priority === PRIORITY.NORMAL).length
        },
        byShiftType: {}
      };
      
      // Count by shift type
      allConnections.forEach(connection => {
        const shiftType = connection.shiftType || 'unknown';
        stats.byShiftType[shiftType] = (stats.byShiftType[shiftType] || 0) + 1;
      });
      
      return stats;
      
    } catch (error) {
      console.error('[DriverConnectOffline] Failed to get stats:', error);
      return { total: 0, pending: 0, failed: 0, byAction: {}, byPriority: {}, byShiftType: {} };
    }
  }

  // Clear all connection data (for testing/reset)
  async clearAllConnections() {
    try {
      await offlineDB.initialize();
      if (!offlineDB.db) {
        throw new Error('Database not initialized');
      }
      const transaction = offlineDB.db.transaction([this.storeName], 'readwrite');
      const store = transaction.objectStore(this.storeName);

      await new Promise((resolve, reject) => {
        const request = store.clear();
        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
      });

      console.log('[DriverConnectOffline] All connection data cleared');
    } catch (error) {
      console.error('[DriverConnectOffline] Failed to clear connection data:', error);
      throw error;
    }
  }

  // Alias for test compatibility
  async clearAllData() {
    return this.clearAllConnections();
  }
}

// Create singleton instance
export const driverConnectOffline = new DriverConnectOfflineService();

// Export service instance
export default driverConnectOffline;
