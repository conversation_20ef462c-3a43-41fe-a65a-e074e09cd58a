# User Permissions Guide

## Root vs Regular User for Deployment

When deploying the Hauling QR Trip Management System, you have two options for user permissions:

### Option 1: Regular User with Sudo (Recommended)

This is the recommended approach for production environments:

## Step-by-Step Deployment with Regular User

### 1. Create and Configure User (as root)

```bash
# Create a new user
adduser deployer

# Add user to sudo group
usermod -aG sudo deployer

# Switch to the new user
su - deployer
# OR log out and log back in as deployer
```

### 2. Download and Deploy (as regular user)

```bash
# Update and install curl
sudo apt update
sudo apt install -y curl

# Create directory for deployment
mkdir -p ~/hauling-deployment
cd ~/hauling-deployment

# Download the script (no sudo needed)
curl -H "Authorization: token *********************************************************************************************" \
     -H "Accept: application/vnd.github.v3.raw" \
     -o download-deployment.sh \
     https://api.github.com/repos/mightybadz18/hauling-qr-trip-management/contents/deploy-hauling-qr-ubuntu/download-deployment.sh

# Make executable and run (no sudo needed)
chmod +x download-deployment.sh
./download-deployment.sh

# Run deployment (with sudo)
sudo ./run-deployment.sh
```

**Benefits:**
- More secure (principle of least privilege)
- Better audit trail of commands
- Reduces risk of accidental system damage
- Follows security best practices

**When to use:**
- Production environments
- Shared servers
- Any environment where security is a priority

### Option 2: Root User

Using root directly is simpler but less secure:

```bash
# Login as root
# Then follow the deployment steps without needing sudo prefix
apt update
apt install -y curl
# etc.
```

**Benefits:**
- Simpler (no need to prefix commands with sudo)
- No permission issues during deployment
- Faster for quick deployments

**When to use:**
- Development/testing environments
- Fresh VPS that will be dedicated to this application
- Temporary deployments
- Containers where root is the default user

## How the Deployment Script Handles Permissions

The deployment script (`deploy-hauling-qr-ubuntu-fixed.sh`):

1. **Requires root privileges** for system-level operations:
   - Installing packages
   - Creating system users
   - Configuring services
   - Setting up SSL certificates

2. **Creates proper file permissions** for security:
   - Configuration files: `chmod 600` (owner read/write only)
   - Application files: `chown www-data:www-data` (web server user)
   - Log directories: `chmod 755` (owner all, others read/execute)

3. **Uses system users** for services:
   - PostgreSQL: `postgres` user
   - Web server: `www-data` user
   - PM2 processes: Configured to run as appropriate user

## Best Practices

1. **Use a regular user with sudo** for production environments
2. **Only use root directly** in controlled environments
3. **Keep sudo usage to a minimum** - only for commands that need it
4. **Review file permissions** after deployment
5. **Rotate passwords** for all system users regularly

## Security Considerations

If you deploy as root:
1. **Change default passwords** immediately after deployment
2. **Disable root SSH access** after deployment
3. **Set up key-based authentication** instead of password
4. **Configure a firewall** to limit access
5. **Consider setting up fail2ban** to prevent brute force attacks