# Database Schema Analysis for PWA Offline Synchronization

## Overview
This document provides a comprehensive analysis of the Hauling QR Trip System database schema to ensure perfect synchronization between offline IndexedDB storage and online PostgreSQL database.

## Database Schema Analysis

### Core Tables for QR Scanning Operations

#### 1. trip_logs Table (37 fields)
**Primary table for trip data storage**

**Core Fields:**
- `id` (integer, auto-increment) - Primary key
- `assignment_id` (integer, NOT NULL) - Links to assignments table
- `trip_number` (integer, NOT NULL) - Sequential trip number
- `status` (trip_status enum, default 'assigned') - Current trip status

**Timestamp Fields:**
- `loading_start_time` (timestamp) - When loading phase started
- `loading_end_time` (timestamp) - When loading phase completed
- `unloading_start_time` (timestamp) - When unloading phase started
- `unloading_end_time` (timestamp) - When unloading phase completed
- `trip_completed_time` (timestamp) - When trip was completed

**Location Context:**
- `actual_loading_location_id` (integer) - Actual loading location used
- `actual_unloading_location_id` (integer) - Actual unloading location used

**Driver Context (Critical for Sync):**
- `performed_by_driver_id` (integer) - Driver who performed the trip
- `performed_by_driver_name` (varchar) - Driver name (immutable for history)
- `performed_by_employee_id` (varchar) - Employee ID (immutable for history)
- `performed_by_shift_id` (integer) - Shift during which trip was performed
- `performed_by_shift_type` (shift_type enum) - Type of shift (day/night)

**JSONB Fields:**
- `notes` (jsonb) - Additional trip notes
- `location_sequence` (jsonb) - Complete route sequence with confirmation status

**Duration Tracking:**
- `total_duration_minutes` (integer) - Total trip duration
- `loading_duration_minutes` (integer) - Loading phase duration
- `travel_duration_minutes` (integer) - Travel time duration
- `unloading_duration_minutes` (integer) - Unloading phase duration

#### 2. driver_shifts Table (22 fields)
**Primary table for driver shift management**

**Core Fields:**
- `id` (integer, auto-increment) - Primary key
- `truck_id` (integer, NOT NULL) - Assigned truck
- `driver_id` (integer, NOT NULL) - Assigned driver
- `shift_type` (shift_type enum, default 'day') - Day or night shift
- `status` (shift_status enum, default 'scheduled') - Current shift status

**Time Management:**
- `start_time` (time, NOT NULL) - Shift start time
- `end_time` (time) - Shift end time
- `start_date` (date, NOT NULL) - Shift start date
- `end_date` (date) - Shift end date

**JSONB Fields:**
- `security_context` (jsonb) - Security audit information
- `audit_trail` (jsonb, default '[]') - Complete audit trail

#### 3. Supporting Tables

**assignments Table:**
- Links trips to trucks, drivers, and locations
- Contains `auto_created` boolean for AutoAssignmentCreator integration

**locations Table:**
- Contains `qr_code_data` (jsonb) for QR code validation
- `type` field (location_type enum: loading, unloading, checkpoint)

**dump_trucks Table:**
- Contains `qr_code_data` (jsonb) for QR code validation
- `status` field for truck availability

**drivers Table:**
- Contains driver information for validation
- `employee_id` field for QR code matching

**scan_logs Table:**
- Audit trail for all QR scans
- Links to trip_logs for complete traceability

## API Endpoint Analysis

### Trip Scanner API: POST /api/scanner/scan

**Required Request Payload:**
```json
{
  "scan_type": "location|truck",
  "qr_data": {
    "id": "location_code|truck_number",
    "type": "location|truck",
    "name": "Location/Truck Name"
  },
  "location_scan_data": {
    // Previous location scan data (for truck scans only)
  },
  "ip_address": "client_ip",
  "user_agent": "browser_user_agent"
}
```

**Response Format:**
```json
{
  "success": true,
  "data": {
    "trip_id": 123,
    "status": "loading_start",
    "message": "Trip started successfully"
  }
}
```

### Driver Connect API: POST /api/driver/connect

**Required Request Payload:**
```json
{
  "driver_qr_data": {
    "employee_id": "EMP001",
    "full_name": "John Doe",
    "type": "driver"
  },
  "truck_qr_data": {
    "id": "T001",
    "truck_number": "T001",
    "type": "truck"
  },
  "action": "check_in|check_out|null"
}
```

**Response Format:**
```json
{
  "success": true,
  "action": "check_in",
  "message": "Driver checked in successfully",
  "shift_id": 456,
  "check_in_time": "2024-01-15T08:00:00Z"
}
```

## Critical Synchronization Requirements

### 1. Data Format Compatibility
- Offline storage MUST store data in exact API payload format
- No derived or calculated fields in offline storage
- Direct pass-through to API endpoints during sync

### 2. Required Fields for Sync
**Trip Scanner:**
- `scan_type`, `qr_data`, `location_scan_data`, `ip_address`, `user_agent`

**Driver Connect:**
- `driver_qr_data`, `truck_qr_data`, `action`

### 3. Temporal Validation Considerations
- 5-minute minimum gap for same-location scans
- Post-completion detection and validation
- 4-phase workflow integrity (loading_start → loading_end → unloading_start → unloading_end)

### 4. AutoAssignmentCreator Integration
- Handles cases where no valid assignment exists
- Creates assignments dynamically during sync
- Maintains workflow integrity

## Identified Mismatches in Current Implementation

### Current Offline Storage Issues:
1. **Over-complex data structure** - Storing derived data instead of raw API payloads
2. **Field name mismatches** - Offline field names don't match API expectations
3. **Missing required fields** - `ip_address`, `user_agent` not captured
4. **Unnecessary metadata** - Priority, retry logic should be separate from sync data

### Required Updates:
1. Simplify offline storage to match exact API format
2. Add missing required fields (ip_address, user_agent)
3. Separate sync metadata from actual payload data
4. Ensure direct compatibility with existing API endpoints

## Next Steps
1. Update `tripScannerOffline.js` to store exact API format
2. Update `driverConnectOffline.js` to store exact API format
3. Update `backgroundSync.js` to handle corrected data format
4. Test synchronization with production API endpoints
5. Validate temporal constraints and workflow integrity
