#!/usr/bin/env node

/**
 * Server Syntax Validation Script
 * 
 * This script tests for syntax errors in server files that could cause server log errors.
 */

const fs = require('fs');
const path = require('path');

class ServerSyntaxTester {
  constructor() {
    this.results = {
      syntax_tests: [],
      summary: {
        total_files: 0,
        passed: 0,
        failed: 0
      }
    };
  }

  /**
   * Test syntax of critical server files
   */
  async testServerSyntax() {
    console.log('🔍 Testing Server File Syntax');
    console.log('=' .repeat(50));

    const criticalFiles = [
      'server/server.js',
      'server/routes/assignments.js',
      'server/routes/scanner.js',
      'server/config/database.js',
      'server/middleware/auth.js'
    ];

    for (const filePath of criticalFiles) {
      await this.testFileSyntax(filePath);
    }

    this.generateSummaryReport();
  }

  /**
   * Test syntax of a single file
   */
  async testFileSyntax(filePath) {
    console.log(`\n📄 Testing: ${filePath}`);
    
    try {
      // Check if file exists
      if (!fs.existsSync(filePath)) {
        console.log(`   ❌ File not found: ${filePath}`);
        this.results.syntax_tests.push({
          file: filePath,
          status: 'FAILED',
          error: 'File not found'
        });
        return;
      }

      // Try to require the file to check for syntax errors
      const fullPath = path.resolve(filePath);
      
      // Clear require cache to ensure fresh load
      delete require.cache[fullPath];
      
      // Attempt to require the file
      require(fullPath);
      
      console.log(`   ✅ Syntax OK: ${filePath}`);
      this.results.syntax_tests.push({
        file: filePath,
        status: 'PASSED'
      });
      
    } catch (error) {
      console.log(`   ❌ Syntax Error: ${filePath}`);
      console.log(`      Error: ${error.message}`);
      
      this.results.syntax_tests.push({
        file: filePath,
        status: 'FAILED',
        error: error.message
      });
    }
  }

  /**
   * Generate summary report
   */
  generateSummaryReport() {
    console.log('\n📊 SYNTAX TEST SUMMARY');
    console.log('='.repeat(50));
    
    let passed = 0;
    let failed = 0;
    
    this.results.syntax_tests.forEach(test => {
      if (test.status === 'PASSED') {
        passed++;
        console.log(`✅ ${test.file}: ${test.status}`);
      } else {
        failed++;
        console.log(`❌ ${test.file}: ${test.status}`);
        if (test.error) {
          console.log(`   Error: ${test.error}`);
        }
      }
    });
    
    this.results.summary = {
      total_files: this.results.syntax_tests.length,
      passed,
      failed
    };
    
    console.log('\n📈 OVERALL RESULTS:');
    console.log(`   Total Files: ${this.results.summary.total_files}`);
    console.log(`   Passed: ${this.results.summary.passed}`);
    console.log(`   Failed: ${this.results.summary.failed}`);
    
    if (this.results.summary.failed === 0) {
      console.log('\n🎉 All server files have valid syntax!');
    } else {
      console.log('\n❌ Server syntax errors found - these need to be fixed!');
    }
  }
}

// Run tests if called directly
if (require.main === module) {
  const tester = new ServerSyntaxTester();
  tester.testServerSyntax().catch(console.error);
}

module.exports = ServerSyntaxTester;
