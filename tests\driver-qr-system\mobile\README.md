# Mobile Device Compatibility Tests

This directory contains comprehensive mobile compatibility tests for the Driver QR Code System, covering cross-browser functionality, responsive design, touch interface requirements, and camera performance.

## Test Coverage

### Cross-Browser Testing
- **Chrome Mobile**: Primary mobile browser testing with device emulation
- **Firefox Mobile**: Alternative browser compatibility testing
- **Safari Mobile**: iOS compatibility (requires macOS for full testing)

### Responsive Design Testing
- **Portrait Mode**: 375x812 (iPhone 12 Pro dimensions)
- **Landscape Mode**: 812x375 (rotated orientation)
- **Viewport Adaptation**: Ensures content fits without horizontal scrolling
- **Touch Target Sizing**: Validates 44px minimum touch targets

### Camera Performance Testing
- **Initialization Time**: Camera should start within 5 seconds
- **Close-Range Scanning**: Tests 4-8 inch scanning distance for ID cards
- **Lighting Conditions**: Simulates various lighting scenarios
- **Memory Management**: Monitors memory usage during extended camera use

### Touch Interface Requirements
- **Minimum Touch Targets**: All interactive elements ≥44px
- **Touch Gesture Support**: Proper touch event handling
- **Spacing Requirements**: Adequate spacing between touch targets
- **Accessibility**: ARIA labels and keyboard navigation support

## Prerequisites

### Required Dependencies
```bash
# Install Selenium WebDriver dependencies
npm install --save-dev selenium-webdriver chromedriver geckodriver
```

### Browser Setup
1. **Chrome**: Install ChromeDriver
   ```bash
   npm install --save-dev chromedriver
   ```

2. **Firefox**: Install GeckoDriver
   ```bash
   npm install --save-dev geckodriver
   ```

3. **Safari**: Enable WebDriver support (macOS only)
   ```bash
   safaridriver --enable
   ```

### System Requirements
- Node.js 16+ 
- Chrome/Chromium browser
- Firefox browser (optional)
- Safari browser (macOS only)
- Camera access permissions for testing

## Running Tests

### All Mobile Tests
```bash
# Run all mobile compatibility tests
npm run test:mobile

# Run with verbose output
npm run test:mobile -- --verbose

# Run specific test suite
npm run test:mobile -- --testNamePattern="Cross-Browser"
```

### Individual Test Categories
```bash
# Cross-browser compatibility
npm run test:mobile -- --testNamePattern="Cross-Browser QR Scanning Tests"

# Responsive design
npm run test:mobile -- --testNamePattern="Portrait and Landscape Mode"

# Touch interface
npm run test:mobile -- --testNamePattern="Touch Interface Requirements"

# Camera performance
npm run test:mobile -- --testNamePattern="Camera Performance"
```

### Development Mode
```bash
# Run tests in watch mode
npm run test:mobile -- --watch

# Run tests with coverage
npm run test:mobile -- --coverage
```

## Test Configuration

### Environment Variables
```bash
# Base URL for testing (default: http://localhost:3000)
TEST_BASE_URL=http://localhost:3000

# Skip browser tests in headless CI
CI=true

# Enable debug logging
DEBUG=true
```

### Browser Options
Tests automatically configure browsers with:
- Fake camera/microphone streams for testing
- Mobile device emulation
- Disabled web security for local testing
- Optimized timeouts for mobile operations

## Test Structure

```
mobile/
├── mobile-compatibility.test.js    # Main test suite
├── jest.config.js                  # Jest configuration
├── setup.js                        # Test setup and helpers
└── README.md                       # This documentation
```

## Expected Test Results

### Performance Benchmarks
- **Page Load Time**: < 3 seconds
- **Camera Initialization**: < 5 seconds  
- **Frame Rate**: > 30 FPS during camera operation
- **Memory Usage**: < 50MB increase during extended use

### Compatibility Requirements
- **Touch Targets**: All interactive elements ≥ 44px
- **Viewport Fit**: No horizontal scrolling required
- **Cross-Browser**: Works in Chrome, Firefox, Safari mobile
- **Orientation**: Functions in both portrait and landscape

## Troubleshooting

### Common Issues

1. **Camera Permission Denied**
   ```bash
   # Chrome: Add --use-fake-ui-for-media-stream flag
   # Firefox: Set media.navigator.permission.disabled = true
   ```

2. **WebDriver Connection Failed**
   ```bash
   # Ensure drivers are installed and in PATH
   npm install --save-dev chromedriver geckodriver
   ```

3. **Tests Timeout**
   ```bash
   # Increase timeout in jest.config.js
   testTimeout: 60000
   ```

4. **Display Issues in CI**
   ```bash
   # Use headless mode or skip browser tests
   export CI=true
   ```

### Debug Mode
```bash
# Run single test with debug output
npm run test:mobile -- --testNamePattern="should load driver connect page" --verbose
```

## Integration with CI/CD

### GitHub Actions Example
```yaml
- name: Run Mobile Tests
  run: |
    npm install
    npm run build
    npm start &
    sleep 10
    npm run test:mobile
  env:
    CI: true
    TEST_BASE_URL: http://localhost:3000
```

### Local Development
```bash
# Start the application
npm run dev

# In another terminal, run mobile tests
npm run test:mobile
```

## Contributing

When adding new mobile tests:

1. Follow the existing test structure and naming conventions
2. Include proper error handling and cleanup
3. Add appropriate timeouts for mobile operations
4. Test across multiple browsers when possible
5. Document any new test requirements or setup steps

## Requirements Mapping

This test suite validates the following requirements:
- **4.2**: Mobile-optimized UI functionality
- **10.2**: Camera controls and focus capability  
- **10.3**: Close-range ID scanning (4-8 inches)
- **10.4**: Touch interface accessibility standards