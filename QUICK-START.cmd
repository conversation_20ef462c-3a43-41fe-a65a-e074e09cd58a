@echo off
REM Quick start script for Ubuntu 24.04 container
REM This will guide you through the setup

echo === Docker Ubuntu 24.04 Quick Start ===
echo.

REM Check if Docker is running
docker info >nul 2>&1
if errorlevel 1 (
    echo ERROR: Docker Desktop is not running!
    echo.
    echo Please:
    echo 1. Start Docker Desktop
    echo 2. Wait for it to fully load (Docker icon appears in system tray)
    echo 3. Run this script again
    pause
    exit /b 1
)

echo Docker is running - proceeding with setup...
echo.

REM Pull Ubuntu image
echo Pulling Ubuntu 24.04 image...
docker pull ubuntu:24.04

REM Remove existing container if it exists
echo Cleaning up any existing container...
docker rm ubuntu-test-24 >nul 2>&1

REM Start the container
echo Starting Ubuntu 24.04 container...
echo.
echo Container will start with:
echo - Image: ubuntu:24.04
echo - Name: ubuntu-test-24
echo - Workspace mounted: %cd%
echo - Interactive terminal ready
echo.

docker run -it --name ubuntu-test-24 ^
  -v %cd%:/workspace ^
  -e DEBIAN_FRONTEND=noninteractive ^
  ubuntu:24.04 bash -c "
    echo === Ubuntu 24.04 Container Ready ===
    echo.
    echo Your workspace is mounted at: /workspace
    echo Available files:
    ls -la /workspace
    echo.
    echo Quick commands:
    echo 'apt update && apt install -y bash curl wget'
    echo 'cd /workspace'
    echo 'chmod +x *.sh'
    echo './test-script-ubuntu.sh'
    echo.
    bash
"

echo.
echo Container stopped.
echo To restart: docker start -ai ubuntu-test-24
pause