/**
 * Mobile Device Compatibility Tests for Driver QR Code System
 * 
 * Tests QR scanning functionality across different mobile browsers,
 * orientations, lighting conditions, and touch interface requirements.
 * 
 * Requirements: 4.2, 10.2, 10.3, 10.4
 */

const { Builder, By, until, Key } = require('selenium-webdriver');
const chrome = require('selenium-webdriver/chrome');
const firefox = require('selenium-webdriver/firefox');
const safari = require('selenium-webdriver/safari');

describe('Mobile Device Compatibility Tests', () => {
  let driver;
  const baseUrl = process.env.TEST_BASE_URL || 'http://localhost:3000';
  const driverConnectUrl = `${baseUrl}/driver-connect`;
  
  // Test timeout for mobile operations (longer due to camera initialization)
  const MOBILE_TIMEOUT = 15000;
  
  // Minimum touch target size (44px as per accessibility guidelines)
  const MIN_TOUCH_TARGET_SIZE = 44;

  afterEach(async () => {
    if (driver) {
      await driver.quit();
    }
  });

  describe('Cross-Browser QR Scanning Tests', () => {
    const browsers = [
      {
        name: 'Chrome Mobile',
        builder: () => {
          const options = new chrome.Options();
          options.addArguments('--use-fake-ui-for-media-stream');
          options.addArguments('--use-fake-device-for-media-stream');
          options.addArguments('--disable-web-security');
          options.addArguments('--allow-running-insecure-content');
          options.mobileEmulation({
            deviceName: 'iPhone 12 Pro'
          });
          return new Builder()
            .forBrowser('chrome')
            .setChromeOptions(options);
        }
      },
      {
        name: 'Firefox Mobile',
        builder: () => {
          const options = new firefox.Options();
          options.setPreference('media.navigator.streams.fake', true);
          options.setPreference('media.navigator.permission.disabled', true);
          return new Builder()
            .forBrowser('firefox')
            .setFirefoxOptions(options);
        }
      }
    ];

    browsers.forEach(({ name, builder }) => {
      describe(`${name} Tests`, () => {
        beforeEach(async () => {
          driver = await builder().build();
          await driver.manage().setTimeouts({ implicit: MOBILE_TIMEOUT });
        });

        test('should load driver connect page successfully', async () => {
          await driver.get(driverConnectUrl);
          
          // Wait for page to load
          await driver.wait(until.titleContains('Driver Connect'), MOBILE_TIMEOUT);
          
          // Verify page elements are present
          const heading = await driver.findElement(By.css('h1, h2'));
          expect(await heading.isDisplayed()).toBe(true);
          
          const scanButton = await driver.findElement(By.css('[data-testid="scan-button"], button'));
          expect(await scanButton.isDisplayed()).toBe(true);
        });

        test('should initialize camera for QR scanning', async () => {
          await driver.get(driverConnectUrl);
          
          // Click scan button to start camera
          const scanButton = await driver.wait(
            until.elementLocated(By.css('[data-testid="scan-button"], button')),
            MOBILE_TIMEOUT
          );
          await scanButton.click();
          
          // Wait for camera to initialize
          await driver.sleep(2000);
          
          // Check if video element is present and active
          const videoElement = await driver.wait(
            until.elementLocated(By.css('video')),
            MOBILE_TIMEOUT
          );
          expect(await videoElement.isDisplayed()).toBe(true);
          
          // Verify video has dimensions (camera is active)
          const videoSize = await videoElement.getSize();
          expect(videoSize.width).toBeGreaterThan(0);
          expect(videoSize.height).toBeGreaterThan(0);
        });

        test('should handle camera permission denial gracefully', async () => {
          // This test simulates permission denial
          await driver.get(driverConnectUrl);
          
          const scanButton = await driver.findElement(By.css('[data-testid="scan-button"], button'));
          await scanButton.click();
          
          // Wait for potential error message
          await driver.sleep(3000);
          
          // Check for error handling UI
          const errorElements = await driver.findElements(By.css('[data-testid="error-message"], .error, .alert-danger'));
          
          // Should either show error message or fallback UI
          if (errorElements.length > 0) {
            const errorMessage = await errorElements[0].getText();
            expect(errorMessage.toLowerCase()).toMatch(/camera|permission|access/);
          }
        });
      });
    });
  });

  describe('Portrait and Landscape Mode Compatibility', () => {
    const orientations = [
      { name: 'Portrait', width: 375, height: 812 },
      { name: 'Landscape', width: 812, height: 375 }
    ];

    orientations.forEach(({ name, width, height }) => {
      describe(`${name} Mode Tests`, () => {
        beforeEach(async () => {
          const options = new chrome.Options();
          options.addArguments('--use-fake-ui-for-media-stream');
          options.addArguments('--use-fake-device-for-media-stream');
          options.addArguments(`--window-size=${width},${height}`);
          
          driver = await new Builder()
            .forBrowser('chrome')
            .setChromeOptions(options)
            .build();
            
          await driver.manage().window().setRect({ width, height, x: 0, y: 0 });
        });

        test(`should display properly in ${name.toLowerCase()} mode`, async () => {
          await driver.get(driverConnectUrl);
          
          // Verify page loads and is responsive
          const body = await driver.findElement(By.css('body'));
          expect(await body.isDisplayed()).toBe(true);
          
          // Check that main content is visible
          const mainContent = await driver.findElement(By.css('main, .main-content, [data-testid="main-content"]'));
          expect(await mainContent.isDisplayed()).toBe(true);
          
          // Verify no horizontal scrolling is needed
          const bodySize = await body.getSize();
          expect(bodySize.width).toBeLessThanOrEqual(width + 20); // Allow small margin
        });

        test(`should maintain usable interface in ${name.toLowerCase()} mode`, async () => {
          await driver.get(driverConnectUrl);
          
          // Find all interactive elements
          const buttons = await driver.findElements(By.css('button'));
          const inputs = await driver.findElements(By.css('input'));
          const links = await driver.findElements(By.css('a'));
          
          const interactiveElements = [...buttons, ...inputs, ...links];
          
          // Verify all interactive elements are accessible
          for (const element of interactiveElements) {
            if (await element.isDisplayed()) {
              const size = await element.getSize();
              const location = await element.getLocation();
              
              // Element should be within viewport
              expect(location.x).toBeGreaterThanOrEqual(0);
              expect(location.y).toBeGreaterThanOrEqual(0);
              expect(location.x + size.width).toBeLessThanOrEqual(width);
              expect(location.y + size.height).toBeLessThanOrEqual(height);
            }
          }
        });

        test(`should handle camera orientation in ${name.toLowerCase()} mode`, async () => {
          await driver.get(driverConnectUrl);
          
          const scanButton = await driver.findElement(By.css('[data-testid="scan-button"], button'));
          await scanButton.click();
          
          await driver.sleep(2000);
          
          // Check if video element adapts to orientation
          const videoElements = await driver.findElements(By.css('video'));
          if (videoElements.length > 0) {
            const video = videoElements[0];
            const videoSize = await video.getSize();
            
            // Video should fit within the viewport
            expect(videoSize.width).toBeLessThanOrEqual(width);
            expect(videoSize.height).toBeLessThanOrEqual(height);
          }
        });
      });
    });
  });

  describe('Touch Interface Requirements', () => {
    beforeEach(async () => {
      const options = new chrome.Options();
      options.addArguments('--use-fake-ui-for-media-stream');
      options.mobileEmulation({
        deviceName: 'iPhone 12 Pro'
      });
      
      driver = await new Builder()
        .forBrowser('chrome')
        .setChromeOptions(options)
        .build();
    });

    test('should meet minimum touch target size requirements (44px)', async () => {
      await driver.get(driverConnectUrl);
      
      // Find all interactive elements
      const interactiveSelectors = [
        'button',
        'input[type="button"]',
        'input[type="submit"]',
        'a',
        '[role="button"]',
        '[data-testid*="button"]'
      ];
      
      for (const selector of interactiveSelectors) {
        const elements = await driver.findElements(By.css(selector));
        
        for (const element of elements) {
          if (await element.isDisplayed()) {
            const size = await element.getSize();
            
            // Check minimum touch target size
            expect(size.width).toBeGreaterThanOrEqual(MIN_TOUCH_TARGET_SIZE);
            expect(size.height).toBeGreaterThanOrEqual(MIN_TOUCH_TARGET_SIZE);
          }
        }
      }
    });

    test('should provide adequate spacing between touch targets', async () => {
      await driver.get(driverConnectUrl);
      
      const buttons = await driver.findElements(By.css('button'));
      
      if (buttons.length > 1) {
        for (let i = 0; i < buttons.length - 1; i++) {
          const button1 = buttons[i];
          const button2 = buttons[i + 1];
          
          if (await button1.isDisplayed() && await button2.isDisplayed()) {
            const loc1 = await button1.getLocation();
            const size1 = await button1.getSize();
            const loc2 = await button2.getLocation();
            
            // Calculate distance between buttons
            const horizontalDistance = Math.abs(loc2.x - (loc1.x + size1.width));
            const verticalDistance = Math.abs(loc2.y - (loc1.y + size1.height));
            
            // Minimum 8px spacing recommended
            if (horizontalDistance < 100 && verticalDistance < 100) {
              expect(Math.min(horizontalDistance, verticalDistance)).toBeGreaterThanOrEqual(8);
            }
          }
        }
      }
    });

    test('should handle touch gestures properly', async () => {
      await driver.get(driverConnectUrl);
      
      const scanButton = await driver.findElement(By.css('[data-testid="scan-button"], button'));
      
      // Simulate touch tap
      await driver.executeScript(`
        const element = arguments[0];
        const touchEvent = new TouchEvent('touchstart', {
          bubbles: true,
          cancelable: true,
          touches: [{
            clientX: element.offsetLeft + element.offsetWidth / 2,
            clientY: element.offsetTop + element.offsetHeight / 2
          }]
        });
        element.dispatchEvent(touchEvent);
      `, scanButton);
      
      await driver.sleep(100);
      
      await driver.executeScript(`
        const element = arguments[0];
        const touchEvent = new TouchEvent('touchend', {
          bubbles: true,
          cancelable: true
        });
        element.dispatchEvent(touchEvent);
      `, scanButton);
      
      // Verify button responds to touch
      await driver.sleep(1000);
      
      // Check if camera or next step is initiated
      const videoElements = await driver.findElements(By.css('video'));
      const nextStepElements = await driver.findElements(By.css('[data-testid="step-2"], .step-2'));
      
      expect(videoElements.length > 0 || nextStepElements.length > 0).toBe(true);
    });
  });

  describe('Camera Performance and Lighting Conditions', () => {
    beforeEach(async () => {
      const options = new chrome.Options();
      options.addArguments('--use-fake-ui-for-media-stream');
      options.addArguments('--use-fake-device-for-media-stream');
      options.mobileEmulation({
        deviceName: 'iPhone 12 Pro'
      });
      
      driver = await new Builder()
        .forBrowser('chrome')
        .setChromeOptions(options)
        .build();
    });

    test('should initialize camera within reasonable time', async () => {
      await driver.get(driverConnectUrl);
      
      const startTime = Date.now();
      
      const scanButton = await driver.findElement(By.css('[data-testid="scan-button"], button'));
      await scanButton.click();
      
      // Wait for video element to appear
      await driver.wait(until.elementLocated(By.css('video')), MOBILE_TIMEOUT);
      
      const initTime = Date.now() - startTime;
      
      // Camera should initialize within 5 seconds
      expect(initTime).toBeLessThan(5000);
    });

    test('should provide camera controls for focus adjustment', async () => {
      await driver.get(driverConnectUrl);
      
      const scanButton = await driver.findElement(By.css('[data-testid="scan-button"], button'));
      await scanButton.click();
      
      await driver.sleep(2000);
      
      // Look for camera control elements
      const cameraControls = await driver.findElements(By.css(
        '[data-testid*="camera"], [data-testid*="focus"], .camera-controls, .focus-controls'
      ));
      
      // Should have some form of camera controls or auto-focus capability
      if (cameraControls.length > 0) {
        expect(cameraControls[0].isDisplayed()).toBe(true);
      }
    });

    test('should handle close-range scanning (4-8 inches)', async () => {
      await driver.get(driverConnectUrl);
      
      const scanButton = await driver.findElement(By.css('[data-testid="scan-button"], button'));
      await scanButton.click();
      
      await driver.sleep(2000);
      
      // Simulate close-range QR code detection
      await driver.executeScript(`
        // Simulate QR code detection event
        if (window.qrScanner && window.qrScanner.onScanSuccess) {
          window.qrScanner.onScanSuccess('{"id":"test-driver-123","driver_id":1,"employee_id":"EMP001","generated_date":"2025-01-27"}');
        } else {
          // Dispatch custom event for QR detection
          window.dispatchEvent(new CustomEvent('qr-code-detected', {
            detail: '{"id":"test-driver-123","driver_id":1,"employee_id":"EMP001","generated_date":"2025-01-27"}'
          }));
        }
      `);
      
      await driver.sleep(1000);
      
      // Check if QR code was processed
      const nextStepElements = await driver.findElements(By.css('[data-testid="step-2"], .step-2'));
      const successElements = await driver.findElements(By.css('[data-testid="scan-success"], .scan-success'));
      
      expect(nextStepElements.length > 0 || successElements.length > 0).toBe(true);
    });

    test('should provide feedback for scanning issues', async () => {
      await driver.get(driverConnectUrl);
      
      const scanButton = await driver.findElement(By.css('[data-testid="scan-button"], button'));
      await scanButton.click();
      
      await driver.sleep(2000);
      
      // Simulate scanning failure or poor lighting
      await driver.executeScript(`
        // Simulate scanning error
        if (window.qrScanner && window.qrScanner.onScanFailure) {
          window.qrScanner.onScanFailure('Poor lighting conditions');
        } else {
          window.dispatchEvent(new CustomEvent('qr-scan-error', {
            detail: 'Poor lighting conditions'
          }));
        }
      `);
      
      await driver.sleep(1000);
      
      // Look for user feedback elements
      const feedbackElements = await driver.findElements(By.css(
        '[data-testid*="feedback"], [data-testid*="error"], .feedback, .scan-feedback, .error-message'
      ));
      
      if (feedbackElements.length > 0) {
        const feedbackText = await feedbackElements[0].getText();
        expect(feedbackText.length).toBeGreaterThan(0);
      }
    });
  });

  describe('Performance and Responsiveness', () => {
    beforeEach(async () => {
      const options = new chrome.Options();
      options.addArguments('--use-fake-ui-for-media-stream');
      options.mobileEmulation({
        deviceName: 'iPhone 12 Pro'
      });
      
      driver = await new Builder()
        .forBrowser('chrome')
        .setChromeOptions(options)
        .build();
    });

    test('should load page within 3 seconds on mobile', async () => {
      const startTime = Date.now();
      
      await driver.get(driverConnectUrl);
      await driver.wait(until.elementLocated(By.css('body')), MOBILE_TIMEOUT);
      
      const loadTime = Date.now() - startTime;
      
      // Page should load within 3 seconds as per requirements
      expect(loadTime).toBeLessThan(3000);
    });

    test('should maintain 60fps during camera operation', async () => {
      await driver.get(driverConnectUrl);
      
      const scanButton = await driver.findElement(By.css('[data-testid="scan-button"], button'));
      await scanButton.click();
      
      await driver.sleep(2000);
      
      // Measure frame rate during camera operation
      const frameRate = await driver.executeScript(`
        return new Promise((resolve) => {
          let frameCount = 0;
          const startTime = performance.now();
          
          function countFrames() {
            frameCount++;
            if (performance.now() - startTime < 1000) {
              requestAnimationFrame(countFrames);
            } else {
              resolve(frameCount);
            }
          }
          
          requestAnimationFrame(countFrames);
        });
      `);
      
      // Should maintain reasonable frame rate (at least 30fps)
      expect(frameRate).toBeGreaterThan(30);
    });

    test('should handle memory efficiently during extended use', async () => {
      await driver.get(driverConnectUrl);
      
      // Get initial memory usage
      const initialMemory = await driver.executeScript(`
        return performance.memory ? performance.memory.usedJSHeapSize : 0;
      `);
      
      // Simulate extended camera usage
      const scanButton = await driver.findElement(By.css('[data-testid="scan-button"], button'));
      await scanButton.click();
      
      await driver.sleep(5000);
      
      // Stop camera
      const stopElements = await driver.findElements(By.css('[data-testid="stop-camera"], .stop-camera, button'));
      if (stopElements.length > 0) {
        await stopElements[0].click();
      }
      
      await driver.sleep(1000);
      
      // Check memory usage after cleanup
      const finalMemory = await driver.executeScript(`
        return performance.memory ? performance.memory.usedJSHeapSize : 0;
      `);
      
      // Memory increase should be reasonable (less than 50MB)
      const memoryIncrease = finalMemory - initialMemory;
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);
    });
  });

  describe('Accessibility and Usability', () => {
    beforeEach(async () => {
      const options = new chrome.Options();
      options.addArguments('--use-fake-ui-for-media-stream');
      options.mobileEmulation({
        deviceName: 'iPhone 12 Pro'
      });
      
      driver = await new Builder()
        .forBrowser('chrome')
        .setChromeOptions(options)
        .build();
    });

    test('should provide clear visual instructions', async () => {
      await driver.get(driverConnectUrl);
      
      // Look for instruction text
      const instructionElements = await driver.findElements(By.css(
        '[data-testid*="instruction"], .instruction, .step-instruction, h1, h2, h3'
      ));
      
      expect(instructionElements.length).toBeGreaterThan(0);
      
      const instructionText = await instructionElements[0].getText();
      expect(instructionText.toLowerCase()).toMatch(/scan|qr|code|id/);
    });

    test('should support keyboard navigation', async () => {
      await driver.get(driverConnectUrl);
      
      // Tab through interactive elements
      const body = await driver.findElement(By.css('body'));
      await body.sendKeys(Key.TAB);
      
      const activeElement = await driver.switchTo().activeElement();
      expect(await activeElement.getTagName()).toMatch(/button|input|a/i);
    });

    test('should provide appropriate ARIA labels', async () => {
      await driver.get(driverConnectUrl);
      
      const interactiveElements = await driver.findElements(By.css('button, input, a'));
      
      for (const element of interactiveElements) {
        if (await element.isDisplayed()) {
          const ariaLabel = await element.getAttribute('aria-label');
          const ariaLabelledBy = await element.getAttribute('aria-labelledby');
          const title = await element.getAttribute('title');
          const text = await element.getText();
          
          // Element should have some form of accessible label
          expect(
            ariaLabel || ariaLabelledBy || title || text
          ).toBeTruthy();
        }
      }
    });
  });
});

// Helper functions for mobile testing
const MobileTestHelpers = {
  /**
   * Simulate device rotation
   */
  async rotateDevice(driver, orientation) {
    await driver.executeScript(`
      screen.orientation.lock('${orientation}');
    `);
  },

  /**
   * Simulate different network conditions
   */
  async setNetworkConditions(driver, conditions) {
    await driver.executeScript(`
      // Simulate network conditions
      if (navigator.connection) {
        Object.defineProperty(navigator.connection, 'effectiveType', {
          value: '${conditions.effectiveType || '4g'}'
        });
      }
    `);
  },

  /**
   * Check if element is within viewport
   */
  async isElementInViewport(driver, element) {
    return await driver.executeScript(`
      const rect = arguments[0].getBoundingClientRect();
      return (
        rect.top >= 0 &&
        rect.left >= 0 &&
        rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
        rect.right <= (window.innerWidth || document.documentElement.clientWidth)
      );
    `, element);
  },

  /**
   * Measure page load performance
   */
  async measurePageLoad(driver) {
    return await driver.executeScript(`
      const perfData = performance.getEntriesByType('navigation')[0];
      return {
        loadTime: perfData.loadEventEnd - perfData.loadEventStart,
        domContentLoaded: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
        firstPaint: performance.getEntriesByType('paint').find(entry => entry.name === 'first-paint')?.startTime || 0
      };
    `);
  }
};

module.exports = { MobileTestHelpers };