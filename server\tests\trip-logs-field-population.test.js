const { query, getClient } = require('../config/database');
const request = require('supertest');
const app = require('../server');

describe('Trip Logs Field Population Tests', () => {
  let client;
  let testTruckId;
  let testDriverId;
  let testLocationId;
  let testAssignmentId;
  let authToken;

  beforeAll(async () => {
    client = await getClient();
    
    // Create test truck
    const truckResult = await client.query(`
      INSERT INTO dump_trucks (truck_number, license_plate, status, qr_code_data)
      VALUES ('TEST002', 'TEST-002', 'active', '{"type": "truck", "id": "TEST002"}')
      RETURNING id
    `);
    testTruckId = truckResult.rows[0].id;

    // Create test driver
    const driverResult = await client.query(`
      INSERT INTO drivers (full_name, employee_id, status)
      VALUES ('Test Driver 2', 'EMP002', 'active')
      RETURNING id
    `);
    testDriverId = driverResult.rows[0].id;

    // Create test location
    const locationResult = await client.query(`
      INSERT INTO locations (location_code, name, type, status, qr_code_data)
      VALUES ('LOC001', 'Test Location', 'loading', 'active', '{"type": "location", "id": "LOC001"}')
      RETURNING id
    `);
    testLocationId = locationResult.rows[0].id;

    // Create test assignment
    const assignmentResult = await client.query(`
      INSERT INTO assignments (
        assignment_code, truck_id, loading_location_id, unloading_location_id,
        loading_location_name, unloading_location_name, status
      ) VALUES ('ASG001', $1, $2, $2, 'Test Loading', 'Test Unloading', 'active')
      RETURNING id
    `, [testTruckId, testLocationId]);
    testAssignmentId = assignmentResult.rows[0].id;

    // Create test user for authentication
    const userResult = await client.query(`
      INSERT INTO users (username, password_hash, role, status)
      VALUES ('testuser', '$2b$10$test', 'supervisor', 'active')
      RETURNING id
    `);

    // Mock authentication token (in real app, this would be generated properly)
    authToken = 'mock-jwt-token';
  });

  afterAll(async () => {
    // Clean up test data
    await client.query('DELETE FROM trip_logs WHERE assignment_id = $1', [testAssignmentId]);
    await client.query('DELETE FROM driver_shifts WHERE driver_id = $1', [testDriverId]);
    await client.query('DELETE FROM assignments WHERE id = $1', [testAssignmentId]);
    await client.query('DELETE FROM locations WHERE id = $1', [testLocationId]);
    await client.query('DELETE FROM drivers WHERE id = $1', [testDriverId]);
    await client.query('DELETE FROM dump_trucks WHERE id = $1', [testTruckId]);
    await client.query('DELETE FROM users WHERE username = $1', ['testuser']);
    
    client.release();
  });

  beforeEach(async () => {
    // Clean up any existing shifts and trips before each test
    await client.query('DELETE FROM trip_logs WHERE assignment_id = $1', [testAssignmentId]);
    await client.query('DELETE FROM driver_shifts WHERE driver_id = $1', [testDriverId]);
  });

  describe('Notes Field Population', () => {
    test('Trip creation should populate notes with contextual information', async () => {
      // Create active shift
      const checkInTime = new Date('2024-07-28T08:00:00');
      await client.query(`
        INSERT INTO driver_shifts (
          truck_id, driver_id, shift_type, start_date, end_date,
          start_time, end_time, status, auto_created, created_at, updated_at
        ) VALUES ($1, $2, 'day', $3, NULL, $4, NULL, 'active', true, $5, $6)
      `, [
        testTruckId, testDriverId, '2024-07-28', '08:00:00',
        checkInTime, checkInTime
      ]);

      // Simulate truck scan for trip creation
      const scanData = {
        scan_type: 'truck',
        scanned_data: JSON.stringify({ type: 'truck', id: 'TEST002' }),
        location_scan_data: { type: 'location', id: 'LOC001' }
      };

      // Make request to scanner endpoint (mocking authentication)
      const response = await request(app)
        .post('/api/scanner/public-scan')
        .send(scanData)
        .expect(200);

      expect(response.body.success).toBe(true);

      // Check that trip_logs entry was created with proper notes
      const tripResult = await client.query(`
        SELECT notes, performed_by_driver_name, performed_by_shift_type, location_sequence
        FROM trip_logs 
        WHERE assignment_id = $1
        ORDER BY created_at DESC
        LIMIT 1
      `, [testAssignmentId]);

      expect(tripResult.rows.length).toBe(1);
      const trip = tripResult.rows[0];

      // Validate notes field
      expect(trip.notes).toBeDefined();
      const notes = JSON.parse(trip.notes);
      expect(notes.action).toBeDefined();
      expect(notes.timestamp).toBeDefined();
      expect(notes.workflow_type).toBe('standard');
      expect(notes.driver).toBeDefined();
      expect(notes.driver.name).toBe('Test Driver 2');
      expect(notes.driver.employee_id).toBe('EMP002');
      expect(notes.truck).toBeDefined();
      expect(notes.truck.truck_number).toBe('TEST002');

      // Validate driver fields
      expect(trip.performed_by_driver_name).toBe('Test Driver 2');
      expect(trip.performed_by_shift_type).toBe('day');

      // Validate location_sequence is populated (via updateLocationSequence)
      expect(trip.location_sequence).toBeDefined();
      const locationSequence = JSON.parse(trip.location_sequence);
      expect(Array.isArray(locationSequence)).toBe(true);
    });

    test('Notes should include proper context for different trip statuses', async () => {
      // Create active shift
      const checkInTime = new Date('2024-07-28T08:00:00');
      await client.query(`
        INSERT INTO driver_shifts (
          truck_id, driver_id, shift_type, start_date, end_date,
          start_time, end_time, status, auto_created, created_at, updated_at
        ) VALUES ($1, $2, 'night', $3, NULL, $4, NULL, 'active', true, $5, $6)
      `, [
        testTruckId, testDriverId, '2024-07-28', '22:00:00',
        checkInTime, checkInTime
      ]);

      // Create initial trip
      await client.query(`
        INSERT INTO trip_logs (
          assignment_id, trip_number, status, loading_start_time, actual_loading_location_id,
          performed_by_driver_id, performed_by_driver_name, performed_by_employee_id,
          performed_by_shift_id, performed_by_shift_type, notes, created_at, updated_at
        ) VALUES ($1, 1, 'loading_start', CURRENT_TIMESTAMP, $2, $3, 'Test Driver 2', 'EMP002', 1, 'night', '{}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        RETURNING id
      `, [testAssignmentId, testLocationId, testDriverId]);

      // Simulate loading completion scan
      const scanData = {
        scan_type: 'truck',
        scanned_data: JSON.stringify({ type: 'truck', id: 'TEST002' }),
        location_scan_data: { type: 'location', id: 'LOC001' }
      };

      const response = await request(app)
        .post('/api/scanner/public-scan')
        .send(scanData)
        .expect(200);

      // Check that the trip was updated with proper notes for loading_end
      const tripResult = await client.query(`
        SELECT notes, status
        FROM trip_logs 
        WHERE assignment_id = $1 AND status = 'loading_end'
        ORDER BY created_at DESC
        LIMIT 1
      `, [testAssignmentId]);

      if (tripResult.rows.length > 0) {
        const trip = tripResult.rows[0];
        const notes = JSON.parse(trip.notes);
        expect(notes.action).toBe('loading_end');
        expect(notes.driver.shift_type).toBe('night');
      }
    });
  });

  describe('Location Sequence Population', () => {
    test('Location sequence should be updated after trip status changes', async () => {
      // Create active shift
      const checkInTime = new Date('2024-07-28T08:00:00');
      await client.query(`
        INSERT INTO driver_shifts (
          truck_id, driver_id, shift_type, start_date, end_date,
          start_time, end_time, status, auto_created, created_at, updated_at
        ) VALUES ($1, $2, 'day', $3, NULL, $4, NULL, 'active', true, $5, $6)
      `, [
        testTruckId, testDriverId, '2024-07-28', '08:00:00',
        checkInTime, checkInTime
      ]);

      // Create trip
      const tripResult = await client.query(`
        INSERT INTO trip_logs (
          assignment_id, trip_number, status, loading_start_time, actual_loading_location_id,
          performed_by_driver_id, performed_by_driver_name, performed_by_employee_id,
          performed_by_shift_id, performed_by_shift_type, notes, created_at, updated_at
        ) VALUES ($1, 1, 'loading_start', CURRENT_TIMESTAMP, $2, $3, 'Test Driver 2', 'EMP002', 1, 'day', '{}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        RETURNING id
      `, [testAssignmentId, testLocationId, testDriverId]);

      const tripId = tripResult.rows[0].id;

      // Check initial location_sequence (should be null initially)
      let sequenceCheck = await client.query(
        'SELECT location_sequence FROM trip_logs WHERE id = $1',
        [tripId]
      );

      // After updateLocationSequence is called (which happens in the scanner), check again
      // Simulate the updateLocationSequence call
      const locationSequence = [
        {
          name: 'Test Loading',
          type: 'loading',
          confirmed: true,
          location_id: testLocationId
        },
        {
          name: 'Test Unloading',
          type: 'unloading',
          confirmed: false,
          location_id: testLocationId
        }
      ];

      await client.query(`
        UPDATE trip_logs
        SET location_sequence = $1, updated_at = CURRENT_TIMESTAMP
        WHERE id = $2
      `, [JSON.stringify(locationSequence), tripId]);

      // Verify location_sequence is properly formatted
      sequenceCheck = await client.query(
        'SELECT location_sequence FROM trip_logs WHERE id = $1',
        [tripId]
      );

      expect(sequenceCheck.rows[0].location_sequence).toBeDefined();
      const sequence = JSON.parse(sequenceCheck.rows[0].location_sequence);
      expect(Array.isArray(sequence)).toBe(true);
      expect(sequence.length).toBe(2);
      expect(sequence[0].type).toBe('loading');
      expect(sequence[0].confirmed).toBe(true);
      expect(sequence[1].type).toBe('unloading');
      expect(sequence[1].confirmed).toBe(false);
    });
  });

  describe('Driver Field Population Completeness', () => {
    test('All driver fields should be populated when active driver exists', async () => {
      // Create active shift
      const checkInTime = new Date('2024-07-28T08:00:00');
      const shiftResult = await client.query(`
        INSERT INTO driver_shifts (
          truck_id, driver_id, shift_type, start_date, end_date,
          start_time, end_time, status, auto_created, created_at, updated_at
        ) VALUES ($1, $2, 'day', $3, NULL, $4, NULL, 'active', true, $5, $6)
        RETURNING id
      `, [
        testTruckId, testDriverId, '2024-07-28', '08:00:00',
        checkInTime, checkInTime
      ]);
      const shiftId = shiftResult.rows[0].id;

      // Simulate trip creation
      const scanData = {
        scan_type: 'truck',
        scanned_data: JSON.stringify({ type: 'truck', id: 'TEST002' }),
        location_scan_data: { type: 'location', id: 'LOC001' }
      };

      const response = await request(app)
        .post('/api/scanner/public-scan')
        .send(scanData)
        .expect(200);

      // Verify all driver fields are populated
      const tripResult = await client.query(`
        SELECT 
          performed_by_driver_id,
          performed_by_driver_name,
          performed_by_employee_id,
          performed_by_shift_id,
          performed_by_shift_type
        FROM trip_logs 
        WHERE assignment_id = $1
        ORDER BY created_at DESC
        LIMIT 1
      `, [testAssignmentId]);

      expect(tripResult.rows.length).toBe(1);
      const trip = tripResult.rows[0];

      expect(trip.performed_by_driver_id).toBe(testDriverId);
      expect(trip.performed_by_driver_name).toBe('Test Driver 2');
      expect(trip.performed_by_employee_id).toBe('EMP002');
      expect(trip.performed_by_shift_id).toBe(shiftId);
      expect(trip.performed_by_shift_type).toBe('day');
    });

    test('Driver fields should handle missing driver gracefully', async () => {
      // Don't create any active shift - simulate no active driver scenario
      
      // Simulate trip creation without active driver
      const scanData = {
        scan_type: 'truck',
        scanned_data: JSON.stringify({ type: 'truck', id: 'TEST002' }),
        location_scan_data: { type: 'location', id: 'LOC001' }
      };

      const response = await request(app)
        .post('/api/scanner/public-scan')
        .send(scanData)
        .expect(200);

      // Check if trip was created and how driver fields are handled
      const tripResult = await client.query(`
        SELECT 
          performed_by_driver_id,
          performed_by_driver_name,
          performed_by_employee_id,
          performed_by_shift_id,
          performed_by_shift_type,
          notes
        FROM trip_logs 
        WHERE assignment_id = $1
        ORDER BY created_at DESC
        LIMIT 1
      `, [testAssignmentId]);

      if (tripResult.rows.length > 0) {
        const trip = tripResult.rows[0];
        
        // Driver fields should be null when no active driver
        expect(trip.performed_by_driver_id).toBeNull();
        expect(trip.performed_by_driver_name).toBeNull();
        expect(trip.performed_by_employee_id).toBeNull();
        expect(trip.performed_by_shift_id).toBeNull();
        expect(trip.performed_by_shift_type).toBeNull();

        // Notes should still be populated but with null driver info
        const notes = JSON.parse(trip.notes);
        expect(notes.driver).toBeNull();
      }
    });
  });

  describe('Performance Impact Tests', () => {
    test('Enhanced field population should not significantly impact performance', async () => {
      // Create active shift
      const checkInTime = new Date('2024-07-28T08:00:00');
      await client.query(`
        INSERT INTO driver_shifts (
          truck_id, driver_id, shift_type, start_date, end_date,
          start_time, end_time, status, auto_created, created_at, updated_at
        ) VALUES ($1, $2, 'day', $3, NULL, $4, NULL, 'active', true, $5, $6)
      `, [
        testTruckId, testDriverId, '2024-07-28', '08:00:00',
        checkInTime, checkInTime
      ]);

      const scanData = {
        scan_type: 'truck',
        scanned_data: JSON.stringify({ type: 'truck', id: 'TEST002' }),
        location_scan_data: { type: 'location', id: 'LOC001' }
      };

      // Test multiple rapid requests
      const startTime = Date.now();
      const promises = [];

      for (let i = 0; i < 5; i++) {
        promises.push(
          request(app)
            .post('/api/scanner/public-scan')
            .send(scanData)
        );
      }

      const responses = await Promise.all(promises);
      const endTime = Date.now();

      // All requests should succeed
      responses.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
      });

      // Performance should be reasonable (under 3 seconds for 5 requests)
      expect(endTime - startTime).toBeLessThan(3000);
    });
  });
});