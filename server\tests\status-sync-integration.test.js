/**
 * Status Synchronization Integration Test
 * 
 * Tests the integration of the monitoring service with the server
 */

const request = require('supertest');
const express = require('express');
const statusSyncMonitor = require('../services/StatusSynchronizationMonitor');

// Mock authentication middleware
const mockAuth = (req, res, next) => {
  req.user = { id: 1, username: 'admin', role: 'admin' };
  next();
};

// Create test app with routes
const app = express();
app.use(express.json());

// Import and setup routes
const dataFlowRoutes = require('../routes/data-flow-validation');
app.use('/api/data-flow-validation', dataFlowRoutes);

// Mock the auth middleware
jest.mock('../middleware/auth', () => mockAuth);

// Mock the StatusSynchronizationService
jest.mock('../services/StatusSynchronizationService', () => ({
  monitorStatusSynchronization: jest.fn().mockResolvedValue({
    overall_status: 'operational',
    sync_issues: [],
    auto_fixes_applied: 0,
    metrics: {
      shifts_checked: 5,
      assignments_checked: 3,
      trips_checked: 10
    },
    monitoring_duration_ms: 150
  }),
  createSyncAlerts: jest.fn().mockResolvedValue([])
}));

describe('Status Synchronization Integration', () => {
  beforeEach(() => {
    statusSyncMonitor.stop();
    jest.clearAllMocks();
  });

  afterEach(() => {
    statusSyncMonitor.stop();
  });

  describe('API Endpoints', () => {
    test('GET /api/data-flow-validation/monitor-status should return monitoring status', async () => {
      statusSyncMonitor.start(30000);

      const response = await request(app)
        .get('/api/data-flow-validation/monitor-status')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('is_running', true);
      expect(response.body.data).toHaveProperty('check_interval_ms', 30000);
      expect(response.body.data).toHaveProperty('metrics');
    });

    test('POST /api/data-flow-validation/force-monitor-check should trigger manual check', async () => {
      const response = await request(app)
        .post('/api/data-flow-validation/force-monitor-check')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('results');
      expect(response.body.data).toHaveProperty('duration_ms');
      expect(response.body.data).toHaveProperty('correlation_id');
    });

    test('GET /api/data-flow-validation/alerts should return alerts', async () => {
      const response = await request(app)
        .get('/api/data-flow-validation/alerts')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('active_alerts');
      expect(response.body.data).toHaveProperty('alert_history');
      expect(response.body.data).toHaveProperty('summary');
    });

    test('POST /api/data-flow-validation/start-monitor should start monitoring', async () => {
      const response = await request(app)
        .post('/api/data-flow-validation/start-monitor')
        .send({ interval: 45000 })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('check_interval_ms', 45000);
      expect(statusSyncMonitor.getStatus().is_running).toBe(true);
    });

    test('POST /api/data-flow-validation/stop-monitor should stop monitoring', async () => {
      statusSyncMonitor.start(30000);
      expect(statusSyncMonitor.getStatus().is_running).toBe(true);

      const response = await request(app)
        .post('/api/data-flow-validation/stop-monitor')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(statusSyncMonitor.getStatus().is_running).toBe(false);
    });
  });

  describe('Service Integration', () => {
    test('should integrate with existing monitoring infrastructure', () => {
      // Test that the service can be started and stopped
      expect(statusSyncMonitor.getStatus().is_running).toBe(false);
      
      statusSyncMonitor.start(60000);
      expect(statusSyncMonitor.getStatus().is_running).toBe(true);
      expect(statusSyncMonitor.getStatus().check_interval_ms).toBe(60000);
      
      statusSyncMonitor.stop();
      expect(statusSyncMonitor.getStatus().is_running).toBe(false);
    });

    test('should provide comprehensive status information', () => {
      const status = statusSyncMonitor.getStatus();
      
      // Verify all required status fields are present
      const requiredFields = [
        'is_running',
        'check_interval_ms',
        'last_check',
        'consecutive_errors',
        'metrics',
        'active_alerts',
        'alerts'
      ];

      requiredFields.forEach(field => {
        expect(status).toHaveProperty(field);
      });

      // Verify metrics structure
      const requiredMetrics = [
        'totalChecks',
        'issuesDetected',
        'autoFixesApplied',
        'criticalIssues',
        'warningIssues',
        'averageCheckDuration',
        'uptime_minutes'
      ];

      requiredMetrics.forEach(metric => {
        expect(status.metrics).toHaveProperty(metric);
      });
    });
  });
});