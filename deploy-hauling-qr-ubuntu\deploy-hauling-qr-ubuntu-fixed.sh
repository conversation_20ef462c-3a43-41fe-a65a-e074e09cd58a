#!/bin/bash
#
# Hauling QR Trip Management System Deployment Script for Ubuntu
# Version: 2.0.1 - Fixed and Simplified
#
# This script automates the deployment of the Hauling QR Trip Management System
# on Ubuntu systems with universal compatibility.
#
# Usage: ./deploy-hauling-qr-ubuntu-fixed.sh [options]
#

# Script version
VERSION="2.0.1"

# Record start time for metrics
DEPLOYMENT_START_TIME=$(date +%s)

# Environment detection flags
INIT_SYSTEM=""
IS_DOCKER=false
IS_WSL=false
IS_SYSTEMD=false
HAS_SYSTEMCTL=false

# Default configuration values
CONFIG_FILE=""
DOMAIN_NAME="truckhaul.top"
SSL_MODE="cloudflare"
APP_PORT="5000"
DB_PASSWORD="PostgreSQLPassword"
ADMIN_USERNAME="admin"
ADMIN_PASSWORD="admin12345"
ADMIN_EMAIL="<EMAIL>"
REPO_URL="https://<EMAIL>/mightybadz18/hauling-qr-trip-management.git"
REPO_BRANCH="main"
ENV_MODE="production"
INTERACTIVE=true
VERBOSE=true
DRY_RUN=false
MONITORING_ENABLED=true
BACKUP_ENABLED=true
BACKUP_RETENTION_DAYS=7
JWT_SECRET="hauling_qr_jwt_secret_2025_secure_key_for_production"

# Logging setup
LOG_DIR="/var/log/hauling-deployment"
LOG_FILE="$LOG_DIR/deployment.log"
LOG_JSON_FILE="$LOG_DIR/deployment.json"
CONSOLE_OUTPUT=true

# Log levels
LOG_LEVEL_DEBUG=0
LOG_LEVEL_INFO=1
LOG_LEVEL_WARNING=2
LOG_LEVEL_ERROR=3
LOG_LEVEL_SUCCESS=4
CURRENT_LOG_LEVEL=$LOG_LEVEL_INFO

# Create log directory if it doesn't exist
mkdir -p "$LOG_DIR"

# Initialize JSON log file if it doesn't exist
if [[ ! -f "$LOG_JSON_FILE" ]]; then
    echo '{"logs":[]}' > "$LOG_JSON_FILE"
fi

# Current step tracking for error handling
current_step="initialization"

# ==============================
# Logging Functions
# ==============================

# Helper function to add log entry to JSON log file
_log_to_json() {
    local level="$1"
    local message="$2"
    local timestamp=$(date '+%Y-%m-%dT%H:%M:%S.%3N%z')
    local context="$current_step"
    local hostname=$(hostname)
    local pid=$$
    
    # Create JSON log entry
    local json_entry="{\"timestamp\":\"$timestamp\",\"level\":\"$level\",\"message\":\"${message//\"/\\\"}\",\"context\":\"$context\",\"hostname\":\"$hostname\",\"pid\":$pid}"
    
    # Only use jq if it's available, otherwise append simple JSON
    if command -v jq >/dev/null 2>&1; then
        # Append to JSON log file using temporary file to avoid race conditions
        local temp_file=$(mktemp)
        jq ".logs += [$json_entry]" "$LOG_JSON_FILE" > "$temp_file" && mv "$temp_file" "$LOG_JSON_FILE"
    else
        # Fallback: append to a simple log array (will be fixed when jq becomes available)
        echo "$json_entry" >> "${LOG_JSON_FILE}.pending"
    fi
}

log_debug() {
    local message="[DEBUG] $(date '+%Y-%m-%d %H:%M:%S') - $1"
    echo "$message" >> "$LOG_FILE"
    [[ "$CONSOLE_OUTPUT" == true && $CURRENT_LOG_LEVEL -le $LOG_LEVEL_DEBUG ]] && echo -e "\033[0;90m$message\033[0m"
    _log_to_json "debug" "$1"
}

log_info() {
    local message="[INFO] $(date '+%Y-%m-%d %H:%M:%S') - $1"
    echo "$message" >> "$LOG_FILE"
    [[ "$CONSOLE_OUTPUT" == true && $CURRENT_LOG_LEVEL -le $LOG_LEVEL_INFO ]] && echo -e "\033[0;34m$message\033[0m"
    _log_to_json "info" "$1"
}

log_warning() {
    local message="[WARNING] $(date '+%Y-%m-%d %H:%M:%S') - $1"
    echo "$message" >> "$LOG_FILE"
    [[ "$CONSOLE_OUTPUT" == true && $CURRENT_LOG_LEVEL -le $LOG_LEVEL_WARNING ]] && echo -e "\033[0;33m$message\033[0m"
    _log_to_json "warning" "$1"
}

log_error() {
    local message="[ERROR] $(date '+%Y-%m-%d %H:%M:%S') - $1"
    echo "$message" >> "$LOG_FILE"
    [[ "$CONSOLE_OUTPUT" == true && $CURRENT_LOG_LEVEL -le $LOG_LEVEL_ERROR ]] && echo -e "\033[0;31m$message\033[0m"
    _log_to_json "error" "$1"
}

log_success() {
    local message="[SUCCESS] $(date '+%Y-%m-%d %H:%M:%S') - $1"
    echo "$message" >> "$LOG_FILE"
    [[ "$CONSOLE_OUTPUT" == true && $CURRENT_LOG_LEVEL -le $LOG_LEVEL_SUCCESS ]] && echo -e "\033[0;32m$message\033[0m"
    _log_to_json "success" "$1"
}

log_section() {
    local message="=== $1 ==="
    echo -e "\n$message" >> "$LOG_FILE"
    [[ "$CONSOLE_OUTPUT" == true ]] && echo -e "\n\033[1;36m$message\033[0m"
    _log_to_json "section" "$1"
}

log_step() {
    current_step=$(echo "$1" | tr ' ' '_' | tr '[:upper:]' '[:lower:]')
    local message="--- $1 ---"
    echo "$message" >> "$LOG_FILE"
    [[ "$CONSOLE_OUTPUT" == true ]] && echo -e "\033[0;36m$message\033[0m"
    _log_to_json "step" "$1"
}

# ==============================
# Environment Detection
# ==============================

# Function to detect the environment and init system
detect_environment() {
    log_debug "Detecting environment and init system"

    # Detect Docker environment
    if [[ -f /.dockerenv ]] || grep -q 'docker\|lxc' /proc/1/cgroup 2>/dev/null; then
        IS_DOCKER=true
        log_info "Docker environment detected"
    fi

    # Detect WSL environment
    if grep -qEi "(Microsoft|WSL)" /proc/version 2>/dev/null; then
        IS_WSL=true
        log_info "WSL environment detected"
    fi

    # Detect systemd
    if [[ -d /run/systemd/system ]] && command -v systemctl &> /dev/null; then
        IS_SYSTEMD=true
        HAS_SYSTEMCTL=true
        INIT_SYSTEM="systemd"
        log_info "Systemd init system detected"
    elif command -v systemctl &> /dev/null; then
        HAS_SYSTEMCTL=true
        INIT_SYSTEM="systemd-fallback"
        log_warning "systemctl available but systemd may not be running as PID 1"
    elif command -v service &> /dev/null; then
        INIT_SYSTEM="sysv"
        log_info "SysV init system detected"
    else
        INIT_SYSTEM="none"
        log_warning "No recognized init system detected - using process-based management"
    fi

    log_info "Environment summary: Docker=$IS_DOCKER, WSL=$IS_WSL, Init=$INIT_SYSTEM"
}

# ==============================
# Essential Tools Installation
# ==============================

# Function to install essential tools required by the script
install_essential_tools() {
    log_step "Installing essential tools"
    current_step="install_essential_tools"
    
    # Update package lists first
    if apt-get update >/dev/null 2>&1; then
        log_debug "Package lists updated successfully"
    else
        log_warning "Failed to update package lists, continuing anyway"
    fi
    
    # Install essential tools
    local essential_tools=("curl" "git" "jq" "bc" "wget")
    local installed_tools=()
    local failed_tools=()
    
    for tool in "${essential_tools[@]}"; do
        if ! command -v "$tool" >/dev/null 2>&1; then
            log_info "Installing $tool..."
            if apt-get install -y "$tool" >/dev/null 2>&1; then
                installed_tools+=("$tool")
                log_debug "$tool installed successfully"
            else
                failed_tools+=("$tool")
                log_warning "Failed to install $tool"
            fi
        else
            log_debug "$tool is already installed"
        fi
    done
    
    # Fix pending JSON logs if jq is now available
    if command -v jq >/dev/null 2>&1 && [[ -f "${LOG_JSON_FILE}.pending" ]]; then
        log_debug "Processing pending JSON log entries"
        
        # Initialize JSON log if it doesn't exist
        if [[ ! -f "$LOG_JSON_FILE" ]]; then
            echo '{"logs":[]}' > "$LOG_JSON_FILE"
        fi
        
        # Process pending entries
        while IFS= read -r json_entry; do
            if [[ -n "$json_entry" ]]; then
                local temp_file=$(mktemp)
                jq ".logs += [$json_entry]" "$LOG_JSON_FILE" > "$temp_file" && mv "$temp_file" "$LOG_JSON_FILE"
            fi
        done < "${LOG_JSON_FILE}.pending"
        
        # Remove pending file
        rm -f "${LOG_JSON_FILE}.pending"
    fi
    
    if [[ ${#installed_tools[@]} -gt 0 ]]; then
        log_success "Essential tools installed: ${installed_tools[*]}"
    fi
    
    if [[ ${#failed_tools[@]} -gt 0 ]]; then
        log_warning "Failed to install some tools: ${failed_tools[*]}"
        log_warning "Some features may not work correctly"
    fi
    
    return 0
}

# ==============================
# Network Connectivity Check
# ==============================

# Function to check network connectivity with multiple methods
check_network_connectivity() {
    local test_hosts=("*******" "*******" "google.com" "github.com")
    local connectivity_ok=false

    log_debug "Checking network connectivity"

    # Test with ping (skip in Docker/container environments where ping might be restricted)
    if [[ "$IS_DOCKER" != true ]]; then
        for host in "${test_hosts[@]}"; do
            if command -v ping >/dev/null 2>&1 && ping -c 1 -W 5 "$host" >/dev/null 2>&1; then
                log_debug "Network connectivity confirmed via ping to $host"
                connectivity_ok=true
                break
            fi
        done
    fi

    # Test with curl if available
    if [[ "$connectivity_ok" == false ]] && command -v curl >/dev/null 2>&1; then
        for host in "http://google.com" "http://github.com"; do
            if curl -s --connect-timeout 10 --max-time 15 "$host" >/dev/null 2>&1; then
                log_debug "Network connectivity confirmed via curl to $host"
                connectivity_ok=true
                break
            fi
        done
    fi

    # Test with wget if curl failed and wget is available
    if [[ "$connectivity_ok" == false ]] && command -v wget >/dev/null 2>&1; then
        for host in "http://google.com" "http://github.com"; do
            if wget --timeout=10 --tries=1 -q --spider "$host" 2>/dev/null; then
                log_debug "Network connectivity confirmed via wget to $host"
                connectivity_ok=true
                break
            fi
        done
    fi

    # In Docker/container environments, be more lenient
    if [[ "$connectivity_ok" == false && "$IS_DOCKER" == true ]]; then
        log_warning "Network connectivity check failed, but continuing in Docker environment"
        log_warning "Network access may be limited in container environments"
        return 0  # Don't fail deployment in Docker
    fi

    if [[ "$connectivity_ok" == true ]]; then
        log_debug "Network connectivity check passed"
        return 0
    else
        log_warning "Network connectivity check failed"
        return 1
    fi
}

# ==============================
# Prerequisites Validation
# ==============================

# Function to validate prerequisites with detailed error messages
validate_prerequisites() {
    log_step "Validating deployment prerequisites"
    current_step="validate_prerequisites"

    local validation_errors=0
    local validation_warnings=0

    # Check if running as root
    if [[ $EUID -ne 0 ]]; then
        log_error "This script must be run as root or with sudo privileges"
        ((validation_errors++))
    fi

    # Check Ubuntu version
    if [[ -f /etc/os-release ]]; then
        local ubuntu_version=$(grep VERSION_ID /etc/os-release | cut -d'"' -f2)
        local major_version=$(echo "$ubuntu_version" | cut -d'.' -f1)

        if [[ $major_version -lt 18 ]]; then
            log_error "Ubuntu version $ubuntu_version is not supported. Minimum required: 18.04"
            ((validation_errors++))
        elif [[ $major_version -eq 18 || $major_version -eq 20 || $major_version -eq 22 || $major_version -eq 24 ]]; then
            log_info "Ubuntu version $ubuntu_version is supported"
        else
            log_warning "Ubuntu version $ubuntu_version is not tested. Supported versions: 18.04, 20.04, 22.04, 24.04"
            ((validation_warnings++))
        fi
    else
        log_warning "Cannot determine Ubuntu version"
        ((validation_warnings++))
    fi

    # Check network connectivity (non-fatal in Docker environments)
    if ! check_network_connectivity; then
        if [[ "$IS_DOCKER" == true ]]; then
            log_warning "Network connectivity check failed in Docker environment - this may be normal"
            ((validation_warnings++))
        else
            log_error "Network connectivity is required for deployment"
            ((validation_errors++))
        fi
    fi

    # Check apt-get availability (essential)
    if ! command -v "apt-get" >/dev/null 2>&1; then
        log_error "Required command not found: apt-get"
        ((validation_errors++))
    fi

    # Check other commands (will be installed if missing)
    local installable_commands=("curl" "git" "wget" "bc" "jq")
    local missing_commands=()
    for cmd in "${installable_commands[@]}"; do
        if ! command -v "$cmd" >/dev/null 2>&1; then
            missing_commands+=("$cmd")
            log_info "Command not found: $cmd (will be installed)"
        fi
    done

    if [[ ${#missing_commands[@]} -gt 0 ]]; then
        log_info "Missing commands will be installed: ${missing_commands[*]}"
    fi

    # Check system resources
    local min_memory_mb=512  # Reduced for Docker environments
    local available_memory=$(free -m | awk 'NR==2{print $7}' 2>/dev/null || echo "1024")
    if [[ $available_memory -lt $min_memory_mb ]]; then
        log_warning "Available memory (${available_memory}MB) is below recommended minimum (${min_memory_mb}MB)"
        ((validation_warnings++))
    fi

    local min_disk_gb=2  # Reduced for Docker environments
    local available_disk=$(df / | awk 'NR==2 {print int($4/1024/1024)}' 2>/dev/null || echo "10")
    if [[ $available_disk -lt $min_disk_gb ]]; then
        log_warning "Available disk space (${available_disk}GB) is below minimum requirement (${min_disk_gb}GB)"
        ((validation_warnings++))
    fi

    # Summary
    if [[ $validation_errors -gt 0 ]]; then
        log_error "Prerequisites validation failed: $validation_errors errors, $validation_warnings warnings"
        log_error "Critical errors must be fixed before proceeding"
        return 1
    elif [[ $validation_warnings -gt 0 ]]; then
        log_warning "Prerequisites validation completed with warnings: $validation_warnings warnings"
        log_info "Deployment will continue and attempt to resolve missing dependencies"
        return 0
    else
        log_success "Prerequisites validation passed"
        return 0
    fi
}

# ==============================
# Help Function
# ==============================

show_help() {
    cat << EOF
Hauling QR Trip Management System Deployment Script v$VERSION

Usage: $0 [options]

Options:
  -c, --config FILE       Path to configuration file
  -d, --domain DOMAIN     Domain name for the application
  -e, --env MODE          Environment mode (production, staging, development)
  -n, --non-interactive   Run in non-interactive mode (requires config file)
  -q, --quiet             Minimal output
  --dry-run               Validate configuration without making changes
  --verify-database       Check database schema and tables
  --log-level LEVEL       Set log level (debug, info, warning, error)
  --json-output           Output logs in JSON format for machine parsing
  -h, --help              Show this help message

Examples:
  $0 --domain example.com --env production
  $0 --config deployment-config.conf
  $0 --non-interactive --log-level debug --json-output

For more information, visit: https://github.com/your-org/hauling-qr-trip-system
EOF
}

# ==============================
# Progress and Output Functions
# ==============================

# Function to show progress (placeholder for CI/CD compatibility)
show_progress() {
    local message="$1"
    local current="$2"
    local total="$3"
    
    if [[ "$CONSOLE_OUTPUT" == true ]]; then
        echo -e "\033[0;36m[$current/$total] $message\033[0m"
    fi
    log_info "Progress [$current/$total]: $message"
}

# Function to output deployment progress (placeholder for CI/CD compatibility)
output_deployment_progress() {
    local phase="$1"
    local status="$2"
    local message="$3"
    local progress="$4"
    
    log_info "Deployment Progress: $phase - $status - $message ($progress%)"
}

# ==============================
# Repository Management
# ==============================

# Function to clone repository with authentication handling
clone_repository_safe() {
    local repo_url="$1"
    local target_dir="$2"
    local branch="${3:-main}"
    
    log_step "Cloning repository"
    current_step="clone_repository"
    
    log_info "Repository: $repo_url"
    log_info "Target directory: $target_dir"
    log_info "Branch: $branch"
    
    # Remove existing directory if it exists
    if [[ -d "$target_dir" ]]; then
        log_warning "Target directory exists, removing: $target_dir"
        rm -rf "$target_dir"
    fi
    
    # Create parent directory
    local parent_dir=$(dirname "$target_dir")
    mkdir -p "$parent_dir"
    
    # Try to clone the repository
    log_info "Attempting to clone repository..."
    
    # First, try without authentication (for public repos)
    if git clone --branch "$branch" --depth 1 "$repo_url" "$target_dir" 2>/dev/null; then
        log_success "Repository cloned successfully"
        log_info "Repository location: $target_dir"
        log_info "Repository contents:"
        ls -la "$target_dir" | head -10
        return 0
    fi
    
    # If that fails, provide helpful error message
    log_error "Repository clone failed"
    log_error "This could be due to:"
    log_error "1. Repository is private and requires authentication"
    log_error "2. Network connectivity issues"
    log_error "3. Invalid repository URL or branch"
    log_error ""
    log_error "For private repositories, you need to:"
    log_error "1. Use a Personal Access Token instead of password"
    log_error "2. Or use SSH keys for authentication"
    log_error "3. Or make the repository public"
    log_error ""
    log_error "GitHub no longer supports password authentication as of August 2021"
    log_error "See: https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/creating-a-personal-access-token"
    
    # For now, create a basic directory structure as fallback
    log_warning "Creating basic directory structure as fallback..."
    create_fallback_structure "$target_dir"
    
    return 1
}

# Function to create fallback directory structure when repository clone fails
create_fallback_structure() {
    local target_dir="$1"
    
    log_info "Creating fallback directory structure..."
    
    # Create basic directory structure
    mkdir -p "$target_dir"/{client,server,database}
    mkdir -p "$target_dir"/server/{routes,services,middleware,config,utils,logs}
    mkdir -p "$target_dir"/client/{src,public,build}
    mkdir -p "$target_dir"/database/{migrations}
    
    # Create basic package.json files
    cat > "$target_dir/package.json" << 'EOF'
{
  "name": "hauling-qr-trip-system",
  "version": "1.0.0",
  "description": "Hauling QR Trip Management System",
  "scripts": {
    "dev": "concurrently \"npm run server:dev\" \"npm run client:dev\"",
    "server:dev": "cd server && npm run dev",
    "client:dev": "cd client && npm start",
    "build": "cd client && npm run build",
    "start": "cd server && npm start"
  },
  "dependencies": {},
  "devDependencies": {
    "concurrently": "^7.6.0"
  }
}
EOF
    
    cat > "$target_dir/server/package.json" << 'EOF'
{
  "name": "hauling-qr-server",
  "version": "1.0.0",
  "description": "Hauling QR Trip Management System - Server",
  "main": "server.js",
  "scripts": {
    "start": "node server.js",
    "dev": "nodemon server.js"
  },
  "dependencies": {
    "express": "^4.18.2",
    "cors": "^2.8.5",
    "helmet": "^6.0.1",
    "pg": "^8.8.0",
    "bcryptjs": "^2.4.3",
    "jsonwebtoken": "^9.0.0",
    "dotenv": "^16.0.3"
  },
  "devDependencies": {
    "nodemon": "^2.0.20"
  }
}
EOF
    
    cat > "$target_dir/client/package.json" << 'EOF'
{
  "name": "hauling-qr-client",
  "version": "1.0.0",
  "description": "Hauling QR Trip Management System - Client",
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-scripts": "5.0.1",
    "axios": "^1.2.0"
  },
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build",
    "test": "react-scripts test",
    "eject": "react-scripts eject"
  },
  "browserslist": {
    "production": [
      ">0.2%",
      "not dead",
      "not op_mini all"
    ],
    "development": [
      "last 1 chrome version",
      "last 1 firefox version",
      "last 1 safari version"
    ]
  }
}
EOF
    
    # Create basic server.js
    cat > "$target_dir/server/server.js" << 'EOF'
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(helmet());
app.use(cors());
app.use(express.json());

// Basic health check route
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    message: 'Hauling QR Trip Management System is running',
    timestamp: new Date().toISOString()
  });
});

// Basic route
app.get('/api', (req, res) => {
  res.json({ message: 'Hauling QR Trip Management System API' });
});

app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
EOF
    
    # Create basic database initialization script
    cat > "$target_dir/database/init.sql" << 'EOF'
-- Hauling QR Trip Management System Database Schema
-- Basic initialization script

-- Create database (this should be run as postgres user)
-- CREATE DATABASE hauling_qr_system;

-- Connect to the database
\c hauling_qr_system;

-- Create basic tables
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(20) DEFAULT 'user',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS trips (
    id SERIAL PRIMARY KEY,
    trip_number VARCHAR(50) UNIQUE NOT NULL,
    driver_id INTEGER REFERENCES users(id),
    status VARCHAR(20) DEFAULT 'pending',
    start_location VARCHAR(255),
    end_location VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert default admin user (password: admin123)
INSERT INTO users (username, email, password_hash, role) 
VALUES ('admin', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin')
ON CONFLICT (username) DO NOTHING;

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_trips_status ON trips(status);
CREATE INDEX IF NOT EXISTS idx_trips_driver ON trips(driver_id);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
EOF
    
    # Create basic migration runner
    cat > "$target_dir/database/run-migration.js" << 'EOF'
const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  user: process.env.DB_USER || 'hauling_app',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'hauling_qr_system',
  password: process.env.DB_PASSWORD,
  port: process.env.DB_PORT || 5432,
});

async function runMigrations() {
  try {
    console.log('Running database migrations...');
    
    // This is a placeholder - in a real system, you'd read migration files
    console.log('No migrations to run - database should be initialized with init.sql');
    console.log('Migrations completed successfully');
    
    process.exit(0);
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

runMigrations();
EOF
    
    # Create admin user creation script
    cat > "$target_dir/server/create-admin-user.js" << 'EOF'
const { Pool } = require('pg');
const bcrypt = require('bcryptjs');
require('dotenv').config();

const pool = new Pool({
  user: process.env.DB_USER || 'hauling_app',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'hauling_qr_system',
  password: process.env.DB_PASSWORD,
  port: process.env.DB_PORT || 5432,
});

async function createAdminUser() {
  try {
    const username = process.env.ADMIN_USERNAME || 'admin';
    const email = process.env.ADMIN_EMAIL || '<EMAIL>';
    const password = process.env.ADMIN_PASSWORD || 'admin123';
    
    console.log(`Creating admin user: ${username}`);
    
    const hashedPassword = await bcrypt.hash(password, 10);
    
    const query = `
      INSERT INTO users (username, email, password_hash, role) 
      VALUES ($1, $2, $3, 'admin')
      ON CONFLICT (username) 
      DO UPDATE SET 
        email = EXCLUDED.email,
        password_hash = EXCLUDED.password_hash,
        updated_at = CURRENT_TIMESTAMP
    `;
    
    await pool.query(query, [username, email, hashedPassword]);
    console.log('Admin user created/updated successfully');
    
    process.exit(0);
  } catch (error) {
    console.error('Failed to create admin user:', error);
    process.exit(1);
  }
}

createAdminUser();
EOF
    
    # Create basic .env template
    cat > "$target_dir/.env.example" << 'EOF'
# Environment Configuration
NODE_ENV=production
PORT=5000

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=hauling_qr_system
DB_USER=hauling_app
DB_PASSWORD=your_db_password_here

# Admin Configuration
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your_admin_password_here
ADMIN_EMAIL=<EMAIL>

# JWT Configuration
JWT_SECRET=your_jwt_secret_here

# Frontend Configuration
REACT_APP_API_URL=https://yourdomain.com/api
REACT_APP_WS_URL=wss://yourdomain.com/ws
EOF
    
    log_success "Fallback directory structure created"
    log_info "Basic application files have been created as placeholders"
    log_warning "You should replace these with the actual repository files when authentication is resolved"
}

# ==============================
# NPM Installation Functions
# ==============================

# Function to install npm with multiple fallback methods
install_npm_robust() {
    log_step "Installing npm with robust error handling"
    
    # Method 1: Try standard apt installation
    log_info "Attempting npm installation via apt..."
    if apt-get install -y npm 2>/dev/null; then
        log_success "npm installed successfully via apt"
        return 0
    fi
    
    log_warning "Standard npm installation failed, trying alternative methods..."
    
    # Method 2: Fix broken packages and retry
    log_info "Fixing broken packages and retrying..."
    apt-get install -f -y 2>/dev/null
    dpkg --configure -a 2>/dev/null
    apt-get update 2>/dev/null
    
    if apt-get install -y npm 2>/dev/null; then
        log_success "npm installed successfully after fixing broken packages"
        return 0
    fi
    
    # Method 3: Install npm via NodeSource repository
    log_info "Attempting npm installation via NodeSource..."
    if install_npm_nodesource; then
        return 0
    fi
    
    # Method 4: Install npm via snap
    log_info "Attempting npm installation via snap..."
    if command -v snap >/dev/null 2>&1; then
        if snap install node --classic 2>/dev/null; then
            log_success "npm installed successfully via snap"
            return 0
        fi
    fi
    
    # Method 5: Install npm manually via curl
    log_info "Attempting manual npm installation..."
    if install_npm_manual; then
        return 0
    fi
    
    # Method 6: Use Node.js built-in npm (if available)
    log_info "Checking if npm is bundled with Node.js..."
    if command -v node >/dev/null 2>&1; then
        local node_path=$(which node)
        local node_dir=$(dirname "$node_path")
        if [[ -f "$node_dir/npm" ]]; then
            log_success "npm found bundled with Node.js"
            return 0
        fi
    fi
    
    log_error "All npm installation methods failed"
    return 1
}

# Method 3: Install npm via NodeSource
install_npm_nodesource() {
    log_info "Installing npm via NodeSource repository..."
    
    # NodeSource usually includes npm with Node.js
    if command -v npm >/dev/null 2>&1; then
        log_success "npm is already available via NodeSource Node.js installation"
        return 0
    fi
    
    return 1
}

# Method 5: Manual npm installation
install_npm_manual() {
    log_info "Installing npm manually via curl..."
    
    # Download and install npm manually
    local npm_install_script="/tmp/npm-install.sh"
    
    if curl -fsSL https://www.npmjs.com/install.sh -o "$npm_install_script" 2>/dev/null; then
        if bash "$npm_install_script" 2>/dev/null; then
            log_success "npm installed manually"
            rm -f "$npm_install_script"
            return 0
        fi
        rm -f "$npm_install_script"
    fi
    
    # Alternative: Install npm via Node.js if available
    if command -v node >/dev/null 2>&1; then
        log_info "Attempting to install npm via Node.js..."
        
        # Create a simple npm installation using Node.js
        local npm_dir="/usr/local/lib/node_modules/npm"
        if mkdir -p "$npm_dir" 2>/dev/null; then
            if curl -fsSL https://registry.npmjs.org/npm/-/npm-latest.tgz | tar -xz -C "$npm_dir" --strip-components=1 2>/dev/null; then
                # Create npm symlink
                ln -sf "$npm_dir/bin/npm-cli.js" /usr/local/bin/npm 2>/dev/null
                chmod +x /usr/local/bin/npm 2>/dev/null
                
                if command -v npm >/dev/null 2>&1; then
                    log_success "npm installed manually via Node.js"
                    return 0
                fi
            fi
        fi
    fi
    
    return 1
}

# Function to fix npm dependencies issues
fix_npm_dependencies() {
    log_step "Attempting additional npm dependency fixes"
    
    # Method 1: Fix broken packages more aggressively
    log_info "Fixing broken packages aggressively..."
    apt-get clean
    apt-get autoclean
    apt-get autoremove -y
    dpkg --configure -a
    apt-get install -f -y
    
    # Try npm installation again
    if apt-get install -y npm 2>/dev/null; then
        log_success "npm installed after aggressive package fixing"
        return 0
    fi
    
    # Method 2: Remove conflicting packages and reinstall
    log_info "Removing conflicting packages..."
    apt-get remove -y nodejs npm 2>/dev/null
    apt-get autoremove -y
    
    # Install nodejs from different source
    if curl -fsSL https://deb.nodesource.com/setup_18.x | bash - 2>/dev/null; then
        if apt-get install -y nodejs 2>/dev/null; then
            log_success "nodejs installed via NodeSource"
            
            # npm should be included
            if command -v npm >/dev/null 2>&1; then
                log_success "npm is now available"
                return 0
            fi
        fi
    fi
    
    # Method 3: Install npm via alternative method
    log_info "Installing npm via alternative method..."
    if command -v node >/dev/null 2>&1; then
        # Use Node.js to install npm
        if curl -L https://www.npmjs.com/install.sh | sh 2>/dev/null; then
            log_success "npm installed via install script"
            return 0
        fi
    fi
    
    return 1
}

# Function to verify npm installation
verify_npm_installation() {
    log_step "Verifying npm installation"
    
    if command -v npm >/dev/null 2>&1; then
        local npm_version=$(npm --version 2>/dev/null || echo "unknown")
        log_success "npm is available (version: $npm_version)"
        
        # Test npm functionality
        if npm list -g --depth=0 >/dev/null 2>&1; then
            log_success "npm is functioning correctly"
            return 0
        else
            log_warning "npm is installed but may have issues"
            return 1
        fi
    else
        log_error "npm is not available after installation attempts"
        return 1
    fi
}

# ==============================
# PM2 Setup Functions
# ==============================

# Function to install and configure PM2
setup_pm2() {
    log_step "Installing and configuring PM2"
    current_step="setup_pm2"
    
    # Check if PM2 is already installed
    if command -v pm2 >/dev/null 2>&1; then
        local pm2_version=$(pm2 --version 2>/dev/null || echo "unknown")
        log_info "PM2 is already installed (version: $pm2_version)"
    else
        # Install PM2 globally with multiple attempts
        log_info "Installing PM2 globally..."
        local install_attempts=3
        local attempt=1
        
        while [[ $attempt -le $install_attempts ]]; do
            log_info "PM2 installation attempt $attempt/$install_attempts..."
            
            if npm install -g pm2; then
                log_success "PM2 installed successfully"
                break
            else
                log_warning "PM2 installation attempt $attempt failed"
                if [[ $attempt -eq $install_attempts ]]; then
                    log_error "Failed to install PM2 after $install_attempts attempts"
                    return 1
                fi
                sleep 2
                ((attempt++))
            fi
        done
    fi
    
    # Verify PM2 installation
    if command -v pm2 >/dev/null 2>&1; then
        local pm2_version=$(pm2 --version 2>/dev/null || echo "unknown")
        log_success "PM2 is available (version: $pm2_version)"
        
        # Test PM2 functionality
        if pm2 ping >/dev/null 2>&1; then
            log_success "PM2 daemon is responsive"
        else
            log_info "Starting PM2 daemon..."
            pm2 ping >/dev/null 2>&1 || log_warning "PM2 daemon may have issues"
        fi
    else
        log_error "PM2 installation verification failed"
        return 1
    fi
    
    # Setup PM2 startup script (handle different environments)
    log_info "Setting up PM2 startup script..."
    if [[ "$IS_SYSTEMD" == true ]]; then
        if pm2 startup systemd -u root --hp /root 2>/dev/null; then
            log_success "PM2 startup script configured for systemd"
        else
            log_warning "PM2 startup script setup failed for systemd"
        fi
    else
        log_info "Non-systemd environment detected, skipping startup script setup"
    fi
    
    log_success "PM2 setup completed"
    return 0
}

# Function to create PM2 ecosystem configuration
create_pm2_ecosystem() {
    log_step "Creating PM2 ecosystem configuration"
    current_step="create_pm2_ecosystem"
    
    local app_dir="/var/www/hauling-qr-system"
    
    # Get port from environment or default
    local app_port=\${PORT:-5000}
    
    # Create PM2 ecosystem file
    log_info "Creating PM2 ecosystem configuration..."
    cat > "$app_dir/ecosystem.config.js" << EOF
module.exports = {
  apps: [
    {
      name: 'hauling-qr-server',
      script: './server/server.js',
      cwd: '$app_dir',
      instances: 1,
      exec_mode: 'fork',
      // Load environment variables from .env file
      env_file: './.env',
      env: {
        NODE_ENV: '$ENV_MODE'
      },
      env_production: {
        NODE_ENV: 'production'
      },
      log_file: '$app_dir/server/logs/combined.log',
      out_file: '$app_dir/server/logs/out.log',
      error_file: '$app_dir/server/logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      max_memory_restart: '1G',
      restart_delay: 4000,
      max_restarts: 10,
      min_uptime: '10s',
      kill_timeout: 5000,
      wait_ready: true,
      listen_timeout: 8000
    }
  ],
  
  // Deployment configuration for easy updates
  deploy: {
    production: {
      user: 'www-data',
      host: 'localhost',
      ref: 'origin/main',
      repo: '$REPO_URL',
      path: '$app_dir',
      'post-deploy': 'cd server && npm install --production && cd ../client && npm install && npm run build && pm2 reload ecosystem.config.js --env production'
    }
  }
};
EOF
    
    # Set proper permissions
    chown www-data:www-data "$app_dir/ecosystem.config.js"
    chmod 644 "$app_dir/ecosystem.config.js"
    
    log_success "PM2 ecosystem configuration created"
    return 0
}

# Function to setup package.json files if needed
setup_package_json_files() {
    log_step "Setting up package.json files"
    local app_dir="/var/www/hauling-qr-system"
    
    # Setup server package.json if missing or incomplete
    if [[ ! -f "$app_dir/server/package.json" ]]; then
        log_info "Creating server package.json..."
        cat > "$app_dir/server/package.json" << 'EOF'
{
  "name": "hauling-qr-server",
  "version": "1.0.0",
  "description": "Hauling QR Trip Management System - Server",
  "main": "server.js",
  "scripts": {
    "start": "node server.js",
    "dev": "nodemon server.js",
    "test": "echo \"Error: no test specified\" && exit 1"
  },
  "dependencies": {
    "express": "^4.18.2",
    "cors": "^2.8.5",
    "helmet": "^6.0.1",
    "pg": "^8.8.0",
    "bcryptjs": "^2.4.3",
    "jsonwebtoken": "^9.0.0",
    "dotenv": "^16.0.3",
    "ws": "^8.13.0"
  },
  "devDependencies": {
    "nodemon": "^2.0.20"
  },
  "keywords": ["hauling", "qr", "trip", "management"],
  "author": "",
  "license": "ISC"
}
EOF
        log_success "Server package.json created"
    fi
    
    # Setup client package.json if missing or incomplete
    if [[ ! -f "$app_dir/client/package.json" ]]; then
        log_info "Creating client package.json..."
        cat > "$app_dir/client/package.json" << 'EOF'
{
  "name": "hauling-qr-client",
  "version": "1.0.0",
  "description": "Hauling QR Trip Management System - Client",
  "private": true,
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-scripts": "5.0.1",
    "axios": "^1.2.0",
    "react-router-dom": "^6.8.0",
    "@testing-library/jest-dom": "^5.16.4",
    "@testing-library/react": "^13.4.0",
    "@testing-library/user-event": "^13.5.0",
    "web-vitals": "^2.1.4"
  },
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build",
    "test": "react-scripts test",
    "eject": "react-scripts eject"
  },
  "eslintConfig": {
    "extends": [
      "react-app",
      "react-app/jest"
    ]
  },
  "browserslist": {
    "production": [
      ">0.2%",
      "not dead",
      "not op_mini all"
    ],
    "development": [
      "last 1 chrome version",
      "last 1 firefox version",
      "last 1 safari version"
    ]
  },
  "proxy": "http://localhost:5000"
}
EOF
        log_success "Client package.json created"
    fi
    
    # Create basic React app structure if missing
    if [[ ! -f "$app_dir/client/src/index.js" ]]; then
        log_info "Creating basic React app structure..."
        mkdir -p "$app_dir/client/src"
        mkdir -p "$app_dir/client/public"
        
        # Create basic index.html
        cat > "$app_dir/client/public/index.html" << 'EOF'
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="Hauling QR Trip Management System" />
    <title>Hauling QR Trip Management</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
  </body>
</html>
EOF
        
        # Create basic index.js
        cat > "$app_dir/client/src/index.js" << 'EOF'
import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);
EOF
        
        # Create basic App.js
        cat > "$app_dir/client/src/App.js" << 'EOF'
import React from 'react';

function App() {
  return (
    <div style={{ padding: '20px', textAlign: 'center' }}>
      <h1>Hauling QR Trip Management System</h1>
      <p>Welcome to the Hauling QR Trip Management System!</p>
      <p>The application is successfully deployed and running.</p>
      <div style={{ marginTop: '20px' }}>
        <a href="/api/health" target="_blank" rel="noopener noreferrer">
          Check API Health
        </a>
      </div>
    </div>
  );
}

export default App;
EOF
        
        log_success "Basic React app structure created"
    fi
    
    # Set proper permissions
    chown -R www-data:www-data "$app_dir/server/package.json" "$app_dir/client/package.json"
    chown -R www-data:www-data "$app_dir/client/src" "$app_dir/client/public" 2>/dev/null || true
    
    log_success "Package.json files setup completed"
}

# Function to start applications with PM2
start_applications_with_pm2() {
    log_step "Starting applications with PM2"
    current_step="start_applications_pm2"
    
    local app_dir="/var/www/hauling-qr-system"
    
    # Change to application directory
    cd "$app_dir" || {
        log_error "Failed to change to application directory"
        return 1
    }
    
    # Verify and setup directory structure
    log_info "Verifying application directory structure..."
    if [[ ! -d "server" ]]; then
        log_error "Server directory not found at $app_dir/server"
        return 1
    fi
    
    if [[ ! -d "client" ]]; then
        log_error "Client directory not found at $app_dir/client"
        return 1
    fi
    
    # Ensure package.json files exist and are properly configured
    setup_package_json_files
    
    # Install root dependencies first (the project has a root package.json)
    log_info "Installing root dependencies..."
    if [[ -f "package.json" ]]; then
        log_info "Running: npm install --production in $(pwd)"
        if npm install --production; then
            log_success "Root dependencies installed successfully"
        else
            log_warning "Root dependencies installation had issues, continuing..."
        fi
    fi
    
    # Install server dependencies
    log_info "Installing server dependencies..."
    if [[ -f "server/package.json" ]]; then
        cd server || {
            log_error "Failed to change to server directory"
            return 1
        }
        
        log_info "Running: npm install --production in $(pwd)"
        if npm install --production; then
            log_success "Server dependencies installed successfully"
        else
            log_error "Server dependencies installation failed"
            cd ..
            return 1
        fi
        cd ..
    else
        log_warning "Server package.json not found, using root dependencies"
    fi
    
    # Install and build client
    log_info "Installing client dependencies and building..."
    if [[ -f "client/package.json" ]]; then
        cd client || {
            log_error "Failed to change to client directory"
            return 1
        }
        
        log_info "Running: npm install in $(pwd)"
        if npm install; then
            log_success "Client dependencies installed successfully"
            
            log_info "Running: npm run build in $(pwd)"
            # Set environment variables for production build
            export GENERATE_SOURCEMAP=false
            export DISABLE_ESLINT_PLUGIN=true
            
            if npm run build; then
                log_success "Client built successfully"
                
                # Verify build directory was created
                if [[ -d "build" ]]; then
                    log_success "Client build directory created"
                    log_info "Build directory contents: $(ls -la build/ | head -5)"
                else
                    log_error "Client build directory not found after build"
                    cd ..
                    return 1
                fi
            else
                log_error "Client build failed"
                cd ..
                return 1
            fi
        else
            log_error "Client dependencies installation failed"
            cd ..
            return 1
        fi
        cd ..
    else
        log_error "Client package.json not found"
        return 1
    fi
    
    # Stop any existing PM2 processes first
    log_info "Stopping any existing PM2 processes..."
    pm2 stop all 2>/dev/null || true
    pm2 delete all 2>/dev/null || true
    
    # Start applications with PM2 (with fallback methods)
    log_info "Starting server with PM2..."
    
    # Method 1: Try with ecosystem config
    if [[ -f "ecosystem.config.js" ]] && pm2 start ecosystem.config.js --env production 2>/dev/null; then
        log_success "Server started with PM2 using ecosystem config"
    else
        log_warning "Ecosystem config failed, trying manual PM2 start..."
        
        # Method 2: Manual PM2 start
        if pm2 start server/server.js --name hauling-qr-server 2>/dev/null; then
            log_success "Server started with PM2 manually"
        else
            log_error "Failed to start server with PM2"
            log_info "Attempting to diagnose PM2 issues..."
            
            # Diagnostic information
            log_info "PM2 status: $(pm2 ping 2>&1)"
            log_info "Node.js version: $(node --version 2>&1)"
            log_info "NPM version: $(npm --version 2>&1)"
            log_info "Server file exists: $(test -f server/server.js && echo 'Yes' || echo 'No')"
            
            # Try one more time with verbose output
            log_info "Attempting final PM2 start with verbose output..."
            if pm2 start server/server.js --name hauling-qr-server --log /tmp/pm2-start.log 2>&1; then
                log_success "Server started with PM2 on final attempt"
            else
                log_error "All PM2 start attempts failed"
                log_info "PM2 log: $(cat /tmp/pm2-start.log 2>/dev/null || echo 'No log available')"
                return 1
            fi
        fi
    fi
    
    # Wait for application to start
    log_info "Waiting for application to start..."
    sleep 5
    
    # Verify PM2 process is running
    if pm2 list | grep -q "online"; then
        log_success "PM2 process is running"
    else
        log_warning "PM2 process may not be running properly"
        pm2 list || true
    fi
    
    # Save PM2 configuration
    log_info "Saving PM2 configuration..."
    if pm2 save 2>/dev/null; then
        log_success "PM2 configuration saved"
    else
        log_warning "PM2 configuration save failed"
    fi
    
    # Setup PM2 startup (if systemd available)
    if [[ "$IS_SYSTEMD" == true ]]; then
        log_info "Setting up PM2 startup..."
        pm2 startup systemd -u root --hp /root 2>/dev/null || log_warning "PM2 startup setup failed"
    fi
    
    # Show PM2 status
    log_info "PM2 application status:"
    pm2 list 2>/dev/null || log_warning "Could not display PM2 status"
    
    # Test if application is responding
    log_info "Testing application response..."
    sleep 2
    if curl -s http://localhost:5000/api/health >/dev/null 2>&1; then
        log_success "Application is responding to health checks"
    else
        log_warning "Application may not be responding yet (this is normal during startup)"
    fi
    
    log_success "Applications started with PM2"
    return 0
}

# Function to create management scripts for users
create_management_scripts() {
    log_step "Creating management scripts"
    current_step="create_management_scripts"
    
    local app_dir="/var/www/hauling-qr-system"
    
    # Create server management script
    log_info "Creating server management script..."
    cat > "$app_dir/manage-server.sh" << 'EOF'
#!/bin/bash
#
# Hauling QR Server Management Script
# Use this script to manage your deployed application
#

echo "🚛 Hauling QR Server Management"
echo "==============================="

case "$1" in
    start)
        echo "Starting services..."
        service postgresql start 2>/dev/null || echo "PostgreSQL may already be running"
        pm2 start hauling-qr-server 2>/dev/null || pm2 start server/server.js --name hauling-qr-server
        nginx 2>/dev/null || echo "Nginx may already be running"
        echo "✅ Services started"
        ;;
    stop)
        echo "Stopping services..."
        pm2 stop hauling-qr-server 2>/dev/null || echo "PM2 process may not be running"
        nginx -s quit 2>/dev/null || echo "Nginx may not be running"
        echo "✅ Services stopped"
        ;;
    restart)
        echo "Restarting services..."
        pm2 restart hauling-qr-server 2>/dev/null || echo "PM2 process may not be running"
        nginx -s reload 2>/dev/null || echo "Nginx may not be running"
        echo "✅ Services restarted"
        ;;
    status)
        echo "=== Service Status ==="
        echo "PM2 Processes:"
        if pm2 list 2>/dev/null | grep -q "online"; then
            pm2 list 2>/dev/null
            echo "✅ PM2 processes are running"
        else
            pm2 list 2>/dev/null || echo "❌ PM2 not available"
        fi
        echo
        
        echo "Database Status:"
        if service postgresql status 2>/dev/null | grep -q "online\|active"; then
            service postgresql status 2>/dev/null | head -3
            echo "✅ PostgreSQL is running"
        else
            echo "❌ PostgreSQL status unknown"
        fi
        echo
        
        echo "Nginx Status:"
        if ps aux | grep nginx | grep -v grep | grep -q "master"; then
            nginx_processes=$(ps aux | grep nginx | grep -v grep | wc -l)
            echo "✅ Nginx is running ($nginx_processes processes)"
            ps aux | grep nginx | grep -v grep | head -5
        else
            echo "❌ Nginx not running"
        fi
        echo
        
        echo "Listening Ports:"
        if command -v netstat >/dev/null 2>&1; then
            ports_output=$(netstat -tlnp 2>/dev/null | grep -E ':(80|443|5000)')
            if [[ -n "$ports_output" ]]; then
                echo "✅ Required ports are listening:"
                echo "$ports_output"
            else
                echo "❌ No required ports found listening"
            fi
        elif command -v ss >/dev/null 2>&1; then
            ports_output=$(ss -tlnp 2>/dev/null | grep -E ':(80|443|5000)')
            if [[ -n "$ports_output" ]]; then
                echo "✅ Required ports are listening:"
                echo "$ports_output"
            else
                echo "❌ No required ports found listening"
            fi
        else
            echo "⚠️ netstat/ss not available - installing net-tools..."
            apt-get update >/dev/null 2>&1 && apt-get install -y net-tools >/dev/null 2>&1
            if command -v netstat >/dev/null 2>&1; then
                ports_output=$(netstat -tlnp 2>/dev/null | grep -E ':(80|443|5000)')
                if [[ -n "$ports_output" ]]; then
                    echo "✅ Required ports are listening:"
                    echo "$ports_output"
                else
                    echo "❌ No required ports found listening"
                fi
            else
                echo "❌ Could not install netstat"
            fi
        fi
        
        echo
        echo "=== Overall Status ==="
        pm2_status="❌"
        db_status="❌"
        nginx_status="❌"
        ports_status="❌"
        
        # Check PM2
        if pm2 list 2>/dev/null | grep -q "online"; then
            pm2_status="✅"
        fi
        
        # Check Database
        if service postgresql status 2>/dev/null | grep -q "online\|active"; then
            db_status="✅"
        fi
        
        # Check Nginx
        if ps aux | grep nginx | grep -v grep | grep -q "master"; then
            nginx_status="✅"
        fi
        
        # Check Ports
        if (command -v netstat >/dev/null 2>&1 && netstat -tlnp 2>/dev/null | grep -E ':(80|443|5000)' >/dev/null) || \
           (command -v ss >/dev/null 2>&1 && ss -tlnp 2>/dev/null | grep -E ':(80|443|5000)' >/dev/null); then
            ports_status="✅"
        fi
        
        echo "$pm2_status Application Server (PM2)"
        echo "$db_status Database (PostgreSQL)"
        echo "$nginx_status Web Server (Nginx)"
        echo "$ports_status Network Ports (80, 443, 5000)"
        
        if [[ "$pm2_status" == "✅" && "$db_status" == "✅" && "$nginx_status" == "✅" && "$ports_status" == "✅" ]]; then
            echo
            echo "🎉 All services are running correctly!"
            echo "🌐 Your application should be accessible at:"
            echo "   • HTTP:  http://your-domain-or-ip"
            echo "   • HTTPS: https://your-domain-or-ip"
            echo "   • API:   http://localhost:5000/api/health"
        else
            echo
            echo "⚠️ Some services need attention. Check the details above."
        fi
        ;;
    logs)
        echo "=== Application Logs ==="
        pm2 logs hauling-qr-server --lines 20 2>/dev/null || echo "PM2 logs not available"
        ;;
    health)
        echo "=== Health Check ==="
        echo "API Health:"
        curl -s http://localhost:5000/api/health 2>/dev/null || echo "API not responding"
        echo
        echo "Database Health:"
        PGPASSWORD="${DB_PASSWORD:-PostgreSQLPassword}" psql -h "${DB_HOST:-localhost}" -p "${DB_PORT:-5432}" -U "${DB_USER:-hauling_app}" -d "${DB_NAME:-hauling_qr_system}" -c "SELECT 'Database OK' as status;" 2>/dev/null || echo "Database connection failed"
        ;;
    config)
        echo "=== Configuration Management ==="
        echo "Current .env configuration:"
        grep -v "^#" .env | grep -v "^$" | sort
        
        echo
        echo "Configuration Options:"
        echo "1. View current configuration"
        echo "2. Update configuration from deployment settings"
        echo "3. Edit configuration manually"
        echo "4. Back up configuration"
        echo "5. Exit"
        echo
        
        read -p "Enter your choice (1-5): " config_choice
        
        case $config_choice in
            1)
                echo
                echo "=== Current Configuration ==="
                grep -v "^#" .env | grep -v "^$" | sort
                ;;
            2)
                echo
                echo "This will update .env from deployment-config.conf"
                echo "Please provide the path to deployment-config.conf:"
                read -p "Path: " config_path
                
                if [[ -f "$config_path" ]]; then
                    # Create a backup first
                    cp .env .env.backup.$(date +%Y%m%d_%H%M%S)
                    echo "✅ Backup created"
                    
                    # Source the deployment config
                    source "$config_path"
                    
                    # Update key settings
                    sed -i "s|^NODE_ENV=.*|NODE_ENV=$ENV_MODE|" .env
                    sed -i "s|^PRODUCTION_DOMAIN=.*|PRODUCTION_DOMAIN=$DOMAIN_NAME|" .env
                    sed -i "s|^ADMIN_USERNAME=.*|ADMIN_USERNAME=$ADMIN_USERNAME|" .env 2>/dev/null || echo "ADMIN_USERNAME=$ADMIN_USERNAME" >> .env
                    sed -i "s|^ADMIN_PASSWORD=.*|ADMIN_PASSWORD=$ADMIN_PASSWORD|" .env 2>/dev/null || echo "ADMIN_PASSWORD=$ADMIN_PASSWORD" >> .env
                    sed -i "s|^ADMIN_EMAIL=.*|ADMIN_EMAIL=$ADMIN_EMAIL|" .env 2>/dev/null || echo "ADMIN_EMAIL=$ADMIN_EMAIL" >> .env
                    
                    echo "✅ Configuration updated from deployment settings"
                else
                    echo "❌ Configuration file not found: $config_path"
                fi
                ;;
            3)
                echo
                echo "Opening configuration file for editing..."
                ${EDITOR:-nano} .env
                echo "✅ Configuration updated"
                ;;
            4)
                echo
                backup_file=".env.backup.$(date +%Y%m%d_%H%M%S)"
                cp .env "$backup_file"
                echo "✅ Configuration backed up to: $backup_file"
                ;;
            5)
                echo "Exiting configuration management"
                ;;
            *)
                echo "Invalid choice"
                ;;
        esac
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|status|logs|health|config}"
        echo
        echo "Commands:"
        echo "  start   - Start all services"
        echo "  stop    - Stop all services"
        echo "  restart - Restart all services"
        echo "  status  - Show service status"
        echo "  logs    - Show application logs"
        echo "  health  - Run health checks"
        echo "  config  - Manage application configuration"
        exit 1
        ;;
esac
EOF
    
    chmod +x "$app_dir/manage-server.sh"
    log_success "Server management script created at $app_dir/manage-server.sh"
    
    # Create database management script
    log_info "Creating database management script..."
    cat > "$app_dir/manage-database.sh" << 'EOF'
#!/bin/bash
#
# Database Management Script
#

# Load environment variables from .env file
if [[ -f "/var/www/hauling-qr-system/.env" ]]; then
    source /var/www/hauling-qr-system/.env
elif [[ -f ".env" ]]; then
    source .env
fi

# Set defaults if not provided
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-5432}"
DB_NAME="${DB_NAME:-hauling_qr_system}"
DB_USER="${DB_USER:-hauling_app}"
DB_PASSWORD="${DB_PASSWORD:-PostgreSQLPassword123}"

case "$1" in
    connect)
        echo "Connecting to database..."
        PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME"
        ;;
    backup)
        echo "Creating database backup..."
        backup_file="backup_$(date +%Y%m%d_%H%M%S).sql"
        PGPASSWORD="$DB_PASSWORD" pg_dump -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" "$DB_NAME" > "$backup_file"
        echo "✅ Backup created: $backup_file"
        ;;
    test)
        echo "Testing database connection..."
        if PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT version();" 2>/dev/null >/dev/null; then
            echo "✅ Database connection successful"
        else
            echo "❌ Database connection failed"
            exit 1
        fi
        ;;
    verify)
        echo "Verifying database schema..."
        echo "Current tables:"
        PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "\dt"
        echo
        echo "Checking critical tables..."
        
        critical_tables=("users" "drivers" "dump_trucks" "locations" "assignments" "trip_logs" "driver_shifts")
        missing_tables=()
        
        for table in "${critical_tables[@]}"; do
            exists=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = '$table');" 2>/dev/null | tr -d ' ')
            if [[ "$exists" == "t" ]]; then
                echo "✅ $table - exists"
            else
                echo "❌ $table - missing"
                missing_tables+=("$table")
            fi
        done
        
        if [[ ${#missing_tables[@]} -eq 0 ]]; then
            echo
            echo "✅ All critical tables are present"
        else
            echo
            echo "❌ Missing tables: ${missing_tables[*]}"
            echo "Run database initialization:"
            echo "PGPASSWORD=\"$DB_PASSWORD\" psql -h \"$DB_HOST\" -p \"$DB_PORT\" -U \"$DB_USER\" -d \"$DB_NAME\" -f /var/www/hauling-qr-system/database/init.sql"
        fi
        ;;
    init)
        echo "Initializing database schema..."
        if [[ -f "/var/www/hauling-qr-system/database/init.sql" ]]; then
            PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f /var/www/hauling-qr-system/database/init.sql
            echo "✅ Database initialization completed"
            echo "Running verification..."
            $0 verify
        elif [[ -f "database/init.sql" ]]; then
            PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f database/init.sql
            echo "✅ Database initialization completed"
            echo "Running verification..."
            $0 verify
        else
            echo "❌ init.sql file not found"
            echo "Expected locations:"
            echo "  - /var/www/hauling-qr-system/database/init.sql"
            echo "  - database/init.sql"
            exit 1
        fi
        ;;
    tables)
        echo "=== Database Tables ==="
        PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "\dt+"
        ;;
    count)
        echo "=== Table Row Counts ==="
        PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
        SELECT 
            schemaname,
            tablename,
            n_live_tup as \"Live Rows\",
            n_dead_tup as \"Dead Rows\"
        FROM pg_stat_user_tables 
        ORDER BY n_live_tup DESC;"
        ;;
    size)
        echo "=== Database Size Information ==="
        PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
        SELECT 
            schemaname,
            tablename,
            pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as \"Total Size\",
            pg_size_pretty(pg_relation_size(schemaname||'.'||tablename)) as \"Table Size\"
        FROM pg_tables 
        WHERE schemaname = 'public'
        ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;"
        ;;
    health)
        echo "=== Database Health Check ==="
        echo "Connection test:"
        if PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT 'Database OK' as status;" 2>/dev/null; then
            echo "✅ Database connection: OK"
        else
            echo "❌ Database connection: FAILED"
        fi
        
        echo
        echo "Database size:"
        PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT pg_size_pretty(pg_database_size('$DB_NAME'));" 2>/dev/null || echo "Unable to get size"
        
        echo
        echo "Active connections:"
        PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT count(*) FROM pg_stat_activity WHERE datname = '$DB_NAME';" 2>/dev/null || echo "Unable to get connection count"
        
        echo
        echo "Table count:"
        PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" 2>/dev/null || echo "Unable to get table count"
        ;;
    query)
        if [[ -z "$2" ]]; then
            echo "Usage: $0 query \"SQL_QUERY\""
            echo "Example: $0 query \"SELECT COUNT(*) FROM users;\""
            exit 1
        fi
        echo "Executing query: $2"
        PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "$2"
        ;;
    *)
        echo "Database Management Script for Hauling QR System"
        echo "================================================"
        echo
        echo "Usage: $0 {command}"
        echo
        echo "Commands:"
        echo "  connect   - Connect to database interactively"
        echo "  test      - Test database connection"
        echo "  verify    - Verify database schema and critical tables"
        echo "  init      - Initialize database with schema"
        echo "  backup    - Create database backup"
        echo "  tables    - List all tables with details"
        echo "  count     - Show row counts for all tables"
        echo "  size      - Show table sizes"
        echo "  health    - Run comprehensive health check"
        echo "  query     - Execute custom SQL query"
        echo
        echo "Examples:"
        echo "  $0 test"
        echo "  $0 verify"
        echo "  $0 backup"
        echo "  $0 query \"SELECT COUNT(*) FROM users;\""
        echo
        echo "Database Configuration:"
        echo "  Host: $DB_HOST"
        echo "  Port: $DB_PORT"
        echo "  Database: $DB_NAME"
        echo "  User: $DB_USER"
        echo
        exit 1
        ;;
esac
EOF
    
    chmod +x "$app_dir/manage-database.sh"
    log_success "Database management script created at $app_dir/manage-database.sh"
    
    log_success "Management scripts created"
}

# Function to sync deployment configuration to application .env
sync_deployment_to_env() {
    log_step "Syncing deployment configuration to application .env"
    current_step="sync_deployment_to_env"
    
    local app_dir="/var/www/hauling-qr-system"
    local env_file="$app_dir/.env"
    
    # Check if application .env exists
    if [[ ! -f "$env_file" ]]; then
        log_warning "Application .env file not found, creating from template"
        
        # Create basic .env file if it doesn't exist
        cat > "$env_file" << EOF
# =============================================================================
# HAULING QR TRIP SYSTEM - ENVIRONMENT CONFIGURATION
# =============================================================================
# Generated by deployment script for $ENV_MODE environment

# Environment Mode
NODE_ENV=$ENV_MODE
ENABLE_HTTPS=true
AUTO_DETECT_IP=false

# Domain Configuration
PRODUCTION_DOMAIN=$DOMAIN_NAME

# Database Configuration
DB_HOST=$DB_HOST
DB_PORT=$DB_PORT
DB_NAME=$DB_NAME
DB_USER=$DB_USER
DB_PASSWORD=$DB_PASSWORD

# Port Configuration
FRONTEND_PORT=3000
PORT=3000
BACKEND_PORT=5000
BACKEND_HTTP_PORT=5000

# API URLs
REACT_APP_API_URL=https://$DOMAIN_NAME/api
REACT_APP_WS_URL=wss://$DOMAIN_NAME
REACT_APP_USE_HTTPS=true

# Admin Configuration
ADMIN_USERNAME=$ADMIN_USERNAME
ADMIN_PASSWORD=$ADMIN_PASSWORD
ADMIN_EMAIL=$ADMIN_EMAIL

# Security
JWT_SECRET=$JWT_SECRET
JWT_EXPIRY=24h
EOF
        
        log_success "Basic .env file created"
    else
        log_info "Updating existing application .env file"
        
        # Update key settings in existing .env
        sed -i "s|^NODE_ENV=.*|NODE_ENV=$ENV_MODE|" "$env_file"
        sed -i "s|^ENABLE_HTTPS=.*|ENABLE_HTTPS=true|" "$env_file"
        sed -i "s|^PRODUCTION_DOMAIN=.*|PRODUCTION_DOMAIN=$DOMAIN_NAME|" "$env_file"
        
        # Update API URLs
        sed -i "s|^REACT_APP_API_URL=.*|REACT_APP_API_URL=https://$DOMAIN_NAME/api|" "$env_file"
        sed -i "s|^REACT_APP_WS_URL=.*|REACT_APP_WS_URL=wss://$DOMAIN_NAME|" "$env_file"
        sed -i "s|^REACT_APP_USE_HTTPS=.*|REACT_APP_USE_HTTPS=true|" "$env_file"
        
        # Update admin settings
        if grep -q "^ADMIN_USERNAME=" "$env_file"; then
            sed -i "s|^ADMIN_USERNAME=.*|ADMIN_USERNAME=$ADMIN_USERNAME|" "$env_file"
        else
            echo "ADMIN_USERNAME=$ADMIN_USERNAME" >> "$env_file"
        fi
        
        if grep -q "^ADMIN_PASSWORD=" "$env_file"; then
            sed -i "s|^ADMIN_PASSWORD=.*|ADMIN_PASSWORD=$ADMIN_PASSWORD|" "$env_file"
        else
            echo "ADMIN_PASSWORD=$ADMIN_PASSWORD" >> "$env_file"
        fi
        
        if grep -q "^ADMIN_EMAIL=" "$env_file"; then
            sed -i "s|^ADMIN_EMAIL=.*|ADMIN_EMAIL=$ADMIN_EMAIL|" "$env_file"
        else
            echo "ADMIN_EMAIL=$ADMIN_EMAIL" >> "$env_file"
        fi
        
        # Update database password if specified
        if [[ -n "$DB_PASSWORD" ]]; then
            sed -i "s|^DB_PASSWORD=.*|DB_PASSWORD=$DB_PASSWORD|" "$env_file"
        fi
        
        # Update JWT secret if specified
        if [[ -n "$JWT_SECRET" ]]; then
            sed -i "s|^JWT_SECRET=.*|JWT_SECRET=$JWT_SECRET|" "$env_file"
        fi
        
        log_success "Application .env file updated"
    fi
    
    # Set proper permissions
    chmod 600 "$env_file"
    chown www-data:www-data "$env_file"
    
    log_success "Deployment configuration synced to application .env"
}

# Function to setup SSL certificates for Cloudflare mode
setup_ssl_certificates() {
    log_step "Setting up SSL certificates for Cloudflare"
    current_step="setup_ssl"
    
    # Create SSL directory
    mkdir -p /etc/nginx/ssl
    
    if [[ "$SSL_MODE" == "cloudflare" ]]; then
        log_info "Creating self-signed certificates for Cloudflare Full mode..."
        
        # Generate self-signed certificate for Cloudflare Full mode
        openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
            -keyout /etc/nginx/ssl/private.key \
            -out /etc/nginx/ssl/certificate.crt \
            -subj "/C=US/ST=State/L=City/O=Organization/CN=$DOMAIN_NAME" 2>/dev/null
        
        # Set proper permissions
        chmod 600 /etc/nginx/ssl/private.key
        chmod 644 /etc/nginx/ssl/certificate.crt
        
        log_success "Self-signed SSL certificates created for Cloudflare"
    else
        log_info "SSL mode is $SSL_MODE, skipping certificate generation"
    fi
    
    return 0
}

# Function to configure Nginx for PM2 applications
configure_nginx_for_pm2() {
    log_step "Configuring Nginx for PM2 applications"
    current_step="configure_nginx_pm2"
    
    # Get the port from environment
    local app_port=${APP_PORT:-5000}
    
    # Create Nginx configuration for the application
    log_info "Creating Nginx configuration for port $app_port..."
    cat > "/etc/nginx/sites-available/hauling-qr-system" << EOF
server {
    listen 80;
    server_name $DOMAIN_NAME www.$DOMAIN_NAME;
    
    # Redirect HTTP to HTTPS
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name $DOMAIN_NAME www.$DOMAIN_NAME;
    
    # SSL Configuration (for Cloudflare Full mode)
    ssl_certificate /etc/nginx/ssl/certificate.crt;
    ssl_certificate_key /etc/nginx/ssl/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";
    
    # Serve static files from client build
    location / {
        root /var/www/hauling-qr-system/client/build;
        try_files \$uri \$uri/ /index.html;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # Proxy API requests to PM2 managed Node.js server
    location /api {
        proxy_pass http://localhost:$app_port;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        proxy_read_timeout 86400;
    }
    
    # WebSocket support
    location /ws {
        proxy_pass http://localhost:$app_port;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
    
    # Health check endpoint
    location /health {
        proxy_pass http://localhost:5000/api/health;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF
    
    # Enable the site
    ln -sf /etc/nginx/sites-available/hauling-qr-system /etc/nginx/sites-enabled/
    
    # Remove default site if it exists
    rm -f /etc/nginx/sites-enabled/default
    
    # Test Nginx configuration
    if nginx -t 2>/dev/null; then
        log_success "Nginx configuration is valid"
        
        # Reload Nginx
        if systemctl reload nginx 2>/dev/null; then
            log_success "Nginx reloaded successfully"
        else
            log_warning "Nginx reload failed"
        fi
    else
        log_error "Nginx configuration test failed"
        return 1
    fi
    
    log_success "Nginx configured for PM2 applications"
    return 0
}

# ==============================
# Deployment Functions
# ==============================

# Function for system preparation
deploy_system_preparation() {
    log_step "System preparation"
    current_step="system_preparation"
    
    log_info "Updating system packages..."
    if apt-get update && apt-get upgrade -y; then
        log_success "System packages updated successfully"
    else
        log_warning "System package update had some issues, continuing..."
    fi
    
    log_success "System preparation completed"
}

# Function for application setup
deploy_application_setup() {
    log_step "Application setup"
    current_step="application_setup"
    
    # Create application directory
    log_info "Creating application directory..."
    mkdir -p /var/www/hauling-qr-system
    
    # Try to clone repository
    if ! clone_repository_safe "$REPO_URL" "/var/www/hauling-qr-system" "$REPO_BRANCH"; then
        log_warning "Repository clone failed, using fallback structure"
    fi
    
    # Install required packages with improved npm handling
    log_info "Installing required system packages..."
    
    # Install basic packages first
    local basic_packages=("nginx" "postgresql" "postgresql-contrib" "nodejs")
    for package in "${basic_packages[@]}"; do
        log_info "Installing $package..."
        if apt-get install -y "$package"; then
            log_success "$package installed successfully"
        else
            log_error "Failed to install $package"
            return 1
        fi
    done
    
    # Handle npm installation with multiple methods
    if install_npm_robust; then
        log_success "npm installation completed"
        
        # Verify npm installation
        if verify_npm_installation; then
            log_success "npm is working correctly"
        else
            log_warning "npm installed but may have issues - continuing deployment"
        fi
    else
        log_error "npm installation failed with all methods"
        log_info "Attempting additional npm fixes..."
        
        # Try additional fix methods
        if fix_npm_dependencies; then
            log_success "npm issues resolved"
        else
            log_warning "npm installation issues persist"
            log_warning "The deployment will continue, but you may need to install npm manually"
            log_info "You can run: curl -L https://www.npmjs.com/install.sh | sudo sh"
        fi
    fi
    
    # Install and setup PM2
    setup_pm2
    
    log_success "Application setup completed"
}

# Function to verify deployment after completion
verify_deployment() {
    log_step "Verifying deployment"
    current_step="verify_deployment"
    
    # Test database connection
    log_info "Testing database connection..."
    if PGPASSWORD="$DB_PASSWORD" psql -h "${DB_HOST:-localhost}" -p "${DB_PORT:-5432}" -U "${DB_USER:-hauling_app}" -d "${DB_NAME:-hauling_qr_system}" -c "SELECT version();" >/dev/null 2>&1; then
        log_success "Database connection test passed"
    else
        log_error "Database connection test failed"
        log_error "This will cause issues with the manage-database.sh script"
        return 1
    fi
    
    # Test manage-database.sh script
    log_info "Testing manage-database.sh script..."
    cd /var/www/hauling-qr-system
    if ./manage-database.sh test >/dev/null 2>&1; then
        log_success "manage-database.sh script working correctly"
    else
        log_warning "manage-database.sh script has issues"
        log_info "Attempting to fix script configuration..."
        
        # Fix the script if it has wrong configuration
        if grep -q "DB_USER=postgres" manage-database.sh; then
            log_info "Fixing incorrect database user in manage-database.sh..."
            sed -i "s/DB_USER=postgres/DB_USER=${DB_USER}/" manage-database.sh
        fi
    fi
    
    # Verify critical tables exist
    log_info "Verifying critical database tables..."
    local critical_tables=("users" "drivers" "dump_trucks" "locations" "assignments" "trip_logs" "driver_shifts")
    local missing_tables=()
    
    for table in "${critical_tables[@]}"; do
        local exists=$(PGPASSWORD="$DB_PASSWORD" psql -h "${DB_HOST:-localhost}" -p "${DB_PORT:-5432}" -U "${DB_USER:-hauling_app}" -d "${DB_NAME:-hauling_qr_system}" -t -c "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = '$table');" 2>/dev/null | tr -d ' ')
        if [[ "$exists" != "t" ]]; then
            missing_tables+=("$table")
        fi
    done
    
    if [[ ${#missing_tables[@]} -eq 0 ]]; then
        log_success "All critical tables verified successfully"
    else
        log_warning "Some critical tables are missing: ${missing_tables[*]}"
        log_info "You may need to run: ./manage-database.sh init"
    fi
    
    log_success "Deployment verification completed"
}

# Function to create management scripts for users
create_management_scripts() {
    log_step "Creating management scripts"
    current_step="create_management_scripts"
    
    local app_dir="/var/www/hauling-qr-system"
    
    # Create database management script with correct configuration
    log_info "Creating database management script..."
    cat > "$app_dir/manage-database.sh" << EOF
#!/bin/bash
#
# Database Management Script
#

# Load environment variables from .env file
if [[ -f "/var/www/hauling-qr-system/.env" ]]; then
    source /var/www/hauling-qr-system/.env
elif [[ -f ".env" ]]; then
    source .env
fi

# Set defaults using deployment configuration
DB_HOST="\${DB_HOST:-${DB_HOST}}"
DB_PORT="\${DB_PORT:-${DB_PORT}}"
DB_NAME="\${DB_NAME:-${DB_NAME}}"
DB_USER="\${DB_USER:-${DB_USER}}"
DB_PASSWORD="\${DB_PASSWORD:-${DB_PASSWORD}}"

case "\$1" in
    connect)
        echo "Connecting to database..."
        PGPASSWORD="\$DB_PASSWORD" psql -h "\$DB_HOST" -p "\$DB_PORT" -U "\$DB_USER" -d "\$DB_NAME"
        ;;
    backup)
        echo "Creating database backup..."
        backup_file="backup_\$(date +%Y%m%d_%H%M%S).sql"
        PGPASSWORD="\$DB_PASSWORD" pg_dump -h "\$DB_HOST" -p "\$DB_PORT" -U "\$DB_USER" "\$DB_NAME" > "\$backup_file"
        echo "✅ Backup created: \$backup_file"
        ;;
    test)
        echo "Testing database connection..."
        if PGPASSWORD="\$DB_PASSWORD" psql -h "\$DB_HOST" -p "\$DB_PORT" -U "\$DB_USER" -d "\$DB_NAME" -c "SELECT version();" 2>/dev/null >/dev/null; then
            echo "✅ Database connection successful"
        else
            echo "❌ Database connection failed"
            exit 1
        fi
        ;;
    verify)
        echo "Verifying database schema..."
        echo "Current tables:"
        PGPASSWORD="\$DB_PASSWORD" psql -h "\$DB_HOST" -p "\$DB_PORT" -U "\$DB_USER" -d "\$DB_NAME" -c "\dt"
        echo
        echo "Checking critical tables..."
        
        critical_tables=("users" "drivers" "dump_trucks" "locations" "assignments" "trip_logs" "driver_shifts")
        missing_tables=()
        
        for table in "\${critical_tables[@]}"; do
            exists=\$(PGPASSWORD="\$DB_PASSWORD" psql -h "\$DB_HOST" -p "\$DB_PORT" -U "\$DB_USER" -d "\$DB_NAME" -t -c "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = '\$table');" 2>/dev/null | tr -d ' ')
            if [[ "\$exists" == "t" ]]; then
                echo "✅ \$table - exists"
            else
                echo "❌ \$table - missing"
                missing_tables+=("\$table")
            fi
        done
        
        if [[ \${#missing_tables[@]} -eq 0 ]]; then
            echo
            echo "✅ All critical tables are present"
        else
            echo
            echo "❌ Missing tables: \${missing_tables[*]}"
            echo "Run database initialization:"
            echo "PGPASSWORD=\"\$DB_PASSWORD\" psql -h \"\$DB_HOST\" -p \"\$DB_PORT\" -U \"\$DB_USER\" -d \"\$DB_NAME\" -f /var/www/hauling-qr-system/database/init.sql"
        fi
        ;;
    init)
        echo "Initializing database schema..."
        if [[ -f "/var/www/hauling-qr-system/database/init.sql" ]]; then
            PGPASSWORD="\$DB_PASSWORD" psql -h "\$DB_HOST" -p "\$DB_PORT" -U "\$DB_USER" -d "\$DB_NAME" -f /var/www/hauling-qr-system/database/init.sql
            echo "✅ Database initialization completed"
            echo "Running verification..."
            \$0 verify
        elif [[ -f "database/init.sql" ]]; then
            PGPASSWORD="\$DB_PASSWORD" psql -h "\$DB_HOST" -p "\$DB_PORT" -U "\$DB_USER" -d "\$DB_NAME" -f database/init.sql
            echo "✅ Database initialization completed"
            echo "Running verification..."
            \$0 verify
        else
            echo "❌ init.sql file not found"
            echo "Expected locations:"
            echo "  - /var/www/hauling-qr-system/database/init.sql"
            echo "  - database/init.sql"
            exit 1
        fi
        ;;
    tables)
        echo "=== Database Tables ==="
        PGPASSWORD="\$DB_PASSWORD" psql -h "\$DB_HOST" -p "\$DB_PORT" -U "\$DB_USER" -d "\$DB_NAME" -c "\dt+"
        ;;
    count)
        echo "=== Table Row Counts ==="
        PGPASSWORD="\$DB_PASSWORD" psql -h "\$DB_HOST" -p "\$DB_PORT" -U "\$DB_USER" -d "\$DB_NAME" -c "
        SELECT 
            schemaname,
            tablename,
            n_live_tup as \"Live Rows\",
            n_dead_tup as \"Dead Rows\"
        FROM pg_stat_user_tables 
        ORDER BY n_live_tup DESC;"
        ;;
    size)
        echo "=== Database Size Information ==="
        PGPASSWORD="\$DB_PASSWORD" psql -h "\$DB_HOST" -p "\$DB_PORT" -U "\$DB_USER" -d "\$DB_NAME" -c "
        SELECT 
            schemaname,
            tablename,
            pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as \"Total Size\",
            pg_size_pretty(pg_relation_size(schemaname||'.'||tablename)) as \"Table Size\"
        FROM pg_tables 
        WHERE schemaname = 'public'
        ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;"
        ;;
    health)
        echo "=== Database Health Check ==="
        echo "Connection test:"
        if PGPASSWORD="\$DB_PASSWORD" psql -h "\$DB_HOST" -p "\$DB_PORT" -U "\$DB_USER" -d "\$DB_NAME" -c "SELECT 'Database OK' as status;" 2>/dev/null; then
            echo "✅ Database connection: OK"
        else
            echo "❌ Database connection: FAILED"
        fi
        
        echo
        echo "Database size:"
        PGPASSWORD="\$DB_PASSWORD" psql -h "\$DB_HOST" -p "\$DB_PORT" -U "\$DB_USER" -d "\$DB_NAME" -t -c "SELECT pg_size_pretty(pg_database_size('\$DB_NAME'));" 2>/dev/null || echo "Unable to get size"
        
        echo
        echo "Active connections:"
        PGPASSWORD="\$DB_PASSWORD" psql -h "\$DB_HOST" -p "\$DB_PORT" -U "\$DB_USER" -d "\$DB_NAME" -t -c "SELECT count(*) FROM pg_stat_activity WHERE datname = '\$DB_NAME';" 2>/dev/null || echo "Unable to get connection count"
        
        echo
        echo "Table count:"
        PGPASSWORD="\$DB_PASSWORD" psql -h "\$DB_HOST" -p "\$DB_PORT" -U "\$DB_USER" -d "\$DB_NAME" -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" 2>/dev/null || echo "Unable to get table count"
        ;;
    query)
        if [[ -z "\$2" ]]; then
            echo "Usage: \$0 query \"SQL_QUERY\""
            echo "Example: \$0 query \"SELECT COUNT(*) FROM users;\""
            exit 1
        fi
        echo "Executing query: \$2"
        PGPASSWORD="\$DB_PASSWORD" psql -h "\$DB_HOST" -p "\$DB_PORT" -U "\$DB_USER" -d "\$DB_NAME" -c "\$2"
        ;;
    *)
        echo "Database Management Script for Hauling QR System"
        echo "================================================"
        echo
        echo "Usage: \$0 {command}"
        echo
        echo "Commands:"
        echo "  connect   - Connect to database interactively"
        echo "  test      - Test database connection"
        echo "  verify    - Verify database schema and critical tables"
        echo "  init      - Initialize database with schema"
        echo "  backup    - Create database backup"
        echo "  tables    - List all tables with details"
        echo "  count     - Show row counts for all tables"
        echo "  size      - Show table sizes"
        echo "  health    - Run comprehensive health check"
        echo "  query     - Execute custom SQL query"
        echo
        echo "Examples:"
        echo "  \$0 test"
        echo "  \$0 verify"
        echo "  \$0 backup"
        echo "  \$0 query \"SELECT COUNT(*) FROM users;\""
        echo
        echo "Database Configuration:"
        echo "  Host: \$DB_HOST"
        echo "  Port: \$DB_PORT"
        echo "  Database: \$DB_NAME"
        echo "  User: \$DB_USER"
        echo
        exit 1
        ;;
esac
EOF
    
    chmod +x "$app_dir/manage-database.sh"
    log_success "Database management script created at $app_dir/manage-database.sh"
    
    log_success "Management scripts created"
}

# Function for database setup
deploy_database_setup() {
    log_step "Database setup"
    current_step="database_setup"
    
    # Start PostgreSQL service (handle both systemd and non-systemd environments)
    log_info "Starting PostgreSQL service..."
    if [[ "$IS_SYSTEMD" == true ]]; then
        systemctl start postgresql
        systemctl enable postgresql
    else
        # For Docker/container environments
        service postgresql start || {
            log_warning "Failed to start PostgreSQL with service command, trying direct start..."
            su - postgres -c "pg_ctl start -D /var/lib/postgresql/data" 2>/dev/null || true
        }
    fi
    
    # Wait for PostgreSQL to be ready
    log_info "Waiting for PostgreSQL to be ready..."
    local max_attempts=30
    local attempt=1
    while [[ $attempt -le $max_attempts ]]; do
        if pg_isready -h localhost -p 5432 >/dev/null 2>&1; then
            log_success "PostgreSQL is ready"
            break
        fi
        log_debug "PostgreSQL not ready yet (attempt $attempt/$max_attempts)"
        sleep 1
        ((attempt++))
    done
    
    if [[ $attempt -gt $max_attempts ]]; then
        log_error "PostgreSQL failed to start within timeout"
        return 1
    fi
    
    # Create database user with improved error handling
    log_info "Creating database user..."
    
    # Use the database variables set at the beginning of main()
    log_info "Setting up database user: $DB_USER for database: $DB_NAME"
    log_info "Database configuration: Host=$DB_HOST, Port=$DB_PORT, User=$DB_USER, Database=$DB_NAME"
    
    if command -v sudo >/dev/null 2>&1; then
        log_info "Using sudo for PostgreSQL operations"
        # First, try to terminate any active connections for this user
        sudo -u postgres psql -c "SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE usename = '$DB_USER';" 2>/dev/null || true
        
        # Drop existing user if exists
        if sudo -u postgres psql -c "DROP USER IF EXISTS $DB_USER;" 2>/dev/null; then
            log_info "Existing database user removed"
        fi
        
        # Create new user with proper privileges
        if sudo -u postgres psql -c "CREATE USER $DB_USER WITH PASSWORD '$DB_PASSWORD' CREATEDB;" 2>/dev/null; then
            log_success "Database user created successfully with CREATEDB privilege"
        else
            # If creation fails, try to update existing user password and privileges
            log_info "User may already exist, updating password and privileges..."
            if sudo -u postgres psql -c "ALTER USER $DB_USER WITH PASSWORD '$DB_PASSWORD' CREATEDB;" 2>/dev/null; then
                log_success "Database user password and privileges updated"
            else
                log_error "Failed to create or update database user with sudo"
                return 1
            fi
        fi
        
    else
        # For environments without sudo (like some Docker containers)
        log_info "Using su command (sudo not available)"
        su - postgres -c "psql -c \"SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE usename = '$DB_USER';\"" 2>/dev/null || true
        
        if su - postgres -c "psql -c \"DROP USER IF EXISTS $DB_USER;\"" 2>/dev/null; then
            log_info "Existing database user removed"
        fi
        
        if su - postgres -c "psql -c \"CREATE USER $DB_USER WITH PASSWORD '$DB_PASSWORD' CREATEDB;\"" 2>/dev/null; then
            log_success "Database user created successfully with CREATEDB privilege"
        else
            log_info "User may already exist, updating password and privileges..."
            if su - postgres -c "psql -c \"ALTER USER $DB_USER WITH PASSWORD '$DB_PASSWORD' CREATEDB;\"" 2>/dev/null; then
                log_success "Database user password and privileges updated"
            else
                log_error "Failed to create or update database user"
                return 1
            fi
        fi
    fi
    
    # Create database with improved error handling
    log_info "Creating database..."
    if command -v sudo >/dev/null 2>&1; then
        sudo -u postgres psql -c "CREATE DATABASE ${DB_NAME:-hauling_qr_system} OWNER ${DB_USER:-hauling_app};" 2>/dev/null || true
        sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE ${DB_NAME:-hauling_qr_system} TO ${DB_USER:-hauling_app};" || true
    else
        su - postgres -c "psql -c \"CREATE DATABASE ${DB_NAME:-hauling_qr_system} OWNER ${DB_USER:-hauling_app};\"" 2>/dev/null || true
        su - postgres -c "psql -c \"GRANT ALL PRIVILEGES ON DATABASE ${DB_NAME:-hauling_qr_system} TO ${DB_USER:-hauling_app};\"" || true
    fi
    
    # Test database connection
    log_info "Testing database connection..."
    if PGPASSWORD="$DB_PASSWORD" psql -h "${DB_HOST:-localhost}" -p "${DB_PORT:-5432}" -U "${DB_USER:-hauling_app}" -d "${DB_NAME:-hauling_qr_system}" -c "SELECT version();" >/dev/null 2>&1; then
        log_success "Database connection test successful"
    else
        log_error "Database connection test failed"
        log_info "Attempting to fix database connection..."
        
        # Try to fix common issues
        if command -v sudo >/dev/null 2>&1; then
            sudo -u postgres psql -c "ALTER USER ${DB_USER:-hauling_app} WITH PASSWORD '$DB_PASSWORD';" || true
        else
            su - postgres -c "psql -c \"ALTER USER ${DB_USER:-hauling_app} WITH PASSWORD '$DB_PASSWORD';\"" || true
        fi
        
        # Test again
        if PGPASSWORD="$DB_PASSWORD" psql -h "${DB_HOST:-localhost}" -p "${DB_PORT:-5432}" -U "${DB_USER:-hauling_app}" -d "${DB_NAME:-hauling_qr_system}" -c "SELECT version();" >/dev/null 2>&1; then
            log_success "Database connection fixed"
        else
            log_warning "Database connection still has issues, but continuing deployment"
        fi
    fi
    
    # Initialize database schema
    local init_sql_path="/var/www/hauling-qr-system/database/init.sql"
    
    if [[ -f "$init_sql_path" ]]; then
        log_info "Initializing database schema from init.sql..."
        
        # Always use the hauling_app user for consistency
        log_info "Initializing database as $DB_USER user..."
        if PGPASSWORD="$DB_PASSWORD" psql -h "${DB_HOST:-localhost}" -p "${DB_PORT:-5432}" -U "${DB_USER:-hauling_app}" -d "${DB_NAME:-hauling_qr_system}" -f "$init_sql_path" 2>&1; then
            log_success "Database schema initialized successfully"
        else
            log_error "Failed to initialize database schema"
            log_error "Attempting to fix database permissions and retry..."
            
            # Try to fix permissions and retry
            if command -v sudo >/dev/null 2>&1; then
                sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE ${DB_NAME:-hauling_qr_system} TO ${DB_USER:-hauling_app};" 2>/dev/null || true
                sudo -u postgres psql -d "${DB_NAME:-hauling_qr_system}" -c "GRANT ALL ON SCHEMA public TO ${DB_USER:-hauling_app};" 2>/dev/null || true
                sudo -u postgres psql -d "${DB_NAME:-hauling_qr_system}" -c "GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO ${DB_USER:-hauling_app};" 2>/dev/null || true
                sudo -u postgres psql -d "${DB_NAME:-hauling_qr_system}" -c "GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO ${DB_USER:-hauling_app};" 2>/dev/null || true
            fi
            
            # Retry initialization
            if PGPASSWORD="$DB_PASSWORD" psql -h "${DB_HOST:-localhost}" -p "${DB_PORT:-5432}" -U "${DB_USER:-hauling_app}" -d "${DB_NAME:-hauling_qr_system}" -f "$init_sql_path" 2>&1; then
                log_success "Database schema initialized successfully after permission fix"
            else
                log_error "Failed to initialize database schema even after permission fix"
                log_error "Manual initialization required: PGPASSWORD=\"$DB_PASSWORD\" psql -h \"${DB_HOST:-localhost}\" -p \"${DB_PORT:-5432}\" -U \"${DB_USER:-hauling_app}\" -d \"${DB_NAME:-hauling_qr_system}\" -f \"$init_sql_path\""
                return 1
            fi
        fi
        
        # Verify that key tables were created
        log_info "Verifying database schema..."
        local table_count
        if command -v sudo >/dev/null 2>&1; then
            table_count=$(sudo -u postgres psql -d "${DB_NAME:-hauling_qr_system}" -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" 2>/dev/null | tr -d ' ')
        else
            table_count=$(PGPASSWORD="$DB_PASSWORD" psql -h "${DB_HOST:-localhost}" -p "${DB_PORT:-5432}" -U "${DB_USER:-hauling_app}" -d "${DB_NAME:-hauling_qr_system}" -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" 2>/dev/null | tr -d ' ')
        fi
        
        if [[ "$table_count" -gt 0 ]]; then
            log_success "Database schema verification passed ($table_count tables created)"
            
            # Check for specific critical tables (updated list based on actual schema)
            local critical_tables=("users" "locations" "drivers" "driver_shifts")
            local missing_tables=()
            
            for table in "${critical_tables[@]}"; do
                local exists
                if command -v sudo >/dev/null 2>&1; then
                    exists=$(sudo -u postgres psql -d "${DB_NAME:-hauling_qr_system}" -t -c "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = '$table');" 2>/dev/null | tr -d ' ')
                else
                    exists=$(PGPASSWORD="$DB_PASSWORD" psql -h "${DB_HOST:-localhost}" -p "${DB_PORT:-5432}" -U "${DB_USER:-hauling_app}" -d "${DB_NAME:-hauling_qr_system}" -t -c "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = '$table');" 2>/dev/null | tr -d ' ')
                fi
                
                if [[ "$exists" != "t" ]]; then
                    missing_tables+=("$table")
                fi
            done
            
            if [[ ${#missing_tables[@]} -eq 0 ]]; then
                log_success "All critical tables verified successfully"
            else
                log_warning "Some critical tables are missing: ${missing_tables[*]}"
                log_warning "Application may not function correctly"
            fi
        else
            log_error "Database schema verification failed - no tables found"
            log_error "Database initialization may have failed"
            return 1
        fi
        
    else
        log_error "Database init.sql not found at: $init_sql_path"
        log_error "Cannot initialize database schema"
        return 1
    fi
    
    # Run database migrations (ensure pg module is available)
    if [[ -f "/var/www/hauling-qr-system/database/run-migration.js" ]]; then
        log_info "Running database migrations..."
        cd /var/www/hauling-qr-system
        
        # Check if pg module is installed, install if missing
        if ! node -e "require('pg')" 2>/dev/null; then
            log_info "Installing pg module for database migrations..."
            if npm install pg 2>/dev/null; then
                log_success "pg module installed successfully"
            else
                log_warning "Failed to install pg module, skipping migrations"
                cd - >/dev/null
                return 0
            fi
        fi
        
        if node database/run-migration.js; then
            log_success "Database migrations completed"
        else
            log_warning "Database migrations had issues, but continuing deployment"
        fi
        cd - >/dev/null
    else
        log_warning "Migration script not found, skipping migrations"
    fi
    
    # Ensure .env file has correct database configuration
    log_info "Updating .env file with correct database configuration..."
    local env_file="/var/www/hauling-qr-system/.env"
    
    # Create or update .env file with correct database settings
    if [[ -f "$env_file" ]]; then
        # Update existing .env file
        sed -i "s|^DB_HOST=.*|DB_HOST=${DB_HOST}|" "$env_file"
        sed -i "s|^DB_PORT=.*|DB_PORT=${DB_PORT}|" "$env_file"
        sed -i "s|^DB_NAME=.*|DB_NAME=${DB_NAME}|" "$env_file"
        sed -i "s|^DB_USER=.*|DB_USER=${DB_USER}|" "$env_file"
        sed -i "s|^DB_PASSWORD=.*|DB_PASSWORD=${DB_PASSWORD}|" "$env_file"
    else
        # Create new .env file with database settings
        cat >> "$env_file" << EOF

# Database Configuration (Updated by deployment)
DB_HOST=${DB_HOST}
DB_PORT=${DB_PORT}
DB_NAME=${DB_NAME}
DB_USER=${DB_USER}
DB_PASSWORD=${DB_PASSWORD}
EOF
    fi
    
    # Verify the .env file has correct settings
    log_info "Verifying .env database configuration..."
    if grep -q "DB_USER=${DB_USER}" "$env_file"; then
        log_success ".env file updated with correct database user: ${DB_USER}"
    else
        log_warning ".env file may not have correct database configuration"
    fi
    
    log_success "Database setup completed"
}

# Function to verify database schema
verify_database_schema() {
    log_info "Verifying database schema and tables..."
    
    # Test database connection
    if ! PGPASSWORD="$DB_PASSWORD" psql -h "${DB_HOST:-localhost}" -p "${DB_PORT:-5432}" -U "${DB_USER:-hauling_app}" -d "${DB_NAME:-hauling_qr_system}" -c "SELECT version();" >/dev/null 2>&1; then
        log_error "Cannot connect to database"
        return 1
    fi
    
    # List all tables
    log_info "Current database tables:"
    if command -v sudo >/dev/null 2>&1; then
        sudo -u postgres psql -d "${DB_NAME:-hauling_qr_system}" -c "\dt" 2>/dev/null || PGPASSWORD="$DB_PASSWORD" psql -h "${DB_HOST:-localhost}" -p "${DB_PORT:-5432}" -U "${DB_USER:-hauling_app}" -d "${DB_NAME:-hauling_qr_system}" -c "\dt"
    else
        PGPASSWORD="$DB_PASSWORD" psql -h "${DB_HOST:-localhost}" -p "${DB_PORT:-5432}" -U "${DB_USER:-hauling_app}" -d "${DB_NAME:-hauling_qr_system}" -c "\dt"
    fi
    
    # Check for critical tables (updated list based on actual schema)
    local critical_tables=("users" "locations" "drivers" "driver_shifts")
    local missing_tables=()
    local existing_tables=()
    
    for table in "${critical_tables[@]}"; do
        local exists
        if command -v sudo >/dev/null 2>&1; then
            exists=$(sudo -u postgres psql -d "${DB_NAME:-hauling_qr_system}" -t -c "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = '$table');" 2>/dev/null | tr -d ' ')
        else
            exists=$(PGPASSWORD="$DB_PASSWORD" psql -h "${DB_HOST:-localhost}" -p "${DB_PORT:-5432}" -U "${DB_USER:-hauling_app}" -d "${DB_NAME:-hauling_qr_system}" -t -c "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = '$table');" 2>/dev/null | tr -d ' ')
        fi
        
        if [[ "$exists" == "t" ]]; then
            existing_tables+=("$table")
        else
            missing_tables+=("$table")
        fi
    done
    
    if [[ ${#existing_tables[@]} -gt 0 ]]; then
        log_success "Existing tables: ${existing_tables[*]}"
    fi
    
    if [[ ${#missing_tables[@]} -gt 0 ]]; then
        log_warning "Missing tables: ${missing_tables[*]}"
        log_warning "You may need to run database initialization manually:"
        log_warning "PGPASSWORD=\"$DB_PASSWORD\" psql -h \"${DB_HOST:-localhost}\" -p \"${DB_PORT:-5432}\" -U \"${DB_USER:-hauling_app}\" -d \"${DB_NAME:-hauling_qr_system}\" -f /var/www/hauling-qr-system/database/init.sql"
        return 1
    else
        log_success "All critical tables are present"
        return 0
    fi
}

# Function to create necessary application directories
create_application_directories() {
    log_step "Creating application directories"
    current_step="create_directories"
    
    local app_dir="/var/www/hauling-qr-system"
    
    # Create log directories
    log_info "Creating log directories..."
    mkdir -p "$app_dir/logs"
    mkdir -p "$app_dir/server/logs"
    mkdir -p "$app_dir/uploads"
    mkdir -p "$app_dir/ssl/dev"
    mkdir -p "$app_dir/ssl/production"
    
    # Set proper permissions
    chown -R www-data:www-data "$app_dir/logs"
    chown -R www-data:www-data "$app_dir/server/logs"
    chown -R www-data:www-data "$app_dir/uploads"
    chmod -R 755 "$app_dir/logs"
    chmod -R 755 "$app_dir/server/logs"
    chmod -R 755 "$app_dir/uploads"
    
    log_success "Application directories created"
}

# Function for basic configuration
deploy_basic_configuration() {
    log_step "Basic configuration"
    current_step="basic_configuration"
    
    # Create application environment file
    log_info "Creating application environment configuration..."
    
    # Set default port if not specified
    local app_port=${APP_PORT:-5000}
    
    # Check if there's an existing .env in the repository
    local source_env=""
    if [[ -f "/var/www/hauling-qr-system/.env" ]]; then
        source_env="/var/www/hauling-qr-system/.env"
        log_info "Found existing .env in repository, will merge with deployment settings"
    fi
    
    # Create the production .env file
    cat > /var/www/hauling-qr-system/.env << EOF
# =============================================================================
# HAULING QR TRIP SYSTEM - PRODUCTION ENVIRONMENT CONFIGURATION
# =============================================================================
# Generated by deployment script for production environment
# This file is used by the application at runtime

# =============================================================================
# ENVIRONMENT MODE CONFIGURATION
# =============================================================================
NODE_ENV=$ENV_MODE
ENABLE_HTTPS=true
AUTO_DETECT_IP=false

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
DB_HOST=localhost
DB_PORT=5432
DB_NAME=hauling_qr_system
DB_USER=postgres
DB_PASSWORD=$DB_PASSWORD

# Database pool configuration
DB_POOL_MAX=25
DB_POOL_MIN=5

# =============================================================================
# PORT CONFIGURATION
# =============================================================================
# Frontend Port (React app)
FRONTEND_PORT=3000
PORT=3000

# Backend Port
BACKEND_PORT=$app_port
BACKEND_HTTP_PORT=$app_port

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
ALLOWED_ORIGINS=$DOMAIN_NAME,www.$DOMAIN_NAME,localhost,127.0.0.1

# JWT Authentication
JWT_SECRET=$JWT_SECRET
JWT_EXPIRY=24h

# =============================================================================
# SSL/HTTPS CONFIGURATION
# =============================================================================
SSL_CERT_PATH_PROD=/etc/nginx/ssl/certificate.crt
SSL_KEY_PATH_PROD=/etc/nginx/ssl/private.key

# =============================================================================
# CLIENT CONFIGURATION
# =============================================================================
CLIENT_PORT=3000
CLIENT_HTTPS=true

# Client API and WebSocket URLs
REACT_APP_API_URL=https://$DOMAIN_NAME/api
REACT_APP_WS_URL=wss://$DOMAIN_NAME
REACT_APP_USE_HTTPS=true

# Production features
REACT_APP_ENABLE_DEV_TOOLS=false
REACT_APP_DEBUG_MODE=false
GENERATE_SOURCEMAP=false

# =============================================================================
# QR CODE CONFIGURATION
# =============================================================================
QR_CODE_SIZE=200
QR_CODE_QUALITY=H

# QR Scanner settings
REACT_APP_QR_CAMERA_FACING=environment
REACT_APP_QR_SCAN_DELAY=500

# =============================================================================
# FILE UPLOAD CONFIGURATION
# =============================================================================
MAX_FILE_SIZE=5242880
UPLOAD_PATH=./uploads
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf

# =============================================================================
# RATE LIMITING CONFIGURATION
# =============================================================================
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=5000
AUTH_RATE_LIMIT_WINDOW_MS=900000
AUTH_RATE_LIMIT_MAX_REQUESTS=1000

# =============================================================================
# ADMIN CONFIGURATION
# =============================================================================
ADMIN_USERNAME=$ADMIN_USERNAME
ADMIN_PASSWORD=$ADMIN_PASSWORD
ADMIN_EMAIL=$ADMIN_EMAIL

# =============================================================================
# LOGGING AND MONITORING
# =============================================================================
LOG_LEVEL=info

# =============================================================================
# OPTIONAL SERVICES
# =============================================================================
REDIS_URL=redis://localhost:6379
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
EOF
    
    # Set proper permissions
    log_info "Setting file permissions..."
    chown -R www-data:www-data /var/www/hauling-qr-system
    chmod -R 755 /var/www/hauling-qr-system
    chmod 600 /var/www/hauling-qr-system/.env
    
    # Create necessary directories
    create_application_directories
    
    # Create PM2 ecosystem and start applications
    create_pm2_ecosystem
    start_applications_with_pm2
    
    # Create management scripts for users
    create_management_scripts
    
    # Setup SSL certificates for Cloudflare
    setup_ssl_certificates
    
    # Sync deployment configuration to application .env
    sync_deployment_to_env
    
    # Configure Nginx for PM2 applications
    configure_nginx_for_pm2
    
    log_success "Basic configuration completed"
}

# Function to install application dependencies
install_application_dependencies() {
    log_step "Installing application dependencies"
    current_step="install_dependencies"
    
    # Navigate to application directory
    if ! cd /var/www/hauling-qr-system; then
        log_error "Failed to navigate to application directory"
        return 1
    fi
    
    # Install server dependencies
    if [[ -f "server/package.json" ]]; then
        log_info "Installing server dependencies..."
        cd server
        if npm install; then
            log_success "Server dependencies installed successfully"
        else
            log_error "Failed to install server dependencies"
            cd ..
            return 1
        fi
        cd ..
    else
        log_warning "Server package.json not found, skipping server dependencies"
    fi
    
    # Install client dependencies
    if [[ -f "client/package.json" ]]; then
        log_info "Installing client dependencies..."
        cd client
        if npm install; then
            log_success "Client dependencies installed successfully"
        else
            log_error "Failed to install client dependencies"
            cd ..
            return 1
        fi
        cd ..
    else
        log_warning "Client package.json not found, skipping client dependencies"
    fi
    
    # Install root dependencies if they exist
    if [[ -f "package.json" ]]; then
        log_info "Installing root dependencies..."
        if npm install; then
            log_success "Root dependencies installed successfully"
        else
            log_warning "Failed to install root dependencies, continuing..."
        fi
    fi
    
    log_success "Application dependencies installation completed"
}

# Function to build client application
build_client_application() {
    log_step "Building client application"
    current_step="build_client"
    
    # Navigate to application directory
    if ! cd /var/www/hauling-qr-system; then
        log_error "Failed to navigate to application directory"
        return 1
    fi
    
    # Build client application
    if [[ -f "client/package.json" ]]; then
        log_info "Building React client application..."
        cd client
        
        # Set production environment
        export NODE_ENV=production
        export REACT_APP_API_URL="https://$DOMAIN_NAME/api"
        export REACT_APP_WS_URL="wss://$DOMAIN_NAME/ws"
        
        if npm run build; then
            log_success "Client application built successfully"
            
            # Copy build files to nginx directory
            if [[ -d "build" ]]; then
                log_info "Copying build files to nginx directory..."
                rm -rf /var/www/html/*
                cp -r build/* /var/www/html/
                chown -R www-data:www-data /var/www/html
                log_success "Build files copied to nginx directory"
            fi
        else
            log_error "Failed to build client application"
            cd ..
            return 1
        fi
        cd ..
    else
        log_warning "Client package.json not found, skipping client build"
    fi
    
    log_success "Client application build completed"
}

# Function to start application services
start_application_services() {
    log_step "Starting application services"
    current_step="start_services"
    
    # Navigate to application directory
    if ! cd /var/www/hauling-qr-system; then
        log_error "Failed to navigate to application directory"
        return 1
    fi
    
    # Install PM2 globally if not already installed
    if ! command -v pm2 >/dev/null 2>&1; then
        log_info "Installing PM2 process manager..."
        if npm install -g pm2; then
            log_success "PM2 installed successfully"
        else
            log_error "Failed to install PM2"
            return 1
        fi
    fi
    
    # Create PM2 ecosystem file
    log_info "Creating PM2 ecosystem configuration..."
    cat > ecosystem.config.js << EOF
module.exports = {
  apps: [
    {
      name: 'hauling-qr-server',
      script: 'server/server.js',
      cwd: '/var/www/hauling-qr-system',
      env: {
        NODE_ENV: '$ENV_MODE',
        PORT: 5000,
        DB_HOST: '$DB_HOST',
        DB_PORT: $DB_PORT,
        DB_NAME: '$DB_NAME',
        DB_USER: '$DB_USER',
        DB_PASSWORD: '$DB_PASSWORD',
        ADMIN_USERNAME: '$ADMIN_USERNAME',
        ADMIN_PASSWORD: '$ADMIN_PASSWORD',
        ADMIN_EMAIL: '$ADMIN_EMAIL',
        JWT_SECRET: '$JWT_SECRET'
      },
      instances: 1,
      exec_mode: 'fork',
      watch: false,
      max_memory_restart: '1G',
      error_file: './server/logs/err.log',
      out_file: './server/logs/out.log',
      log_file: './server/logs/combined.log',
      time: true
    }
  ]
};
EOF
    
    # Create log directories
    mkdir -p server/logs
    chown -R www-data:www-data server/logs
    
    # Stop any existing PM2 processes
    log_info "Stopping any existing PM2 processes..."
    pm2 stop all 2>/dev/null || true
    pm2 delete all 2>/dev/null || true
    
    # Start the server with PM2
    log_info "Starting server with PM2..."
    if pm2 start ecosystem.config.js; then
        log_success "Server started successfully with PM2"
        
        # Save PM2 configuration
        pm2 save
        
        # Setup PM2 to start on boot
        pm2 startup systemd -u root --hp /root 2>/dev/null || true
        
        # Show PM2 status
        log_info "PM2 Process Status:"
        pm2 list
        
    else
        log_error "Failed to start server with PM2"
        return 1
    fi
    
    # Start nginx
    log_info "Starting nginx web server..."
    if systemctl start nginx && systemctl enable nginx; then
        log_success "Nginx started and enabled successfully"
    else
        log_error "Failed to start nginx"
        return 1
    fi
    
    # Verify services are running
    log_info "Verifying services..."
    
    # Check PM2 processes
    if pm2 list | grep -q "online"; then
        log_success "✅ Server is running (PM2)"
    else
        log_error "❌ Server is not running properly"
    fi
    
    # Check nginx
    if systemctl is-active --quiet nginx; then
        log_success "✅ Nginx is running"
    else
        log_error "❌ Nginx is not running properly"
    fi
    
    # Check if server is responding
    log_info "Testing server response..."
    sleep 3  # Give server time to start
    
    if curl -s http://localhost:5000/api/health >/dev/null 2>&1; then
        log_success "✅ Server API is responding"
    else
        log_warning "⚠️  Server API not responding yet (may need more time to start)"
    fi
    
    log_success "Application services startup completed"
}

# ==============================
# Main Execution Flow
# ==============================

main() {
    # Initialize log file
    echo "=== Hauling QR Trip Management System Deployment Log ===" > "$LOG_FILE"
    echo "Date: $(date)" >> "$LOG_FILE"
    echo "Version: $VERSION" >> "$LOG_FILE"

    # Detect environment first
    detect_environment

    # Install essential tools first (before any other operations)
    install_essential_tools

    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -c|--config)
                CONFIG_FILE="$2"
                shift 2
                ;;
            -d|--domain)
                DOMAIN_NAME="$2"
                shift 2
                ;;
            -e|--env)
                ENV_MODE="$2"
                shift 2
                ;;
            -n|--non-interactive)
                INTERACTIVE=false
                shift
                ;;
            -q|--quiet)
                VERBOSE=false
                CONSOLE_OUTPUT=false
                shift
                ;;
            --dry-run)
                DRY_RUN=true
                shift
                ;;
            --log-level)
                case "$2" in
                    debug) CURRENT_LOG_LEVEL=$LOG_LEVEL_DEBUG ;;
                    info) CURRENT_LOG_LEVEL=$LOG_LEVEL_INFO ;;
                    warning) CURRENT_LOG_LEVEL=$LOG_LEVEL_WARNING ;;
                    error) CURRENT_LOG_LEVEL=$LOG_LEVEL_ERROR ;;
                    *) log_error "Invalid log level: $2"; exit 1 ;;
                esac
                shift 2
                ;;
            --json-output)
                # JSON output is already enabled by default
                shift
                ;;
            --verify-database)
                # Load configuration if provided
                if [[ -n "$CONFIG_FILE" ]]; then
                    if [[ ! -f "$CONFIG_FILE" ]]; then
                        log_error "Configuration file not found: $CONFIG_FILE"
                        exit 1
                    fi
                    source "$CONFIG_FILE"
                fi
                
                # Set default DB_PASSWORD if not set
                if [[ -z "$DB_PASSWORD" ]]; then
                    DB_PASSWORD="PostgreSQLPassword"
                fi
                
                verify_database_schema
                exit $?
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done

    # Load deployment configuration (separate from application .env)
    if [[ -n "$CONFIG_FILE" ]]; then
        if [[ ! -f "$CONFIG_FILE" ]]; then
            log_error "Configuration file not found: $CONFIG_FILE"
            exit 1
        fi
        
        log_info "Loading deployment configuration from: $CONFIG_FILE"
        source "$CONFIG_FILE"
    else
        log_info "No deployment configuration file specified, using defaults"
    fi

    # Set defaults for missing configuration
    DOMAIN_NAME=${DOMAIN_NAME:-"truckhaul.top"}
    ENV_MODE=${ENV_MODE:-"production"}
    SSL_MODE=${SSL_MODE:-"cloudflare"}
    ADMIN_USERNAME=${ADMIN_USERNAME:-"admin"}
    ADMIN_PASSWORD=${ADMIN_PASSWORD:-"admin12345"}
    ADMIN_EMAIL=${ADMIN_EMAIL:-"admin@${DOMAIN_NAME}"}
    
    # Database configuration defaults
    DB_HOST=${DB_HOST:-"localhost"}
    DB_PORT=${DB_PORT:-"5432"}
    DB_NAME=${DB_NAME:-"hauling_qr_system"}
    DB_USER=${DB_USER:-"hauling_app"}
    DB_PASSWORD=${DB_PASSWORD:-"PostgreSQLPassword"}
    
    # Generate secure passwords if not provided
    if [[ -z "$DB_PASSWORD" ]]; then
        if command -v openssl >/dev/null 2>&1; then
            DB_PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)
        else
            DB_PASSWORD="defaultdbpass123"
        fi
    fi
    
    if [[ -z "$JWT_SECRET" ]]; then
        if command -v openssl >/dev/null 2>&1; then
            JWT_SECRET=$(openssl rand -base64 64 | tr -d "=+/" | cut -c1-48)
        else
            JWT_SECRET="defaultjwtsecret123456789012345678901234567890"
        fi
    fi

    # Validate prerequisites
    if ! validate_prerequisites; then
        log_error "Prerequisites validation failed. Deployment cannot continue."
        exit 1
    fi

    # Exit if dry run
    if [[ "$DRY_RUN" == true ]]; then
        log_info "Dry run mode - configuration validated successfully"
        log_info "Domain: $DOMAIN_NAME"
        log_info "Environment: $ENV_MODE"
        log_info "SSL Mode: $SSL_MODE"
        exit 0
    fi

    # Start main deployment process
    log_section "Starting Hauling QR Trip Management System Deployment"
    log_info "Version: $VERSION"
    log_info "Domain: $DOMAIN_NAME"
    log_info "Environment: $ENV_MODE"
    log_info "SSL Mode: $SSL_MODE"

    # Deployment steps with proper error handling
    deploy_system_preparation
    deploy_application_setup
    deploy_database_setup
    deploy_basic_configuration
    
    # Install dependencies and build application
    install_application_dependencies
    build_client_application
    
    # Start server and client
    start_application_services
    
    # Create management scripts
    create_management_scripts
    
    # Verify deployment is working correctly
    verify_deployment

    # Calculate deployment duration
    DEPLOYMENT_END_TIME=$(date +%s)
    DEPLOYMENT_DURATION=$((DEPLOYMENT_END_TIME - DEPLOYMENT_START_TIME))

    log_section "Deployment Completed Successfully"
    log_success "Total deployment time: ${DEPLOYMENT_DURATION} seconds"
    
    # Display important paths and information
    log_info "=== DEPLOYMENT SUMMARY ==="
    log_info "Application URL: https://$DOMAIN_NAME"
    log_info "Admin credentials: $ADMIN_USERNAME / $ADMIN_PASSWORD"
    log_info ""
    log_info "=== FILE LOCATIONS ==="
    log_info "Application directory: /var/www/hauling-qr-system"
    log_info "Server files: /var/www/hauling-qr-system/server"
    log_info "Client files: /var/www/hauling-qr-system/client"
    log_info "Database files: /var/www/hauling-qr-system/database"
    log_info "Environment config: /var/www/hauling-qr-system/.env"
    log_info "Nginx config: /etc/nginx/sites-available/hauling-qr-system"
    log_info "SSL certificates: /etc/nginx/ssl/"
    log_info ""
    log_info "=== LOGS ==="
    log_info "Deployment log: $LOG_FILE"
    log_info "Server logs: /var/www/hauling-qr-system/server/logs/"
    log_info "PM2 logs: pm2 logs hauling-qr-server"
    log_info ""
    log_info "=== USEFUL COMMANDS ==="
    log_info "Server management: /var/www/hauling-qr-system/manage-server.sh {start|stop|restart|status|logs|health}"
    log_info "Database management: /var/www/hauling-qr-system/manage-database.sh {connect|backup|test|verify|init}"
    log_info ""
    log_info "Quick commands:"
    log_info "Check status: /var/www/hauling-qr-system/manage-server.sh status"
    log_info "View logs: /var/www/hauling-qr-system/manage-server.sh logs"
    log_info "Health check: /var/www/hauling-qr-system/manage-server.sh health"
    log_info "Restart app: /var/www/hauling-qr-system/manage-server.sh restart"

    exit 0
}

# Execute main function with all arguments
main "$@"