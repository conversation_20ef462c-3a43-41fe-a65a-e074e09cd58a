/**
 * Jest configuration for mobile compatibility tests
 */

module.exports = {
  testEnvironment: 'node',
  testMatch: ['<rootDir>/**/*.test.js'],
  testTimeout: 30000, // 30 seconds for mobile tests
  setupFilesAfterEnv: ['<rootDir>/setup.js'],
  collectCoverageFrom: [
    '../../../client/src/**/*.{js,jsx}',
    '!../../../client/src/index.js',
    '!../../../client/src/reportWebVitals.js'
  ],
  coverageDirectory: '../../../coverage/mobile',
  verbose: true,
  bail: false, // Continue running tests even if some fail
  maxWorkers: 1, // Run tests sequentially to avoid browser conflicts
};