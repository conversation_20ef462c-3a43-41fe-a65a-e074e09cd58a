import React, { useState } from 'react';
import { usePWAStatus } from '../hooks/usePWAStatus';
import toast from 'react-hot-toast';

const ManualSyncButton = ({ className = '', showLabel = true }) => {
  const { 
    isOnline, 
    syncStatus, 
    queuedScans, 
    queuedConnections, 
    triggerSync,
    syncError 
  } = usePWAStatus();
  
  const [isManualSyncing, setIsManualSyncing] = useState(false);

  const handleManualSync = async () => {
    if (!isOnline) {
      toast.error('Cannot sync while offline. Please check your connection.');
      return;
    }

    if (queuedScans === 0 && queuedConnections === 0) {
      toast.success('No data to sync - everything is up to date!');
      return;
    }

    setIsManualSyncing(true);
    
    try {
      console.log('[ManualSync] Starting manual sync...');
      await triggerSync();
      
      // Wait a moment for sync to complete
      setTimeout(() => {
        setIsManualSyncing(false);
        toast.success(`Sync completed! ${queuedScans + queuedConnections} items synced.`);
      }, 2000);
      
    } catch (error) {
      console.error('[ManualSync] Manual sync failed:', error);
      setIsManualSyncing(false);
      toast.error('Sync failed. Please try again.');
    }
  };

  // Don't show button if offline and no queued data
  if (!isOnline && queuedScans === 0 && queuedConnections === 0) {
    return null;
  }

  const totalQueued = queuedScans + queuedConnections;
  const isSyncing = syncStatus === 'syncing' || isManualSyncing;
  
  // Determine button style based on status
  let buttonClass = 'manual-sync-btn ';
  if (isSyncing) {
    buttonClass += 'syncing ';
  } else if (syncError) {
    buttonClass += 'error ';
  } else if (totalQueued > 0) {
    buttonClass += 'pending ';
  } else {
    buttonClass += 'synced ';
  }
  
  buttonClass += className;

  return (
    <div className="manual-sync-container">
      <button
        className={buttonClass}
        onClick={handleManualSync}
        disabled={isSyncing || !isOnline}
        title={
          !isOnline 
            ? 'Offline - sync will start automatically when connection is restored'
            : totalQueued > 0 
              ? `Sync ${totalQueued} pending items`
              : 'All data synced'
        }
      >
        <span className="sync-icon">
          {isSyncing ? '🔄' : totalQueued > 0 ? '📤' : '✅'}
        </span>
        {showLabel && (
          <span className="sync-text">
            {isSyncing 
              ? 'Syncing...' 
              : totalQueued > 0 
                ? `Sync (${totalQueued})`
                : 'Synced'
            }
          </span>
        )}
      </button>
      
      {syncError && (
        <div className="sync-error-message">
          ⚠️ {syncError}
        </div>
      )}
      
      <style jsx>{`
        .manual-sync-container {
          position: relative;
        }
        
        .manual-sync-btn {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px 16px;
          border: none;
          border-radius: 8px;
          font-size: 14px;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
          min-width: 100px;
          justify-content: center;
        }
        
        .manual-sync-btn:disabled {
          cursor: not-allowed;
          opacity: 0.6;
        }
        
        .manual-sync-btn.pending {
          background: #ff9800;
          color: white;
          animation: pulse 2s infinite;
        }
        
        .manual-sync-btn.syncing {
          background: #2196f3;
          color: white;
        }
        
        .manual-sync-btn.synced {
          background: #4caf50;
          color: white;
        }
        
        .manual-sync-btn.error {
          background: #f44336;
          color: white;
        }
        
        .manual-sync-btn:hover:not(:disabled) {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }
        
        .sync-icon {
          font-size: 16px;
          animation: ${isSyncing ? 'spin 1s linear infinite' : 'none'};
        }
        
        .sync-text {
          white-space: nowrap;
        }
        
        .sync-error-message {
          position: absolute;
          top: 100%;
          left: 0;
          right: 0;
          background: #f44336;
          color: white;
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 12px;
          margin-top: 4px;
          z-index: 1000;
        }
        
        @keyframes pulse {
          0% { opacity: 1; }
          50% { opacity: 0.7; }
          100% { opacity: 1; }
        }
        
        @keyframes spin {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

export default ManualSyncButton;
