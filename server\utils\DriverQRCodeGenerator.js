const QRCode = require('qrcode');
const { v4: uuidv4 } = require('uuid');
const crypto = require('crypto');
const { query } = require('../config/database');
const { logError, logInfo } = require('./logger');

/**
 * Driver QR Code Generator Utility
 * Handles generation, validation, and storage of driver QR codes
 */
class DriverQRCodeGenerator {
  
  /**
   * Generate QR code data structure for a driver with enhanced security
   * @param {number} driverId - Driver database ID
   * @param {string} employeeId - Driver employee ID
   * @returns {Object} QR code data object
   */
  static generateQRData(driverId, employeeId) {
    const timestamp = new Date().toISOString();
    const baseData = {
      id: employeeId, // Use employee_id as the main identifier
      driver_id: driverId,
      employee_id: employeeId,
      generated_date: timestamp,
      type: 'driver' // Add type for consistency with other QR codes
    };

    // Add tamper detection hash
    const dataString = JSON.stringify(baseData);
    const tamperHash = crypto.createHash('sha256')
      .update(dataString + process.env.QR_SECRET || 'default-secret')
      .digest('hex')
      .substring(0, 8);

    return {
      ...baseData,
      checksum: tamperHash
    };
  }

  /**
   * Generate and store driver QR code
   * @param {number} driverId - Driver database ID
   * @param {string} employeeId - Driver employee ID
   * @returns {Promise<Object>} Generated QR code data and image
   */
  static async generateDriverQR(driverId, employeeId) {
    try {
      // Validate inputs
      if (!driverId || !employeeId) {
        throw new Error('Driver ID and Employee ID are required');
      }

      // Check if driver exists and is active
      const driverResult = await query(
        'SELECT id, employee_id, full_name, status FROM drivers WHERE id = $1',
        [driverId]
      );

      if (driverResult.rows.length === 0) {
        throw new Error('Driver not found');
      }

      const driver = driverResult.rows[0];
      
      if (driver.status !== 'active') {
        throw new Error('Cannot generate QR code for inactive driver');
      }

      if (driver.employee_id !== employeeId) {
        throw new Error('Employee ID mismatch');
      }

      // Generate QR code data
      const qrData = this.generateQRData(driverId, employeeId);
      
      // Convert to JSON string for QR code generation
      const qrDataString = JSON.stringify(qrData);

      // Generate QR code image as data URL
      const qrCodeDataURL = await QRCode.toDataURL(qrDataString, {
        width: 300,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        },
        errorCorrectionLevel: 'M' // Medium error correction for ID cards
      });

      // Store QR code data in database
      await query(
        'UPDATE drivers SET driver_qr_code = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
        [JSON.stringify(qrData), driverId]
      );

      // Enhanced audit logging for QR generation
      logInfo('DRIVER_QR_GENERATED', `QR code generated for driver ${employeeId}`, {
        driver_id: driverId,
        employee_id: employeeId,
        generated_at: qrData.generated_date,
        qr_data_hash: this._hashQRData(qrData),
        security_level: 'enhanced',
        tamper_protection: 'enabled'
      });

      return {
        success: true,
        qr_data: qrData,
        qr_code_image: qrCodeDataURL,
        driver: {
          id: driver.id,
          employee_id: driver.employee_id,
          full_name: driver.full_name
        }
      };

    } catch (error) {
      logError('DRIVER_QR_GENERATION_ERROR', error, {
        driver_id: driverId,
        employee_id: employeeId
      });
      throw error;
    }
  }

  /**
   * Validate driver QR code structure and data
   * @param {string|Object} qrData - QR code data (JSON string or parsed object)
   * @returns {Promise<Object>} Validation result with driver info
   */
  static async validateDriverQR(qrData) {
    try {
      // Parse QR data if it's a string
      let parsedData;
      if (typeof qrData === 'string') {
        try {
          parsedData = JSON.parse(qrData);
        } catch (parseError) {
          throw new Error('Invalid QR code format - not valid JSON');
        }
      } else {
        parsedData = qrData;
      }

      // Validate required fields
      const requiredFields = ['id', 'driver_id', 'employee_id', 'generated_date', 'type'];
      for (const field of requiredFields) {
        if (!parsedData[field]) {
          throw new Error(`Missing required field: ${field}`);
        }
      }

      // Enhanced tamper detection - verify checksum if present
      // Note: Checksum validation is optional to support legacy QR codes
      if (parsedData.checksum && process.env.STRICT_QR_VALIDATION === 'true') {
        const { checksum, ...dataWithoutChecksum } = parsedData;
        const expectedChecksum = crypto.createHash('sha256')
          .update(JSON.stringify(dataWithoutChecksum) + (process.env.QR_SECRET || 'default-secret'))
          .digest('hex')
          .substring(0, 8);

        if (checksum !== expectedChecksum) {
          throw new Error('QR code tamper detection failed - invalid checksum');
        }
      }

      // Validate field types
      if (typeof parsedData.driver_id !== 'number' && !Number.isInteger(Number(parsedData.driver_id))) {
        throw new Error('Invalid driver_id format');
      }

      if (parsedData.type !== 'driver') {
        throw new Error('Invalid QR code type - expected "driver"');
      }

      // Validate date format
      const generatedDate = new Date(parsedData.generated_date);
      if (isNaN(generatedDate.getTime())) {
        throw new Error('Invalid generated_date format');
      }

      // Check if driver exists and is active
      const driverResult = await query(
        `SELECT id, employee_id, full_name, status, driver_qr_code 
         FROM drivers 
         WHERE id = $1 AND employee_id = $2`,
        [parsedData.driver_id, parsedData.employee_id]
      );

      if (driverResult.rows.length === 0) {
        throw new Error('Driver not found or employee ID mismatch');
      }

      const driver = driverResult.rows[0];

      if (driver.status !== 'active') {
        throw new Error('Driver account is inactive. Please contact your supervisor.');
      }

      // Note: We don't validate against stored QR data timestamp to allow 
      // long-term QR code usage without requiring regeneration

      return {
        success: true,
        valid: true,
        driver: {
          id: driver.id,
          employee_id: driver.employee_id,
          full_name: driver.full_name,
          status: driver.status
        },
        qr_data: parsedData
      };

    } catch (error) {
      logError('DRIVER_QR_VALIDATION_ERROR', error, {
        qr_data: typeof qrData === 'string' ? qrData.substring(0, 100) : qrData
      });
      
      return {
        success: false,
        valid: false,
        error: error.message,
        driver: null
      };
    }
  }

  /**
   * Get driver QR code information
   * @param {number} driverId - Driver database ID
   * @returns {Promise<Object>} Driver QR code info
   */
  static async getDriverQRInfo(driverId) {
    try {
      const result = await query(
        `SELECT id, employee_id, full_name, status, driver_qr_code, updated_at
         FROM drivers 
         WHERE id = $1`,
        [driverId]
      );

      if (result.rows.length === 0) {
        throw new Error('Driver not found');
      }

      const driver = result.rows[0];
      
      return {
        success: true,
        driver: {
          id: driver.id,
          employee_id: driver.employee_id,
          full_name: driver.full_name,
          status: driver.status
        },
        has_qr_code: !!driver.driver_qr_code,
        qr_data: driver.driver_qr_code,
        last_updated: driver.updated_at
      };

    } catch (error) {
      logError('DRIVER_QR_INFO_ERROR', error, { driver_id: driverId });
      throw error;
    }
  }

  /**
   * Regenerate QR code for a driver (useful if QR code is compromised)
   * @param {number} driverId - Driver database ID
   * @returns {Promise<Object>} New QR code data and image
   */
  static async regenerateDriverQR(driverId) {
    try {
      // Get current driver info
      const driverResult = await query(
        'SELECT id, employee_id, full_name, status FROM drivers WHERE id = $1',
        [driverId]
      );

      if (driverResult.rows.length === 0) {
        throw new Error('Driver not found');
      }

      const driver = driverResult.rows[0];

      // Generate new QR code
      const result = await this.generateDriverQR(driverId, driver.employee_id);

      logInfo('DRIVER_QR_REGENERATED', `QR code regenerated for driver ${driver.employee_id}`, {
        driver_id: driverId,
        employee_id: driver.employee_id
      });

      return result;

    } catch (error) {
      logError('DRIVER_QR_REGENERATION_ERROR', error, { driver_id: driverId });
      throw error;
    }
  }

  /**
   * Bulk generate QR codes for multiple drivers
   * @param {Array<number>} driverIds - Array of driver IDs
   * @returns {Promise<Object>} Results for each driver
   */
  static async bulkGenerateQR(driverIds) {
    const results = {
      success: [],
      failed: [],
      total: driverIds.length
    };

    for (const driverId of driverIds) {
      try {
        // Get driver info
        const driverResult = await query(
          'SELECT id, employee_id, status FROM drivers WHERE id = $1',
          [driverId]
        );

        if (driverResult.rows.length === 0) {
          results.failed.push({
            driver_id: driverId,
            error: 'Driver not found'
          });
          continue;
        }

        const driver = driverResult.rows[0];
        
        if (driver.status !== 'active') {
          results.failed.push({
            driver_id: driverId,
            employee_id: driver.employee_id,
            error: 'Driver is not active'
          });
          continue;
        }

        // Generate QR code
        const qrResult = await this.generateDriverQR(driverId, driver.employee_id);
        results.success.push({
          driver_id: driverId,
          employee_id: driver.employee_id,
          qr_data: qrResult.qr_data
        });

      } catch (error) {
        results.failed.push({
          driver_id: driverId,
          error: error.message
        });
      }
    }

    logInfo('DRIVER_QR_BULK_GENERATION', 'Bulk QR generation completed', {
      total: results.total,
      success_count: results.success.length,
      failed_count: results.failed.length
    });

    return results;
  }

  /**
   * Hash QR data for audit logging
   * @param {Object} qrData - QR data to hash
   * @returns {string} SHA256 hash
   * @private
   */
  static _hashQRData(qrData) {
    try {
      const dataString = JSON.stringify(qrData);
      return crypto.createHash('sha256').update(dataString).digest('hex').substring(0, 16);
    } catch (error) {
      return 'HASH_ERROR';
    }
  }
}

module.exports = DriverQRCodeGenerator;