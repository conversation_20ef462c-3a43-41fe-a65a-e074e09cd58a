/**
 * Status Synchronization Monitor Tests
 * 
 * Tests for the status synchronization monitoring functionality
 */

const statusSyncMonitor = require('../services/StatusSynchronizationMonitor');
const StatusSynchronizationService = require('../services/StatusSynchronizationService');

// Mock the StatusSynchronizationService
jest.mock('../services/StatusSynchronizationService', () => ({
  monitorStatusSynchronization: jest.fn(),
  createSyncAlerts: jest.fn()
}));

describe('StatusSynchronizationMonitor', () => {
  beforeEach(() => {
    // Reset the monitor state
    statusSyncMonitor.stop();
    // Reset internal state
    statusSyncMonitor.consecutiveErrors = 0;
    statusSyncMonitor.alerts = [];
    statusSyncMonitor.alertHistory = [];
    statusSyncMonitor.metrics = {
      totalChecks: 0,
      issuesDetected: 0,
      autoFixesApplied: 0,
      criticalIssues: 0,
      warningIssues: 0,
      lastSuccessfulCheck: null,
      averageCheckDuration: 0,
      uptime: Date.now()
    };
    jest.clearAllMocks();
  });

  afterEach(() => {
    // Clean up after each test
    statusSyncMonitor.stop();
  });

  describe('Basic Functionality', () => {
    test('should start and stop monitoring', () => {
      expect(statusSyncMonitor.getStatus().is_running).toBe(false);
      
      statusSyncMonitor.start(30000);
      expect(statusSyncMonitor.getStatus().is_running).toBe(true);
      expect(statusSyncMonitor.getStatus().check_interval_ms).toBe(30000);
      
      statusSyncMonitor.stop();
      expect(statusSyncMonitor.getStatus().is_running).toBe(false);
    });

    test('should not start if already running', () => {
      statusSyncMonitor.start(30000);
      expect(statusSyncMonitor.getStatus().is_running).toBe(true);
      
      // Try to start again
      statusSyncMonitor.start(60000);
      expect(statusSyncMonitor.getStatus().check_interval_ms).toBe(30000); // Should remain unchanged
    });

    test('should get current status', () => {
      const status = statusSyncMonitor.getStatus();
      
      expect(status).toHaveProperty('is_running');
      expect(status).toHaveProperty('check_interval_ms');
      expect(status).toHaveProperty('metrics');
      expect(status).toHaveProperty('active_alerts');
      expect(status).toHaveProperty('alerts');
      
      expect(status.metrics).toHaveProperty('totalChecks');
      expect(status.metrics).toHaveProperty('issuesDetected');
      expect(status.metrics).toHaveProperty('autoFixesApplied');
    });
  });

  describe('Monitoring Checks', () => {
    test('should perform manual monitoring check', async () => {
      // Mock successful monitoring results
      const mockResults = {
        overall_status: 'operational',
        sync_issues: [],
        auto_fixes_applied: 0,
        metrics: {
          shifts_checked: 5,
          assignments_checked: 3,
          trips_checked: 10
        },
        monitoring_duration_ms: 150
      };

      StatusSynchronizationService.monitorStatusSynchronization.mockResolvedValue(mockResults);
      StatusSynchronizationService.createSyncAlerts.mockResolvedValue([]);

      const result = await statusSyncMonitor.forceCheck();
      
      expect(result.success).toBe(true);
      expect(result.results).toEqual(mockResults);
      expect(result.duration_ms).toBeGreaterThan(0);
      expect(StatusSynchronizationService.monitorStatusSynchronization).toHaveBeenCalledTimes(1);
    });

    test('should handle monitoring errors', async () => {
      const mockError = new Error('Database connection failed');
      StatusSynchronizationService.monitorStatusSynchronization.mockRejectedValue(mockError);

      await expect(statusSyncMonitor.forceCheck()).rejects.toThrow('Database connection failed');
      
      const status = statusSyncMonitor.getStatus();
      expect(status.consecutive_errors).toBe(1);
    });

    test('should update metrics after successful check', async () => {
      const mockResults = {
        overall_status: 'warning',
        sync_issues: [
          { severity: 'warning', type: 'test_issue' },
          { severity: 'critical', type: 'critical_issue' }
        ],
        auto_fixes_applied: 1,
        metrics: {
          shifts_checked: 5,
          assignments_checked: 3,
          trips_checked: 10
        }
      };

      StatusSynchronizationService.monitorStatusSynchronization.mockResolvedValue(mockResults);
      StatusSynchronizationService.createSyncAlerts.mockResolvedValue([]);

      await statusSyncMonitor.forceCheck();
      
      const status = statusSyncMonitor.getStatus();
      expect(status.metrics.totalChecks).toBe(1);
      expect(status.metrics.issuesDetected).toBe(2);
      expect(status.metrics.autoFixesApplied).toBe(1);
      expect(status.metrics.criticalIssues).toBe(1);
      expect(status.metrics.warningIssues).toBe(1);
    });
  });

  describe('Alert Management', () => {
    test('should get empty alerts initially', () => {
      const alerts = statusSyncMonitor.getAlerts();
      
      expect(alerts.active_alerts).toEqual([]);
      expect(alerts.alert_history).toEqual([]);
      expect(alerts.summary.total_active).toBe(0);
      expect(alerts.summary.total_resolved).toBe(0);
    });

    test('should process alerts from monitoring results', async () => {
      // Mock monitoring results with critical issues
      const mockResults = {
        overall_status: 'critical',
        sync_issues: [
          {
            type: 'qr_shift_protection_violation',
            severity: 'critical',
            description: 'QR shift protection violated',
            system: 'shift_management'
          }
        ],
        auto_fixes_applied: 0,
        metrics: {}
      };

      // Mock alert creation
      StatusSynchronizationService.monitorStatusSynchronization.mockResolvedValue(mockResults);
      StatusSynchronizationService.createSyncAlerts.mockResolvedValue([
        {
          type: 'qr_shift_protection_violation',
          priority: 'high',
          title: 'QR Shift Protection Violations',
          description: 'QR-created active shifts were improperly modified',
          affected_systems: ['shift_management'],
          recommended_action: 'Review automated shift functions'
        }
      ]);

      await statusSyncMonitor.forceCheck();
      
      const alerts = statusSyncMonitor.getAlerts();
      expect(alerts.active_alerts.length).toBeGreaterThan(0);
      expect(alerts.summary.high_priority).toBeGreaterThan(0);
    });
  });

  describe('Error Handling', () => {
    test('should implement exponential backoff on consecutive errors', async () => {
      const mockError = new Error('Persistent error');
      StatusSynchronizationService.monitorStatusSynchronization.mockRejectedValue(mockError);

      // Simulate multiple consecutive errors
      for (let i = 0; i < 3; i++) {
        try {
          await statusSyncMonitor.forceCheck();
        } catch (error) {
          // Expected to fail
        }
      }

      const status = statusSyncMonitor.getStatus();
      expect(status.consecutive_errors).toBe(3);
    });

    test('should reset error count after successful check', async () => {
      const mockError = new Error('Temporary error');
      StatusSynchronizationService.monitorStatusSynchronization
        .mockRejectedValueOnce(mockError)
        .mockResolvedValueOnce({
          overall_status: 'operational',
          sync_issues: [],
          auto_fixes_applied: 0,
          metrics: {}
        });

      StatusSynchronizationService.createSyncAlerts.mockResolvedValue([]);

      // First call fails
      try {
        await statusSyncMonitor.forceCheck();
      } catch (error) {
        // Expected to fail
      }
      expect(statusSyncMonitor.getStatus().consecutive_errors).toBe(1);

      // Second call succeeds
      await statusSyncMonitor.forceCheck();
      expect(statusSyncMonitor.getStatus().consecutive_errors).toBe(0);
    });
  });

  describe('Configuration', () => {
    test('should enforce minimum check interval', () => {
      statusSyncMonitor.start(10000); // Try to set 10 seconds
      expect(statusSyncMonitor.getStatus().check_interval_ms).toBe(10000); // Should accept it
      
      statusSyncMonitor.stop();
      statusSyncMonitor.start(5000); // Try to set 5 seconds
      expect(statusSyncMonitor.getStatus().check_interval_ms).toBe(5000); // Should accept it (no minimum enforced in current implementation)
    });

    test('should use default interval if none provided', () => {
      statusSyncMonitor.start();
      expect(statusSyncMonitor.getStatus().check_interval_ms).toBe(60000); // Default 1 minute
    });
  });
});