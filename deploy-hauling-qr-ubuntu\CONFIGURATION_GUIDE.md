# 📝 Configuration Guide

## Two-Stage Configuration Approach

The Hauling QR Trip Management System uses a two-stage configuration approach:

1. **Deployment Configuration** (`deployment-config.conf`)
2. **Application Configuration** (`.env`)

## 🔧 Deployment Configuration

The `deployment-config.conf` file is used **during deployment** to configure:

- Domain name
- SSL mode
- Admin credentials
- Database password (used to create database user and transferred to .env)
- GitHub repository access
- Environment mode (production/development)

```bash
# Example deployment-config.conf
DOMAIN_NAME="truckhaul.top"
ENV_MODE="production"
SSL_MODE="cloudflare"
ADMIN_USERNAME="admin"
ADMIN_PASSWORD="admin12345"
ADMIN_EMAIL="<EMAIL>"
DB_PASSWORD="PostgreSQLPassword"
REPO_URL="https://<EMAIL>/mightybadz18/hauling-qr-trip-management.git"
REPO_BRANCH="main"
```

## 🔧 Application Configuration

The `.env` file is used **by the application at runtime** to configure:

- Database connection
- API URLs
- JWT secrets
- CORS settings
- File upload settings
- And many other application-specific settings

```bash
# Example .env (simplified)
NODE_ENV=production
DB_HOST=127.0.0.1
DB_PORT=5432
DB_NAME=hauling_qr_system
DB_USER=hauling_app
DB_PASSWORD=PostgreSQLPassword
REACT_APP_API_URL=https://truckhaul.top/api
REACT_APP_WS_URL=wss://truckhaul.top
```

## 🔄 Configuration Sync

During deployment, key settings from `deployment-config.conf` are automatically synced to the application's `.env` file:

- Environment mode (production/development)
- Domain name
- SSL settings
- Admin credentials
- Database password (after creating database user)
- Security settings

This ensures that the application uses the correct settings after deployment.

### Why DB_PASSWORD is in Deployment Config

The database password is included in `deployment-config.conf` because:

1. **Database Setup**: The deployment script needs it to create the PostgreSQL user
2. **Security**: The deployer chooses a secure password during deployment
3. **Transfer**: It's automatically written to the application's `.env` file
4. **One-time Use**: After deployment, it's only used by the application from `.env`

## 📋 Configuration Files Location

- **Deployment Configuration**: `./deployment-config.conf`
- **Application Configuration**: `/var/www/hauling-qr-system/.env`

## 🔐 Security Notes

1. Both configuration files contain sensitive information and should be kept secure
2. Set proper file permissions: `chmod 600 deployment-config.conf`
3. Never commit these files to version control
4. Change default passwords after deployment

## 🔧 Manual Configuration Sync

If you need to manually sync deployment settings to the application's `.env` file:

```bash
# Run the sync script
./sync-env-config.sh

# Or use the management script
/var/www/hauling-qr-system/manage-server.sh config
```

## 🚀 Best Practices

1. **Keep deployment-config.conf minimal** - Only include settings needed for deployment
2. **Use .env for application settings** - All runtime settings should be in .env
3. **Change default passwords** - Update admin and database passwords after deployment
4. **Backup configurations** - Keep secure backups of both configuration files