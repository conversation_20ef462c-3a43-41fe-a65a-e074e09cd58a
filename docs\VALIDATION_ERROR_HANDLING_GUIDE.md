# Validation Error Handling Guide

This guide provides comprehensive information about the enhanced validation error handling system implemented in the Hauling QR Trip Management System.

## Overview

The system distinguishes between two types of errors:
- **Validation Errors (400 Bad Request)**: Business rule violations that users can resolve
- **System Errors (500 Internal Server Error)**: Technical failures requiring system intervention

## ValidationError Class

### Location
`server/utils/ValidationError.js`

### Purpose
Custom error class that extends the standard Error class to provide structured validation error information.

### Key Features
- `isValidationError` flag for easy identification
- Structured details object for additional context
- Proper stack trace capture
- Static factory methods for common scenarios

### Basic Usage

```javascript
const ValidationError = require('../utils/ValidationError');

// Basic validation error
throw new ValidationError('Invalid truck assignment');

// With structured details
throw new ValidationError('Trip validation failed', {
  field: 'truck_id',
  code: 'TRUCK_NOT_FOUND',
  context: { truck_id: 123 },
  suggestion: 'Verify the truck number and ensure it is active'
});
```

### Factory Methods

#### ValidationError.forField()
For field-specific validation failures:

```javascript
throw ValidationError.forField(
  'truck_id',
  'Truck not found or inactive',
  truckId,
  'Verify the truck number and ensure it is active'
);
```

#### ValidationError.forWorkflow()
For workflow state violations:

```javascript
throw ValidationError.forWorkflow(
  'unloading_end',
  'start loading operation',
  'loading_start'
);
```

#### ValidationError.forBusinessRule()
For business rule violations:

```javascript
throw ValidationError.forBusinessRule(
  'Post-completion temporal validation',
  {
    truck_id: truckId,
    location_id: locationId,
    minutes_since_completion: 5,
    minimum_required_minutes: 10
  }
);
```

## Server-Side Implementation

### Error Detection and Handling

In `server/routes/scanner.js`, the catch block distinguishes between error types:

```javascript
try {
  // Process scan logic
} catch (error) {
  // Check if this is a validation error
  const isValidationError = error.isValidationError || error instanceof ValidationError;
  
  if (isValidationError) {
    // Return 400 Bad Request for validation errors
    res.status(400).json({
      success: false,
      error: 'Validation Error',
      error_type: 'validation',
      message: error.message,
      details: error.details || {},
      processing_time_ms: Date.now() - scanStartTime,
      location_data: error.location_data || null
    });
  } else {
    // Return 500 Internal Server Error for system errors
    res.status(500).json({
      success: false,
      error: 'Scan Processing Error',
      error_type: 'system',
      message: error.message,
      processing_time_ms: Date.now() - scanStartTime
    });
  }
}
```

### Common Validation Scenarios

#### 4-Phase Workflow Violations

```javascript
// Location type validation
if (location.type === 'unloading' && operationType === 'loading') {
  throw new ValidationError(
    `Cannot start loading operation at unloading location "${location.name}". Loading operations must be performed at loading-type locations only.`,
    {
      type: 'workflow_violation',
      current_phase: 'unloading_end',
      required_action: 'Must scan truck QR at loading location',
      location_name: location.name,
      location_type: location.type,
      violation_type: 'location_type_mismatch',
      next_steps: [
        'Navigate to an appropriate loading location',
        'Scan the truck QR code at the loading location',
        'Ensure the location type matches the required operation'
      ]
    }
  );
}
```

#### Post-Completion Temporal Validation

```javascript
// Same location temporal validation
if (minutesSinceCompletion < minimumGapMinutes) {
  throw new ValidationError(
    `Cannot start new trip at "${location.name}" only ${Math.round(minutesSinceCompletion)} minutes after completion at the same location. Please wait at least ${minimumGapMinutes} minutes before scanning at this location again, or proceed to a different location.`,
    {
      type: 'temporal_validation',
      violation_type: 'same_location_insufficient_time_gap',
      truck_number: truck.truck_number,
      location_name: location.name,
      minutes_since_completion: Math.round(minutesSinceCompletion),
      minimum_required_minutes: minimumGapMinutes,
      recommendation: `Wait ${Math.ceil(minimumGapMinutes - minutesSinceCompletion)} more minutes or scan at different location`
    }
  );
}
```

#### Assignment Role Validation

```javascript
// Assignment role mismatch
if (assignment.loading_location_id !== location.id && assignment.unloading_location_id !== location.id) {
  throw new ValidationError(
    `Assignment role mismatch: Truck ${truck.truck_number} is not assigned to operate at "${location.name}". Current assignment: ${assignment.loading_location_name} → ${assignment.unloading_location_name}`,
    {
      type: 'assignment_validation',
      violation_type: 'location_not_in_assignment',
      truck_number: truck.truck_number,
      current_location: location.name,
      assigned_route: `${assignment.loading_location_name} → ${assignment.unloading_location_name}`,
      suggestion: 'Verify the truck assignment or scan at the correct location'
    }
  );
}
```

#### Driver Attendance Parameter Validation (Fixed January 2025)

The driver attendance endpoints previously had issues with empty string parameters from frontend query strings. This has been resolved with enhanced parameter cleaning and validation.

```javascript
// Empty string parameter handling (now fixed)
const cleanQuery = Object.fromEntries(
  Object.entries(req.query).map(([key, value]) => [key, value === '' ? undefined : value])
);

// Enhanced validation schema
const attendanceQuerySchema = Joi.object({
  driver_id: createNumericField({ positive: true }).optional().allow(''),
  date_from: createDateField().optional().allow(''),
  date_to: createDateField().optional().allow(''),
  truck_id: createNumericField({ positive: true }).optional().allow(''),
  status: Joi.string().valid('all', 'active', 'completed', 'scheduled').optional().allow(''),
  // ... other fields
}).options({ stripUnknown: true });
```

**Common Issues Resolved:**
- Empty string parameters (`driver_id=&date_from=&`) no longer cause 400 errors
- Database queries now include all shifts, not just completed ones
- Enhanced debugging with `ATTENDANCE_DEBUG` logs
- Debug endpoint `/api/driver-admin/debug` for troubleshooting

**Error Handling Example:**
```javascript
if (error) {
  console.log('ATTENDANCE_VALIDATION_ERROR:', error.details[0].message);
  return res.status(400).json({
    success: false,
    error: 'VALIDATION_ERROR',
    message: 'Invalid query parameters',
    details: error.details[0].message
  });
}
}
```

## Client-Side Implementation

### Error Type Detection

In `client/src/pages/scanner/QRScanner.js`, the error handling checks the `error_type` field:

```javascript
try {
  const response = await scannerAPI.processScan(scanRequest);
  // Handle success
} catch (error) {
  const errorData = error.response?.data;
  const errorMessage = errorData?.message;
  const isValidationError = errorData?.error_type === 'validation';
  
  if (isValidationError) {
    // Handle validation errors with user guidance
    handleValidationError(errorData);
  } else {
    // Handle system errors
    handleSystemError(errorData);
  }
}
```

### Validation Error Handling

```javascript
function handleValidationError(errorData) {
  if (errorData.message?.includes('4-Phase Workflow Violation')) {
    // Extract next steps from the error message
    const nextStepsMatch = errorData.message.match(/Next Steps:\n([\s\S]*)/);
    const nextSteps = nextStepsMatch ? 
      nextStepsMatch[1].split('\n').filter(step => step.trim()) : [];
    
    toast.error(
      <div className="max-w-md">
        <div className="font-medium text-orange-600">Workflow Validation</div>
        <div className="text-sm mt-1">{errorData.message.split('\n\nNext Steps:')[0]}</div>
        {nextSteps.length > 0 && (
          <div className="text-xs mt-2 text-gray-700">
            <div className="font-medium">Next Steps:</div>
            <ul className="list-disc list-inside mt-1">
              {nextSteps.map((step, index) => (
                <li key={index}>{step.replace('• ', '')}</li>
              ))}
            </ul>
          </div>
        )}
      </div>,
      { duration: 10000 }
    );
  } else if (errorData.message?.includes('Cannot start loading operation at unloading location')) {
    toast.error(
      <div>
        <div className="font-medium text-orange-600">Location Type Validation</div>
        <div className="text-sm">{errorData.message}</div>
        <div className="text-xs mt-1 text-gray-600">
          Loading operations must be at loading locations only
        </div>
      </div>,
      { duration: 8000 }
    );
  } else {
    // Other validation errors
    toast.error(
      <div>
        <div className="font-medium text-orange-600">Validation Error</div>
        <div className="text-sm">{errorData.message}</div>
      </div>,
      { duration: 6000 }
    );
  }
}
```

### System Error Handling

```javascript
function handleSystemError(errorData) {
  toast.error(
    <div>
      <div className="font-medium text-red-600">System Error</div>
      <div className="text-sm">{errorData.message}</div>
      <div className="text-xs mt-1 text-gray-600">
        Please try again or contact support if the issue persists
      </div>
    </div>,
    { duration: 8000 }
  );
}
```

## Visual Styling

### Validation Errors
- **Color**: Orange (`text-orange-600`, `bg-orange-50`, `border-orange-200`)
- **Icon**: ⚠️ (Warning)
- **Duration**: 6-10 seconds depending on complexity
- **Header**: "Workflow Validation" or "Validation Error"

### System Errors
- **Color**: Red (`text-red-600`, `bg-red-50`, `border-red-200`)
- **Icon**: ❌ (Error)
- **Duration**: 8 seconds
- **Header**: "System Error"

## Error Response Structure

### Validation Error Response (400 Bad Request)

```json
{
  "success": false,
  "error": "Validation Error",
  "error_type": "validation",
  "message": "4-Phase Workflow Violation: Cannot perform loading operation at unloading location",
  "details": {
    "type": "workflow_violation",
    "current_phase": "unloading_end",
    "required_action": "Must scan truck QR at loading location",
    "location_name": "Point B - Primary Dump Site",
    "location_type": "unloading",
    "violation_type": "location_type_mismatch",
    "field": "workflow_state",
    "code": "WORKFLOW_VIOLATION",
    "suggestion": "Navigate to the appropriate loading location to continue the workflow",
    "next_steps": [
      "Navigate to an appropriate loading location",
      "Scan the truck QR code at the loading location",
      "Ensure the location type matches the required operation"
    ]
  },
  "processing_time_ms": 1250,
  "location_data": {
    "id": "LOC-002",
    "name": "Point B - Primary Dump Site",
    "type": "unloading"
  }
}
```

### System Error Response (500 Internal Server Error)

```json
{
  "success": false,
  "error": "Scan Processing Error",
  "error_type": "system",
  "message": "Database connection failed during scan processing",
  "processing_time_ms": 1250,
  "location_data": null
}
```

## Testing Validation Errors

### Manual Testing

1. **Location Type Mismatch**: Try to scan a truck at an unloading location when expecting a loading operation
2. **Post-Completion Temporal**: Complete a trip and immediately try to scan at the same location
3. **Assignment Role Conflict**: Scan a truck at a location not included in its assignment
4. **Invalid QR Code**: Scan malformed or missing QR code data

### Automated Testing

```javascript
// Test validation error handling
describe('Validation Error Handling', () => {
  it('should return 400 for workflow violations', async () => {
    const response = await request(app)
      .post('/api/scanner/scan')
      .send({
        scan_type: 'truck',
        scanned_data: JSON.stringify({ type: 'truck', id: 'T001' }),
        location_scan_data: { id: 'LOC-002', type: 'unloading' }
      })
      .expect(400);

    expect(response.body.error_type).toBe('validation');
    expect(response.body.details.type).toBe('workflow_violation');
  });

  it('should return 500 for system errors', async () => {
    // Mock database failure
    jest.spyOn(database, 'query').mockRejectedValue(new Error('Connection failed'));

    const response = await request(app)
      .post('/api/scanner/scan')
      .send(validScanData)
      .expect(500);

    expect(response.body.error_type).toBe('system');
  });
});
```

## Best Practices

### For Developers

1. **Use ValidationError for business rules**: Always throw ValidationError for business rule violations
2. **Provide actionable guidance**: Include clear next steps in error messages
3. **Structure error details**: Use the details object for programmatic error handling
4. **Preserve context**: Include location_data and other relevant context
5. **Test both error types**: Ensure proper handling of validation and system errors

### For Users

1. **Read error messages carefully**: Validation errors provide specific guidance
2. **Follow next steps**: Error messages include actionable steps to resolve issues
3. **Check location types**: Ensure operations match location capabilities
4. **Wait for temporal restrictions**: Respect minimum time gaps between operations
5. **Verify assignments**: Ensure trucks are assigned to the correct locations

## Troubleshooting

### Common Issues

#### ValidationError not being caught as validation error
- Ensure `isValidationError` property is set to `true`
- Check that error instanceof ValidationError returns true
- Verify ValidationError is properly imported

#### Client not showing proper error styling
- Check that `error_type` field is being read correctly
- Verify toast styling classes are applied based on error type
- Ensure error message parsing is working for complex messages

#### Error details not being preserved
- Check that error.details is being passed through in catch blocks
- Verify location_data is being attached to errors when available
- Ensure structured error information is not being lost in serialization

### Debugging

Enable detailed error logging:

```javascript
// Server-side debugging
if (isValidationError) {
  console.log('Validation Error Details:', {
    message: error.message,
    details: error.details,
    stack: error.stack,
    isValidationError: error.isValidationError
  });
}

// Client-side debugging
console.log('Error Response:', {
  status: error.response?.status,
  error_type: error.response?.data?.error_type,
  message: error.response?.data?.message,
  details: error.response?.data?.details
});
```

## Migration Guide

### From Legacy Error Handling

If you have existing error handling code, update it to use the new ValidationError class:

```javascript
// Old way
throw new Error('Invalid truck assignment');

// New way
throw new ValidationError('Invalid truck assignment', {
  field: 'truck_id',
  code: 'INVALID_TRUCK_ASSIGNMENT',
  suggestion: 'Verify the truck number and assignment status'
});
```

### Client-Side Updates

Update error handling to check for `error_type`:

```javascript
// Old way
if (error.response?.status === 400) {
  // Handle validation error
}

// New way
if (error.response?.data?.error_type === 'validation') {
  // Handle validation error with enhanced UI
}
```

## Future Enhancements

- **Error Analytics**: Track validation error patterns for system improvement
- **Localization**: Support for multiple languages in error messages
- **Error Recovery**: Automatic suggestions for resolving common validation errors
- **Progressive Disclosure**: Expandable error details for technical users
- **Error Reporting**: Integration with error tracking services

---

This validation error handling system provides a robust foundation for distinguishing between user-resolvable validation issues and technical system problems, enabling better user experience and more effective troubleshooting.