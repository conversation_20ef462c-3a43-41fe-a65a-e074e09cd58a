/**
 * Unit tests for AutomatedFixService
 * 
 * Tests the automated fix functionality for system health issues
 * including shift, assignment, and trip fixes.
 */

const AutomatedFixService = require('../../services/AutomatedFixService');
const db = require('../../config/database');

// Mock dependencies
jest.mock('../../config/database', () => ({
  query: jest.fn(),
  getClient: jest.fn(),
  pool: {
    query: jest.fn()
  }
}));

describe('AutomatedFixService', () => {
  let fixService;
  let mockClient;
  
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Create mock client
    mockClient = {
      query: jest.fn(),
      release: jest.fn()
    };
    
    // Mock getClient to return our mock client
    db.getClient.mockResolvedValue(mockClient);
    
    // Create instance of service
    fixService = new AutomatedFixService();
  });
  
  describe('fixShiftManagement', () => {
    it('should fix shift management issues', async () => {
      // Mock database responses
      mockClient.query
        .mockResolvedValueOnce({ rows: [] }) // BEGIN
        .mockResolvedValueOnce({ rows: [] }) // schedule_auto_activation()
        .mockResolvedValueOnce({ // Get statistics
          rows: [{
            active_count: 2,
            scheduled_count: 1,
            completed_count: 5,
            total_count: 8
          }]
        })
        .mockResolvedValueOnce({ rows: [] }) // Insert log
        .mockResolvedValueOnce({ rows: [] }); // COMMIT
      
      // Call the method
      const result = await fixService.fixShiftManagement();
      
      // Assertions
      expect(result.success).toBe(true);
      expect(result.affectedRecords.active).toBe(2);
      expect(result.affectedRecords.scheduled).toBe(1);
      expect(result.affectedRecords.completed).toBe(5);
      expect(result.affectedRecords.total).toBe(8);
      
      // Verify database calls
      expect(mockClient.query).toHaveBeenCalledTimes(5);
      expect(mockClient.release).toHaveBeenCalledTimes(1);
    });
    
    it('should handle case with no issues to fix', async () => {
      // Mock database responses
      mockClient.query
        .mockResolvedValueOnce({ rows: [] }) // BEGIN
        .mockResolvedValueOnce({ rows: [] }) // schedule_auto_activation()
        .mockResolvedValueOnce({ // Get statistics
          rows: [{
            active_count: 0,
            scheduled_count: 0,
            completed_count: 0,
            total_count: 0
          }]
        })
        .mockResolvedValueOnce({ rows: [] }) // Insert log
        .mockResolvedValueOnce({ rows: [] }); // COMMIT
      
      // Call the method
      const result = await fixService.fixShiftManagement();
      
      // Assertions
      expect(result.success).toBe(true);
      expect(result.affectedRecords.total).toBe(0);
      
      // Verify database calls
      expect(mockClient.query).toHaveBeenCalledTimes(5);
      expect(mockClient.release).toHaveBeenCalledTimes(1);
    });
    
    it('should handle database errors', async () => {
      // Mock database error
      mockClient.query.mockRejectedValueOnce(new Error('Database connection failed'));
      
      // Call the method and expect it to return error result
      const result = await fixService.fixShiftManagement();
      
      // Assertions
      expect(result.success).toBe(false);
      expect(result.message).toBe('Failed to fix shift management issues');
      expect(result.details).toBe('Database connection failed');
    });
  });
  
  describe('fixAssignmentManagement', () => {
    it('should fix assignment management issues', async () => {
      // Mock database responses
      mockClient.query
        .mockResolvedValueOnce({ rows: [] }) // BEGIN
        .mockResolvedValueOnce({ // Synchronize assignments
          rows: [{ updated_count: 2 }]
        })
        .mockResolvedValueOnce({ rows: [] }) // Insert log
        .mockResolvedValueOnce({ rows: [] }); // COMMIT
      
      // Call the method
      const result = await fixService.fixAssignmentManagement();
      
      // Assertions
      expect(result.success).toBe(true);
      expect(result.affectedRecords.updated_assignments).toBe(2);
      
      // Verify database calls
      expect(mockClient.query).toHaveBeenCalledTimes(4);
      expect(mockClient.release).toHaveBeenCalledTimes(1);
    });
    
    it('should handle case with no issues to fix', async () => {
      // Mock database responses
      mockClient.query
        .mockResolvedValueOnce({ rows: [] }) // BEGIN
        .mockResolvedValueOnce({ // Synchronize assignments
          rows: [{ updated_count: 0 }]
        })
        .mockResolvedValueOnce({ rows: [] }) // Insert log
        .mockResolvedValueOnce({ rows: [] }); // COMMIT
      
      // Call the method
      const result = await fixService.fixAssignmentManagement();
      
      // Assertions
      expect(result.success).toBe(true);
      expect(result.affectedRecords.updated_assignments).toBe(0);
      
      // Verify database calls
      expect(mockClient.query).toHaveBeenCalledTimes(4);
      expect(mockClient.release).toHaveBeenCalledTimes(1);
    });
  });
  
  describe('fixTripMonitoring', () => {
    it('should fix trip workflow issues', async () => {
      // Mock database responses
      mockClient.query
        .mockResolvedValueOnce({ rows: [] }) // BEGIN
        .mockResolvedValueOnce({ // Get "No Active Shift" cases
          rows: [
            { assignment_id: 1, truck_id: 1, truck_number: 'DT-100', assigned_driver: 'John Doe', display_status: '⚠️ No Active Shift' },
            { assignment_id: 2, truck_id: 2, truck_number: 'DT-101', assigned_driver: 'Jane Smith', display_status: '⚠️ No Active Shift' }
          ]
        })
        .mockResolvedValueOnce({ // Find actual shift for first truck
          rows: [{ id: 1, driver_id: 1, shift_type: 'day', status: 'active', driver_name: 'John Doe' }]
        })
        .mockResolvedValueOnce({ // Update assignment for first truck
          rows: [{ id: 1 }]
        })
        .mockResolvedValueOnce({ // Find actual shift for second truck
          rows: [{ id: 2, driver_id: 2, shift_type: 'day', status: 'active', driver_name: 'Jane Smith' }]
        })
        .mockResolvedValueOnce({ // Update assignment for second truck
          rows: [{ id: 2 }]
        })
        .mockResolvedValueOnce({ // Fix invalid trip statuses
          rows: [{ fixed_count: 0 }]
        })
        .mockResolvedValueOnce({ rows: [] }) // Insert log
        .mockResolvedValueOnce({ rows: [] }); // COMMIT
      
      // Call the method
      const result = await fixService.fixTripMonitoring();
      
      // Assertions
      expect(result.success).toBe(true);
      expect(result.affectedRecords.fixed_shift_sync_issues).toBe(2);
      expect(result.affectedRecords.fixed_invalid_statuses).toBe(0);
      expect(result.affectedRecords.no_active_shift_cases_found).toBe(2);
      
      // Verify database calls
      expect(mockClient.query).toHaveBeenCalledTimes(9);
      expect(mockClient.release).toHaveBeenCalledTimes(1);
    });
    
    it('should handle case with no issues to fix', async () => {
      // Mock database responses
      mockClient.query
        .mockResolvedValueOnce({ rows: [] }) // BEGIN
        .mockResolvedValueOnce({ rows: [] }) // Get "No Active Shift" cases - empty
        .mockResolvedValueOnce({ // Fix invalid trip statuses
          rows: [{ fixed_count: 0 }]
        })
        .mockResolvedValueOnce({ rows: [] }) // Insert log
        .mockResolvedValueOnce({ rows: [] }); // COMMIT
      
      // Call the method
      const result = await fixService.fixTripMonitoring();
      
      // Assertions
      expect(result.success).toBe(true);
      expect(result.affectedRecords.fixed_shift_sync_issues).toBe(0);
      expect(result.affectedRecords.fixed_invalid_statuses).toBe(0);
      
      // Verify database calls
      expect(mockClient.query).toHaveBeenCalledTimes(5);
      expect(mockClient.release).toHaveBeenCalledTimes(1);
    });
  });
  

});